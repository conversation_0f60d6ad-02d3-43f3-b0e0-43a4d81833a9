import React, { useEffect, useState } from "react"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { getHistory, getHotOrCold, SVGIcon, useRoundPhase } from "@/hooks"
import { useGameStateStore } from "@/stores/game-state-store"

export const RouletteHistory = React.memo(
  ({ dialogTrigger }: { dialogTrigger?: React.JSX.Element }) => {
    const roundData = useGameStateStore((state) => state.roundData)
    const [arr, setArr] = useState<History[]>([])
    const [hotNumbers, setHotNumbers] = useState<number[]>([])
    const [coldNumbers, setColdNumbers] = useState<number[]>([])
    const { Spinning } = useRoundPhase()

    useEffect(() => {
      if (!roundData) return

      const fetchData = async () => {
        const [history, hot, cold] = await Promise.all([
          getHistory(35),
          getHotOrCold(0),
          getHotOrCold(1),
        ])

        const reversedHistory = history.reverse()
        setArr(reversedHistory)
        setHotNumbers(hot)
        setColdNumbers(cold)
      }

      if (roundData) fetchData()
    }, [roundData])

    const numberTagProps = {
      vertHot: {
        icon: "/images/misc/hot.webp",
        numbers: hotNumbers,
        borderColor: "rgb(251, 191, 36)",
        bgColor:
          "rgba(220, 38, 38, 0.8) 0%, rgba(220, 38, 38, 0.7) 50%, black 100%",
      },
      vertCold: {
        icon: "/images/misc/cold.webp",
        numbers: coldNumbers,
        borderColor: "rgb(251, 191, 36)",
        bgColor:
          "rgba(37, 99, 235, 0.8) 0%, rgba(37, 99, 235, 0.7) 50%, black 100%",
      },
      hot: {
        icon: "/images/misc/hot.webp",
        numbers: hotNumbers,
        borderColor: "rgba(220, 38, 38, 1)",
        bgColor:
          "rgba(220, 38, 38, 0.8) 0%, rgba(220, 38, 38, 0.7) 50%, transparent 100%",
      },
      cold: {
        icon: "/images/misc/cold.webp",
        numbers: coldNumbers,
        borderColor: "rgba(37, 99, 235, 1)",
        bgColor:
          "rgba(37, 99, 235, 0.8) 0%, rgba(37, 99, 235, 0.7) 50%, transparent 100%",
      },
    }

    const DefaultTrigger = () => {
      const isLoading = !arr.length || !hotNumbers.length || !coldNumbers.length

      if (isLoading) {
        return (
          <div className='group relative flex w-auto items-center justify-center'>
            <div className='flex cursor-pointer self-center'>
              <div className='relative flex min-h-12 min-w-24 items-center justify-between gap-6 rounded-l-full border-y border-l animate-pulse border-amber-400 bg-black pl-4 pr-6'></div>
              <div className='-ml-6 flex'>
                {/* Hot numbers skeleton */}
                <div className='relative min-h-12 flex items-center justify-center animate-pulse gap-1 rounded-l-full bg-red-600/20 px-1 pr-7'></div>
                {/* Cold numbers skeleton */}
                <div className='-ml-6 relative flex items-center justify-center gap-1 rounded-l-full bg-blue-600/20 px-1 pr-7 animate-pulse min-h-12'></div>
              </div>
            </div>
            {import.meta.env.VITE_APP_BUILD === "online" && (
              <SVGIcon
                url='src/assets/svgs/chevron-down.svg'
                className='absolute -bottom-4 left-[46%] h-5 w-5 cursor-pointer transition-transform duration-200 ease-in-out group-hover:scale-105'
              />
            )}
          </div>
        )
      }

      return (
        <div className='group relative flex w-auto items-center justify-center'>
          <div className='flex cursor-pointer self-center'>
            <div className='relative flex items-center justify-between gap-6 rounded-l-full border-y border-l border-amber-400 bg-black pl-2 pr-6'>
              <TopCircleList
                showBonus={true}
                numbers={arr.slice(0, sliceEnd)}
              />
            </div>
            <div className='-ml-6 flex'>
              <NumberTag {...numberTagProps.hot} />
              <NumberTag {...numberTagProps.cold} className='-ml-6' />
            </div>
          </div>
          {import.meta.env.VITE_APP_BUILD === "online" && (
            <SVGIcon
              url='src/assets/svgs/chevron-down.svg'
              className='absolute -bottom-4 left-[46%] h-5 w-5 cursor-pointer transition-transform duration-200 ease-in-out group-hover:scale-105'
            />
          )}
        </div>
      )
    }

    const sliceEnd = import.meta.env.VITE_APP_BUILD === "online" ? 8 : 28

    return
  }
)
