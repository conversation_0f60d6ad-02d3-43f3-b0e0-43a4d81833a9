/**
 * Configuration Index
 *
 * This file serves as the central export point for all configuration modules.
 * It ensures proper load order and provides a single import point for all configs.
 *
 * Load order is guaranteed by ES module synchronous imports.
 */

import { soundMap } from "./audio-config"
import { rouletteChips } from "./chip-config"
import { wheelConfig } from "./wheel-config"

// Core game configurations
export { soundMap, type SoundKeys } from "./audio-config"
export { rouletteChips, type Chip } from "./chip-config"
export {
  wheelConfig,
  resultImageConfig,
  getResultingImagePath,
} from "./wheel-config"

// Bonus and selector configurations
export * from "./bonus-config"
export * from "./selector-config"
export * from "./specials-config"

/**
 * Configuration validation and initialization
 * This runs immediately when the module is imported, ensuring configs are valid
 */
const validateConfigurations = () => {
  // Basic validation to ensure critical configs are loaded
  if (!soundMap || Object.keys(soundMap).length === 0) {
    throw new Error("Audio configuration failed to load")
  }

  if (!rouletteChips || rouletteChips.length === 0) {
    throw new Error("Chip configuration failed to load")
  }

  if (!wheelConfig || wheelConfig.length === 0) {
    throw new Error("Wheel configuration failed to load")
  }

  // Log successful configuration loading in development
  if (import.meta.env.DEV) {
    console.log("✅ All configurations loaded successfully", {
      sounds: Object.keys(soundMap).length,
      chips: rouletteChips.length,
      wheelNumbers: wheelConfig.length,
    })
  }
}

// Run validation immediately
validateConfigurations()
