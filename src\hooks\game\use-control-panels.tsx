import { useMemo } from "react"
import type { ControlPanelButton } from "@/features/bottom-navigation/control-panel"
import { useRoundPhase } from "@/hooks"
import { useBettingStore } from "@/stores/betting-store"
import { useGameStateStore } from "@/stores/game-state-store"
import { rouletteChips } from "@/config/chip-config"
import { Voisins, Tiers, Orphalins, Zeros } from "@/config/specials-config"
import type { SelectableCell } from "@/config/selector-config"

/**
 * Hook to provide organized control panel buttons for the game
 * This centralizes the control panel configuration in one place
 */
export const useControlPanels = () => {
  const { handleBetAction, setFavoritesPopoverOpen } = useBettingStore()
  const {
    setGameType,
    gameType,
    setShowPastNumbers,
    showPastNumbers,
    setShowHotCold,
    showHotCold,
  } = useGameStateStore()
  const { Betting } = useRoundPhase()
  const selectedChip = useBettingStore((state) => state.selectedChip)
  const autoplayActive = useBettingStore((state) => state.autoplay.active)

  // Bet options panel buttons
  const betOptionsButtons = useMemo<ControlPanelButton[]>(
    () => [
      {
        id: "half",
        src: "/assets/images/nav-buttons/half button.svg",
        alt: "Half bet",
        onClick: () => handleBetAction("half"),
        disabled: !Betting,
      },
      {
        id: "double",
        src: "/assets/images/nav-buttons/Doubles.svg",
        alt: "Double bet",
        onClick: () => handleBetAction("double"),
        disabled: !Betting,
      },
      {
        id: "undo",
        src: "/assets/images/nav-buttons/Undo button.svg",
        alt: "Undo last bet",
        onClick: () => handleBetAction("undo"),
        disabled: !Betting,
      },
      {
        id: "repeat",
        src: "/assets/images/nav-buttons/Repeat.svg",
        alt: "Repeat last bet",
        onClick: () => handleBetAction("repeat-last"),
        disabled: !Betting,
      },
      {
        id: "autoplay",
        src: "/assets/images/nav-buttons/AutoPlay.svg",
        alt: "Autoplay",
        onClick: () => {
          if (autoplayActive) {
            handleBetAction("stop-autoplay")
          } else {
            handleBetAction("start-autoplay")
          }
        },
        active: autoplayActive,
        disabled: !Betting,
      },
      {
        id: "favourites",
        src: "/assets/images/nav-buttons/Favourites.svg",
        alt: "Favorites",
        onClick: () => setFavoritesPopoverOpen(true),
        disabled: !Betting,
      },
      {
        id: "stats",
        src: "/assets/images/nav-buttons/Statistics.svg",
        alt: "Statistics",
        onClick: () => {
          // Toggle statistics view
          // This would typically open statistics view
        },
      },
      {
        id: "hot-cold",
        src: "/assets/images/nav-buttons/HC.svg",
        alt: "Hot and Cold Numbers",
        onClick: () => {
          setShowHotCold(!showHotCold)
        },
      },
      {
        id: "past-numbers",
        src: "/assets/images/nav-buttons/PN.svg",
        alt: "Past Numbers",
        onClick: () => {
          setShowPastNumbers(!showPastNumbers)
        },
      },
    ],
    [handleBetAction, Betting, autoplayActive, showHotCold, showPastNumbers]
  )

  // Chips panel buttons - using chips from config
  const chipsButtons = useMemo<ControlPanelButton[]>(
    () =>
      rouletteChips.map((chip) => ({
        id: `chip-${chip.value}`,
        src: chip.src,
        alt: `${chip.value} Credits`,
        onClick: () => {
          useBettingStore.getState().setSelectedChip(chip)
        },
        value: chip.value,
        isChip: true,
        active: selectedChip?.value === chip.value,
        disabled: !Betting,
      })),
    [selectedChip, Betting]
  )

  // Helper function to place special bets
  const placeSpecialBet = (specialBetCells: SelectableCell[]) => {
    if (!Betting || !selectedChip) return

    // Place bets on all cells in the special bet (similar to handleSpecialButtonClick)
    const addChip = useBettingStore.getState().addChip
    specialBetCells.forEach((cell) => {
      addChip(cell, selectedChip)
    })
  }

  // Controls panel buttons
  const controlsButtons = useMemo<ControlPanelButton[]>(
    () => [
      {
        id: "specials",
        src: "/assets/images/nav-buttons/Specials.svg",
        alt: "Special Bets",
        onClick: () => {
          // Toggle between normal and special game types
          setGameType(gameType === "special" ? "normal" : "special")
        },
        aspect: "freeform",
        active: gameType === "special",
      },
      // Special bet buttons
      {
        id: "big-series",
        src: "/assets/images/nav-buttons/Big Series.svg",
        alt: "Big Series",
        onClick: () => placeSpecialBet(Voisins),
        disabled: !Betting,
      },
      {
        id: "small-series",
        src: "/assets/images/nav-buttons/Small Series.svg",
        alt: "Small Series",
        onClick: () => placeSpecialBet(Tiers),
        disabled: !Betting,
      },
      {
        id: "orphans",
        src: "/assets/images/nav-buttons/Orphans.svg",
        alt: "Orphans",
        onClick: () => placeSpecialBet(Orphalins),
        disabled: !Betting,
      },
      {
        id: "zero-spiel",
        src: "/assets/images/nav-buttons/Zero Spiel.svg",
        alt: "Zero Spiel",
        onClick: () => placeSpecialBet(Zeros),
        disabled: !Betting,
      },
      {
        id: "black-split",
        src: "/assets/images/nav-buttons/Black Split.svg",
        alt: "Black Split",
        onClick: () => {
          // Place bets on black splits
          handleBetAction("black-split")
        },
        disabled: !Betting,
      },
      {
        id: "red-split",
        src: "/assets/images/nav-buttons/Red Split.svg",
        alt: "Red Split",
        onClick: () => {
          // Place bets on red splits
          handleBetAction("red-split")
        },
        disabled: !Betting,
      },
    ],
    [
      handleBetAction,
      gameType,
      setGameType,
      Betting,
      selectedChip,
      placeSpecialBet,
    ]
  )

  return {
    betOptionsButtons,
    chipsButtons,
    controlsButtons,
  }
}
