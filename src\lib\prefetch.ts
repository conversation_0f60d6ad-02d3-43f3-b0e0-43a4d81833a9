import { logger } from '@/middleware'

/**
 * PrefetchService
 * 
 * A service for prefetching JavaScript modules to improve loading performance.
 * This service handles different prefetching strategies and priorities.
 */
class PrefetchService {
  private prefetched = new Set<string>()
  private prefetchQueue: Array<{ path: string; priority: string }> = []
  private isProcessingQueue = false

  /**
   * Prefetch a module
   * 
   * @param path - The path to the module to prefetch
   * @param options - Prefetch options
   */
  prefetch(
    path: string,
    options: {
      priority?: 'high' | 'medium' | 'low'
      strategy?: 'eager' | 'idle' | 'visible' | 'hover'
    } = {}
  ): void {
    const { priority = 'medium', strategy = 'eager' } = options

    // Skip if already prefetched or queued
    if (this.prefetched.has(path)) {
      return
    }

    // Add to queue
    this.prefetchQueue.push({ path, priority })

    // Start processing queue if not already processing
    if (!this.isProcessingQueue) {
      this.processQueue()
    }
  }

  /**
   * Process the prefetch queue
   */
  private processQueue(): void {
    if (this.prefetchQueue.length === 0) {
      this.isProcessingQueue = false
      return
    }

    this.isProcessingQueue = true

    // Sort queue by priority
    this.prefetchQueue.sort((a, b) => {
      const priorityMap = { high: 0, medium: 1, low: 2 }
      return priorityMap[a.priority as keyof typeof priorityMap] - priorityMap[b.priority as keyof typeof priorityMap]
    })

    // Get next item
    const next = this.prefetchQueue.shift()
    if (!next) {
      this.processQueue()
      return
    }

    const { path, priority } = next

    // Execute prefetch based on priority
    const prefetchFn = () => {
      // Mark as prefetched before import to prevent duplicate prefetching
      this.prefetched.add(path)

      // Dynamic import
      import(/* @vite-ignore */ path)
        .then(() => {
          logger.debug(`Prefetched: ${path}`, { context: 'Prefetch' })
          this.processQueue()
        })
        .catch(error => {
          logger.error(`Failed to prefetch: ${path}`, error, { context: 'Prefetch' })
          this.prefetched.delete(path) // Remove from prefetched set on error
          this.processQueue()
        })
    }

    switch (priority) {
      case 'high':
        // Execute immediately
        prefetchFn()
        break
      case 'medium':
        // Execute during idle time or with a small delay
        if ('requestIdleCallback' in window) {
          window.requestIdleCallback(() => prefetchFn())
        } else {
          setTimeout(prefetchFn, 300)
        }
        break
      case 'low':
        // Execute with a longer delay
        setTimeout(prefetchFn, 1000)
        break
    }
  }

  /**
   * Prefetch modules based on visibility
   * 
   * @param element - The element to observe
   * @param path - The path to the module to prefetch
   * @param options - Prefetch options
   */
  prefetchOnVisible(
    element: Element,
    path: string,
    options: {
      priority?: 'high' | 'medium' | 'low'
      rootMargin?: string
    } = {}
  ): () => void {
    const { priority = 'medium', rootMargin = '200px' } = options

    // Skip if already prefetched
    if (this.prefetched.has(path)) {
      return () => {}
    }

    // Create observer
    if ('IntersectionObserver' in window) {
      const observer = new IntersectionObserver(
        entries => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              this.prefetch(path, { priority })
              observer.disconnect()
            }
          })
        },
        { rootMargin }
      )

      // Start observing
      observer.observe(element)

      // Return cleanup function
      return () => observer.disconnect()
    }

    // Fallback for browsers without IntersectionObserver
    this.prefetch(path, { priority: 'low' })
    return () => {}
  }

  /**
   * Clear the prefetch cache
   */
  clearCache(): void {
    this.prefetched.clear()
  }
}

// Export singleton instance
export const prefetchService = new PrefetchService()

export default prefetchService
