import { createContext, useContext, useState, type ReactNode } from "react"

export type ControlPanelTheme = "default" | "dark" | "light" | "colorful"

interface ThemeStyles {
  borderColor: string
  backgroundColor: string
  activeColor: string
  hoverColor: string
}

export const themeStyles: Record<ControlPanelTheme, ThemeStyles> = {
  default: {
    borderColor: "#c69e61",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    activeColor: "#FFD700",
    hoverColor: "#E0E0E0",
  },
  dark: {
    borderColor: "#2196F3",
    backgroundColor: "rgba(20, 20, 20, 0.9)",
    activeColor: "#64B5F6",
    hoverColor: "#455A64",
  },
  light: {
    borderColor: "#9E9E9E",
    backgroundColor: "rgba(245, 245, 245, 0.9)",
    activeColor: "#FFC107",
    hoverColor: "#BDBDBD",
  },
  colorful: {
    borderColor: "#E91E63",
    backgroundColor: "rgba(25, 25, 25, 0.8)",
    activeColor: "#FF4081",
    hoverColor: "#7B1FA2",
  },
}

interface ControlPanelThemeContextType {
  theme: ControlPanelTheme
  setTheme: (theme: ControlPanelTheme) => void
  styles: ThemeStyles
}

const ControlPanelThemeContext = createContext<ControlPanelThemeContextType>({
  theme: "default",
  setTheme: () => {},
  styles: themeStyles.default,
})

export function useControlPanelTheme() {
  return useContext(ControlPanelThemeContext)
}

interface ControlPanelThemeProviderProps {
  children: ReactNode
  initialTheme?: ControlPanelTheme
}

export function ControlPanelThemeProvider({ children, initialTheme = "default" }: ControlPanelThemeProviderProps) {
  const [theme, setTheme] = useState<ControlPanelTheme>(initialTheme)

  const value = {
    theme,
    setTheme,
    styles: themeStyles[theme],
  }

  return <ControlPanelThemeContext.Provider value={value}>{children}</ControlPanelThemeContext.Provider>
}
