import { User } from "lucide-react"
import { useEffect, useRef, useState } from "react"
import Divider from "@/components/ui/Divider"
import { ScrollFade } from "@/components/ui/scroll-fade"
import { logger } from "@/middleware"

/**
 * Interface for winner data
 */
interface Winner {
  username: string
  amount: number
}

const STYLES = {
  // Main container
  mainContainer: "flex flex-col h-full",

  // Header section
  headerContainer: "flex items-center gap-1 justify-center p-2",
  totalWinnersText: "text-xl font-bold text-white",
  userIcon: "h-6 w-6 text-white",
  wonText: "text-xl text-white uppercase font-semibold",
  totalAmountText: "text-xl text-[#C69E61] font-bold",

  // Scrollable section
  scrollContainer: "space-y-2 mx-auto h-0 flex-grow overflow-hidden",
  scrollContentPadding: "space-y-1",

  // Winner item
  winnerItem: "flex justify-between items-center gap-3",

  // Text styling for amounts
  topWinnerText: "font-bold text-[#C69E61] text-lg",
  regularWinnerText: "font-bold text-white text-lg",

  // Text styling for usernames
  topWinnerUsername: "text-[#C69E61] font-light text-lg",
  regularWinnerUsername: "text-white font-light text-lg"
}

export const formatCurrency = (amount: number) => {
    return `R${amount.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ",")}`
  }

/**
 * Component to display recent winners and their amounts
 */
const WinnersPanel = () => {
  // Number of top winners to highlight with gold text
  const TOP_WINNERS_COUNT = 4 

  // Ref for the scrollable container
  const scrollContainerRef = useRef<HTMLDivElement>(null)

  // Interval reference for scrolling
  const scrollIntervalRef = useRef<number | null>(null)

  // State to track if scrolling is active
  const [isScrolling, setIsScrolling] = useState(true)

  // Scroll settings
  const SCROLL_SPEED = 0.5 // pixels per interval
  const SCROLL_INTERVAL = 16 // milliseconds (roughly 60fps)
  const PAUSE_DURATION = 2000 // milliseconds to pause at top and bottom

  // Dummy data array that can be replaced with real data later
  const unsortedWinners: Winner[] = [
    { username: "User123", amount: 2500 },
    { username: "WinnerPro", amount: 1800 },
    { username: "LuckyPlayer", amount: 3200 },
    { username: "RouletteKing", amount: 1500 },
    { username: "GoldRusher", amount: 2100 },
    { username: "SpinMaster", amount: 4200 },
    { username: "LuckyCharm", amount: 2800 },
    { username: "BigWinner", amount: 3600 },
    { username: "FortuneSeeker", amount: 2900 },
    { username: "RouletteQueen", amount: 2300 },
    { username: "RouletteWizard", amount: 1900 },
    { username: "RouletteChamp", amount: 2700 },
    { username: "RouletteAce", amount: 2400 },
    { username: "RouletteGuru", amount: 2600 },
    { username: "RoulettePro", amount: 2200 },
    { username: "RouletteExpert", amount: 2000 },
    { username: "RouletteMaestro", amount: 2100 },
    { username: "RouletteGenius", amount: 1700 },
    { username: "RouletteWizard", amount: 1600 },
    { username: "RouletteMaster", amount: 1800 },
    { username: "RouletteAce", amount: 1900 },
    { username: "RouletteGuru", amount: 2000 },
    { username: "RoulettePro", amount: 2100 },
    { username: "RouletteExpert", amount: 2200 },
    { username: "RouletteMaestro", amount: 2300 },
    { username: "RouletteGenius", amount: 2400 }
  ]

  // Sort winners by amount in descending order
  const winners = [...unsortedWinners].sort((a, b) => b.amount - a.amount)

  // Calculate summary statistics
  const totalWinners = winners.length
  const totalAmount = winners.reduce((sum, winner) => sum + winner.amount, 0)

  // Auto-scrolling implementation
  useEffect(() => {
    const container = scrollContainerRef.current
    if (!container || !isScrolling) return

    // Make sure there's enough content to scroll
    if (container.scrollHeight <= container.clientHeight) {
      logger.debug('Not enough content to scroll', {
        context: 'WinnersPanel',
        data: { scrollHeight: container.scrollHeight, clientHeight: container.clientHeight }
      })
      return
    }

    // Log that scrolling is starting
    logger.debug('Starting auto-scroll animation', {
      context: 'WinnersPanel',
      data: { scrollHeight: container.scrollHeight, clientHeight: container.clientHeight }
    })

    // Function to handle the scrolling
    const performScroll = () => {
      if (!container) return

      // Calculate new scroll position
      const currentPosition = container.scrollTop
      const maxScroll = container.scrollHeight - container.clientHeight

      // If we're near the bottom, reset to top
      if (currentPosition >= maxScroll - 2) {
        // Use a small timeout to create a brief pause at the bottom
        // before resetting to the top
        clearInterval(scrollIntervalRef.current!)
        scrollIntervalRef.current = null

        setTimeout(() => {
          if (container) {
            // Reset to top
            container.scrollTop = 0

            // Restart scrolling after a brief pause
            setTimeout(() => {
              if (isScrolling) {
                scrollIntervalRef.current = window.setInterval(performScroll, SCROLL_INTERVAL)
              }
            }, PAUSE_DURATION) // Pause at top
          }
        }, PAUSE_DURATION) // Pause at bottom
      } else {
        // Apply the scroll
        container.scrollTop = currentPosition + SCROLL_SPEED
      }
    }

    // Start the interval-based scrolling
    scrollIntervalRef.current = window.setInterval(performScroll, SCROLL_INTERVAL)

    // Clean up on unmount
    return () => {
      if (scrollIntervalRef.current !== null) {
        clearInterval(scrollIntervalRef.current)
        scrollIntervalRef.current = null
      }
    }
  }, [isScrolling])

  // Pause scrolling on hover
  const handleMouseEnter = () => setIsScrolling(false)
  const handleMouseLeave = () => setIsScrolling(true)

  return (
    <div className={STYLES.mainContainer}>
      {/* Winner header and amounts */}
      <div className={STYLES.headerContainer}>
        <span className={STYLES.totalWinnersText}>{totalWinners}</span>
        <User className={STYLES.userIcon} />
        <span className={STYLES.wonText}>won</span>
        <span className={STYLES.totalAmountText}>{formatCurrency(totalAmount)}</span>
      </div>

      <Divider />

      {/* Scrollable winners list with fade effect */}
      <ScrollFade direction="y-both" size="sm" className={STYLES.scrollContainer}>
        <div
          ref={scrollContainerRef}
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
          className="overflow-hidden h-full "
          style={{ scrollBehavior: 'smooth' }}
        >
          {/* Extra padding at top and bottom to create smooth looping effect */}
          <div className={STYLES.scrollContentPadding}>
          {winners.map((winner, index) => (
            <div key={index} className={STYLES.winnerItem}>
              <span
              className={index < TOP_WINNERS_COUNT ? STYLES.topWinnerText : STYLES.regularWinnerText}
              >{formatCurrency(winner.amount)}</span>
              <span
                className={index < TOP_WINNERS_COUNT ? STYLES.topWinnerUsername : STYLES.regularWinnerUsername}
              >
                {winner.username}
              </span>
            </div>
          ))}

          {/* Duplicate the first few items at the end for seamless looping */}
          {winners.slice(0, 3).map((winner, index) => (
            <div key={`duplicate-${index}`} className={STYLES.winnerItem}>
              <span className={index < TOP_WINNERS_COUNT ? STYLES.topWinnerText : STYLES.regularWinnerText}
              >{formatCurrency(winner.amount)}</span>
              <span
                className={index < TOP_WINNERS_COUNT ? STYLES.topWinnerUsername : STYLES.regularWinnerUsername}
              >
                {winner.username}
              </span>
            </div>
          ))}
          </div>
        </div>
      </ScrollFade>

      <Divider />

      {/* Note: scrollbar hiding is handled via inline styles and className */}
    </div>
  )
}

export default WinnersPanel
