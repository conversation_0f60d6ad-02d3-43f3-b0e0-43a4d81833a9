# Middleware Architecture

This folder contains centralized middleware components that abstract common functionality across the application.

## Overview

The middleware architecture is designed to:

1. Centralize common functionality
2. Reduce code duplication
3. Provide consistent patterns for common tasks
4. Improve maintainability and testability

## Middleware Categories

### API Middleware

Located in `src/middleware/api/`

- **apiClient.ts**: Centralized API client with error handling and retries
- **authMiddleware.ts**: Authentication utilities for API requests
- **queryProvider.tsx**: React Query provider for data fetching
- **useApiQuery.ts**: Custom hooks for API data fetching with React Query

### Logging Middleware

Located in `src/middleware/logging/`

- **logger.ts**: Centralized logging utilities with context and formatting

### Storage Middleware

Located in `src/middleware/storage/`

- **persistenceMiddleware.ts**: Secure storage utilities for local data
- **stateMiddleware.ts**: Enhanced state management with Zustand and Immer

### Performance Middleware

Located in `src/middleware/performance/`

- **monitoring.ts**: Performance monitoring utilities

### UI Middleware

Located in `src/middleware/ui/`

- **toastMiddleware.ts**: Centralized toast notification service
- **errorBoundary.tsx**: Enhanced error boundary component
- **formMiddleware.ts**: Form validation with React Hook Form and Zod
- **virtualListMiddleware.tsx**: Virtualized list component for performance
- **dateMiddleware.ts**: Date formatting utilities

## Usage Examples

### API Client

```typescript
import { apiClient, authMiddleware } from '@/middleware';

// Get data with authentication
const fetchUserData = async (userId: string, token: string) => {
  const headers = authMiddleware.getAuthHeader(token);
  const [data, error] = await apiClient.get(`/users/${userId}`, null, headers);
  
  if (error) {
    // Handle error
    return null;
  }
  
  return data;
};
```

### React Query

```typescript
import { useApiQuery, useApiMutation } from '@/middleware';

// In a component
const { data, isLoading, error } = useApiQuery(
  ['users', userId],
  `/users/${userId}`,
  null,
  authHeaders,
  { 
    enabled: !!userId,
    showErrorToast: true
  }
);

// Mutation example
const { mutate, isLoading } = useApiMutation(
  ['updateUser', userId],
  `/users/${userId}`,
  'put',
  authHeaders,
  {
    showSuccessToast: true,
    successToastTitle: 'Success',
    successToastMessage: 'User updated successfully'
  }
);
```

### Logging

```typescript
import { logger } from '@/middleware';

// Log with context
logger.info('User logged in', { context: 'Auth', data: { userId } });
logger.error('Failed to process payment', error, { context: 'Payment' });
```

### Toast Notifications

```typescript
import { toastService } from '@/middleware';

// Show different types of toasts
toastService.success('Success', 'Operation completed successfully');
toastService.error('Error', 'Failed to complete operation');
toastService.warning('Warning', 'This action cannot be undone');
toastService.info('Info', 'Your session will expire in 5 minutes');
```

### Enhanced Error Boundary

```tsx
import { EnhancedErrorBoundary } from '@/middleware';

// Wrap components with error boundary
const MyComponent = () => (
  <EnhancedErrorBoundary context="UserProfile">
    <UserProfileContent />
  </EnhancedErrorBoundary>
);
```

### Form Validation

```tsx
import { useValidatedForm, createFormSchema } from '@/middleware';
import { z } from 'zod';

// Create a form schema
const loginSchema = z.object({
  email: createFormSchema.email(),
  password: createFormSchema.password(6)
});

// Use in a component
const LoginForm = () => {
  const { register, handleSubmit, formState: { errors } } = useValidatedForm({
    schema: loginSchema
  });
  
  const onSubmit = (data) => {
    // Handle form submission
  };
  
  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      {/* Form fields */}
    </form>
  );
};
```

### Virtualized Lists

```tsx
import { VirtualizedList } from '@/middleware';

// Use in a component
const UserList = ({ users }) => (
  <VirtualizedList
    items={users}
    height={500}
    width="100%"
    itemSize={80}
    renderItem={(user, index, style) => (
      <div style={style} key={user.id}>
        {user.name}
      </div>
    )}
  />
);
```

### Date Formatting

```tsx
import { dateUtils, DATE_FORMATS } from '@/middleware';

// Format dates
const formattedDate = dateUtils.formatDate(new Date(), DATE_FORMATS.MEDIUM_DATE);
const relativeTime = dateUtils.formatRelativeTime(timestamp);
```

### Enhanced State Management

```typescript
import { createEnhancedStore, createSelector } from '@/middleware';

// Create a store with persistence and immer
interface UserStore {
  user: User | null;
  isLoggedIn: boolean;
  setUser: (user: User | null) => void;
  logout: () => void;
}

export const useUserStore = createEnhancedStore<UserStore>(
  (set) => ({
    user: null,
    isLoggedIn: false,
    setUser: (user) => set((state) => {
      state.user = user;
      state.isLoggedIn = !!user;
    }),
    logout: () => set((state) => {
      state.user = null;
      state.isLoggedIn = false;
    })
  }),
  {
    name: 'user-store',
    persist: true,
    secure: true,
    immer: true
  }
);

// Create selectors
export const useUser = createSelector(useUserStore, (state) => state.user);
export const useIsLoggedIn = createSelector(useUserStore, (state) => state.isLoggedIn);
```

### Performance Monitoring

```typescript
import { performanceMonitor } from '@/middleware';

// Measure function execution time
const loadData = async () => {
  return performanceMonitor.measure('loadData', async () => {
    // Expensive operation
    const data = await fetchLargeDataset();
    return processData(data);
  });
};

// Manual measurement
const measureId = performanceMonitor.startMeasure('renderComponent', { componentName: 'UserList' });
// ... operations
performanceMonitor.endMeasure(measureId);
```
