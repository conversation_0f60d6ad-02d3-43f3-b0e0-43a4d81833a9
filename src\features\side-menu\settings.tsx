import { useMemo } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { <PERSON>lider } from "@/components/ui/slider"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { SVGIcon } from "@/hooks"
import { useSettingsStore, VideoQuality } from "@/stores/settings-store"

export const Settings = () => {
  const masterVolume = useSettingsStore((state) => state.masterVolume)
  const sfxVolume = useSettingsStore((state) => state.sfxVolume)
  const musicVolume = useSettingsStore((state) => state.musicVolume)
  const setMasterVolume = useSettingsStore((state) => state.setMasterVolume)
  const setSfxVolume = useSettingsStore((state) => state.setSfxVolume)
  const setMusicVolume = useSettingsStore((state) => state.setMusicVolume)
  const videoQuality = useSettingsStore((state) => state.videoQuality)
  const setVideoQuality = useSettingsStore((state) => state.setVideoQuality)
  const dealerVoice = useSettingsStore((state) => state.dealerVoice)
  const setDealerVoice = useSettingsStore((state) => state.setDealerVoice)
  const isMuted = useSettingsStore((state) => state.isMuted)
  const setIsMuted = useSettingsStore((state) => state.setIsMuted)
  const isVideoDisplayed = useSettingsStore((state) => state.isVideoDisplayed)
  const setIsVideoDisplayed = useSettingsStore(
    (state) => state.setIsVideoDisplayed
  )

  const qualityOptions = [
    "auto",
    "1080p",
    "720p",
    "480p",
    "360p",
  ] as VideoQuality[]

  const VolumeSlider = useMemo(
    () =>
      ({ label, icon, value, onChange, disabled = false }) =>
        (
          <div className='space-y-2'>
            <div className='flex items-center justify-between'>
              <div className='flex items-center gap-2'>
                {icon}
                <Label className='text-xs text-gray-400'>{label}</Label>
              </div>
              <span className='text-xs text-gray-400'>
                {Math.round(value * 100)}%
              </span>
            </div>
            <Slider
              value={[value * 100]}
              onValueChange={(newValue) => onChange(newValue[0] / 100)}
              max={100}
              disabled={disabled}
              className='[&_[role=slider]]:bg-amber-400'
            />
          </div>
        ),
    []
  )

  return (
    <Card className='w-full'>
      <CardContent className='p-0'>
        <Tabs defaultValue='video' className='w-full'>
          <TabsList className='grid w-full grid-cols-2 gap-2 bg-transparent'>
            <TabsTrigger
              value='video'
              className='rounded-lg font-bold data-[state=active]:bg-amber-400 data-[state=active]:text-black'
            >
              <SVGIcon url='/assets/svgs/video.svg' className='mr-2 h-4 w-4' />
              VIDEO
            </TabsTrigger>
            <TabsTrigger
              value='sound'
              className='rounded-lg font-bold data-[state=active]:bg-amber-400 data-[state=active]:text-black'
            >
              <SVGIcon url='/assets/svgs/sound.svg' className='mr-2 h-4 w-4' />
              SOUND
            </TabsTrigger>
          </TabsList>

          <TabsContent value='video' className='mt-6 space-y-6'>
            <div className='space-y-2'>
              <Button
                onClick={() => setIsVideoDisplayed(!isVideoDisplayed)}
                variant={isVideoDisplayed ? "primary" : "selected"}
                className='w-full justify-between rounded-lg px-6 py-2'
              >
                <span>Enable Video</span>
                <span>{isVideoDisplayed ? "ON" : "OFF"}</span>
              </Button>

              <div className='space-y-2'>
                <Label className='text-gray-400'>Video Quality</Label>
                <div className='mb-2 flex items-center justify-between text-xs text-gray-400'>
                  {qualityOptions.map((quality) => (
                    <span key={quality} className='text-center'>
                      {quality === "auto" ? "Auto" : quality}
                    </span>
                  ))}
                </div>
                <Slider
                  value={[
                    qualityOptions.indexOf(videoQuality) *
                      (100 / (qualityOptions.length - 1)),
                  ]}
                  onValueChange={(values) => {
                    const index = Math.round(
                      (values[0] / 100) * (qualityOptions.length - 1)
                    )
                    setVideoQuality(qualityOptions[index])
                  }}
                  max={100}
                  step={25}
                  disabled={!isVideoDisplayed}
                  className='[&_[role=slider]]:bg-amber-400'
                />
              </div>
            </div>
          </TabsContent>

          <TabsContent value='sound' className='mt-6 space-y-6'>
            <div className='space-y-4'>
              <Button
                onClick={() => setIsMuted(!isMuted)}
                variant={isMuted ? "primary" : "selected"}
                className='mb-2 w-full justify-between rounded-lg px-6 py-2'
              >
                <span>Mute All</span>
                <span>{isMuted ? "ON" : "OFF"}</span>
              </Button>

              <VolumeSlider
                label='MASTER VOLUME'
                icon={
                  <SVGIcon
                    url='/assets/svgs/volume-2.svg'
                    className='h-4 w-4 text-gray-400'
                  />
                }
                value={masterVolume}
                onChange={setMasterVolume}
                disabled={isMuted}
              />

              <VolumeSlider
                label='SFX VOLUME'
                icon={
                  <SVGIcon
                    url='/assets/svgs/volume-2.svg'
                    className='h-4 w-4 text-gray-400'
                  />
                }
                value={sfxVolume}
                onChange={setSfxVolume}
                disabled={isMuted}
              />

              <VolumeSlider
                label='MUSIC VOLUME'
                icon={
                  <SVGIcon
                    url='/assets/svgs/music.svg'
                    className='h-4 w-4 text-gray-400'
                  />
                }
                value={musicVolume}
                onChange={setMusicVolume}
                disabled={isMuted}
              />

              <div className='space-y-2'>
                <div className='flex items-center gap-2'>
                  <SVGIcon
                    url='/assets/svgs/mic.svg'
                    className='h-4 w-4 text-gray-400'
                  />
                  <Label className='text-xs text-gray-400'>
                    DEALER'S VOICE
                  </Label>
                </div>
                <RadioGroup
                  value={dealerVoice}
                  onValueChange={setDealerVoice}
                  className='flex flex-wrap gap-4'
                >
                  <div className='flex items-center space-x-2'>
                    <RadioGroupItem
                      value='RSA'
                      id='rsa'
                      className='border-amber-400 text-amber-400'
                    />
                    <Label htmlFor='rsa'>RSA</Label>
                  </div>
                  <div className='flex items-center space-x-2'>
                    <RadioGroupItem
                      value='EU'
                      id='eu'
                      className='border-amber-400 text-amber-400'
                    />
                    <Label htmlFor='eu'>EU</Label>
                  </div>
                  <div className='flex items-center space-x-2'>
                    <RadioGroupItem
                      value='ZULU'
                      id='zulu'
                      className='border-amber-400 text-amber-400'
                    />
                    <Label htmlFor='zulu'>isiZulu</Label>
                  </div>
                  <div className='flex items-center space-x-2'>
                    <RadioGroupItem
                      value='XHOSA'
                      id='xhosa'
                      className='border-amber-400 text-amber-400'
                    />
                    <Label htmlFor='xhosa'>isiXhosa</Label>
                  </div>
                </RadioGroup>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
