import { useEffect, useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { SVGIcon } from "@/hooks/ui/svg-icon"

export default function ErrorPage({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    setIsVisible(true)
  }, [])

  return (
    <main className='flex min-h-[100svh] items-center justify-center bg-black p-4'>
      <Card
        className={`w-full max-w-2xl border-2 border-amber-400 bg-zinc-950 shadow-lg transition-all duration-500 ease-out ${
          isVisible ? "translate-y-0 opacity-100" : "translate-y-4 opacity-0"
        }`}
      >
        <CardHeader className='space-y-1'>
          <div className='mb-4 flex items-center justify-center'>
            <SVGIcon
              url='/assets/svgs/circle-alert.svg'
              className='aspect-square h-auto w-20 animate-pulse text-destructive'
            />
          </div>
          <CardTitle className='text-center text-3xl font-bold'>
            Oops! Something went wrong
          </CardTitle>
          <CardDescription className='text-center'>
            We apologize for the inconvenience. An unexpected error has
            occurred.
          </CardDescription>
        </CardHeader>
        <CardContent className='space-y-4'>
          {error.digest && (
            <p className='text-center text-xs text-muted-foreground'>
              Error ID: {error.digest}
            </p>
          )}
          <div className='flex justify-center'>
            <Button onClick={reset} variant='outline'>
              <SVGIcon
                url='/assets/svgs/rotate-cw.svg'
                className='mr-2 h-4 w-4'
              />
              Try again
            </Button>
          </div>
          <Card className='bg-zinc-850 p-4'>
            <CardHeader className='p-0'>
              <CardTitle className='text-lg leading-tight'>
                Possible reasons for this error:
              </CardTitle>
            </CardHeader>
            <CardContent className='p-0'>
              <ul className='list-disc space-y-1 pl-6'>
                <li>Temporary server issue</li>
                <li>Network connectivity problems</li>
                <li>Outdated browser cache</li>
                <li>Recent site updates</li>
              </ul>
            </CardContent>
          </Card>
        </CardContent>
      </Card>
    </main>
  )
}
