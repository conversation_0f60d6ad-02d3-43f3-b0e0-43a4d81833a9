import { useEffect } from "react"
import { fetchCurrencyCode } from "@/hooks"
import { formatNumber } from "@/lib/formatting"
import { useAuthStore } from "@/stores/auth-store"
import { useGameStateStore } from "@/stores/game-state-store"

export function MinMaxBet({ min, max }: { min: number; max: number }) {
  const token = useAuthStore((state) => state.token)
  const { currencyCode, setCurrencyCode } = useGameStateStore()

  useEffect(() => {
    const fetchCurrency = async () => {
      const currencyCode = await fetchCurrencyCode(token)
      if (currencyCode) {
        setCurrencyCode(currencyCode)
      } else {
        setCurrencyCode("FUN")
      }
    }

    fetchCurrency()
  }, [token])

  return (
    <section className=' flex flex-col items-center justify-center rounded-lg px-1 bg-black/30 border border-amber-500 '>
      {[
        { label: "Min", value: min },
        { label: "Max", value: max },
      ].map(({ label, value }, idx) => (
        <div key={idx} className={`flex items-center text-xs gap-2`}>
          <span className='font-bold text-xs'>{label}:</span> {currencyCode}{" "}
          {formatNumber(value)}
        </div>
      ))}
    </section>
  )
}
