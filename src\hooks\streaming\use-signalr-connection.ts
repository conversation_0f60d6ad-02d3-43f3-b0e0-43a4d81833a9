import { HubConnection, HubConnectionBuilder } from "@microsoft/signalr"
import { useCallback, useEffect, useRef, useState } from "react"
import { useGameStateStore } from "@/stores/game-state-store"
import { RoundChangeData, StreamSyncData } from "./types"

/**
 * Hook for managing SignalR connection to the roulette hub
 */
export const useSignalRConnection = () => {
  const isSessionActive = useGameStateStore((state) => state.isSessionActive)
  const connectionUrl = `${import.meta.env.VITE_APP_API_URL}rouletteHub`

  const hubConnectionRef = useRef<HubConnection | null>(null)
  const isMountedRef = useRef<boolean>(true)
  const [isConnected, setIsConnected] = useState(false)

  const disconnectSignalR = useCallback(async () => {
    if (hubConnectionRef.current) {
      await hubConnectionRef.current.stop()
      hubConnectionRef.current = null
      setIsConnected(false)
    }
  }, [])

  const setupSignalR = useCallback(async () => {
    if (
      hubConnectionRef.current &&
      (hubConnectionRef.current.state === "Connected" ||
        hubConnectionRef.current.state === "Connecting")
    ) {
      return hubConnectionRef.current
    }

    await disconnectSignalR()

    const hubConnection = new HubConnectionBuilder()
      .withUrl(connectionUrl)
      .withAutomaticReconnect({
        nextRetryDelayInMilliseconds: (retryContext) => {
          if (!isSessionActive) return null
          return Math.min(
            1000 * Math.pow(1.5, retryContext.previousRetryCount),
            30000
          )
        },
      })
      .build()

    hubConnectionRef.current = hubConnection

    hubConnection.onclose(() => {
      setIsConnected(false)
    })

    hubConnection.onreconnected(async () => {
      setIsConnected(true)
      if (isMountedRef.current) {
        await hubConnection.invoke("RequestCurrentRound").catch(console.error)
      }
    })

    return hubConnection
  }, [connectionUrl, isSessionActive, disconnectSignalR])

  const registerEventHandlers = useCallback(
    (
      onRoundChange: (data: RoundChangeData) => void,
      onStreamSync: (data: StreamSyncData) => void
    ) => {
      if (!hubConnectionRef.current) return

      hubConnectionRef.current.on("RoundChange", onRoundChange)
      hubConnectionRef.current.on("StreamSync", onStreamSync)

      return () => {
        if (hubConnectionRef.current) {
          hubConnectionRef.current.off("RoundChange")
          hubConnectionRef.current.off("StreamSync")
        }
      }
    },
    []
  )

  useEffect(() => {
    isMountedRef.current = true

    return () => {
      isMountedRef.current = false
      disconnectSignalR()
    }
  }, [disconnectSignalR])

  return {
    hubConnectionRef,
    isConnected,
    setIsConnected,
    setupSignalR,
    disconnectSignalR,
    isMountedRef,
    registerEventHandlers,
  }
}
