import { ChevronDown } from 'lucide-react'
import React from 'react'
import Divider from '@/components/ui/Divider'
import { ScrollFade } from '@/components/ui/scroll-fade'
import { cn } from '@/lib/utils'
import { formatCurrency } from '../winners/winners-panel'

interface StatsData {
  number: number
  value: number
}

interface StatisticsPanelProps {
  stats?: StatsData[]
}

/**
 * Component to display betting statistics
 */
const StatisticsPanel = ({ stats = [] }: StatisticsPanelProps) => {
  // Default stats if none provided
  const defaultStats: StatsData[] = [
    {number: 0, value: 1250},
    {number: 1, value: 3780},
    {number: 2, value: 890},
    {number: 3, value: 2340},
    {number: 4, value: 1670},
    {number: 5, value: 4120},
    {number: 6, value: 760},
    {number: 7, value: 3250},
    {number: 8, value: 1980},
    {number: 9, value: 4560},
    {number: 10, value: 650},
    {number: 11, value: 2870},
    {number: 12, value: 1430},
    {number: 13, value: 3910},
    {number: 14, value: 920},
    {number: 15, value: 2680},
    {number: 16, value: 1540},
    {number: 17, value: 4230},
    {number: 18, value: 780},
    {number: 19, value: 3150},
    {number: 20, value: 1860},
    {number: 21, value: 4390},
    {number: 22, value: 710},
    {number: 23, value: 2950},
    {number: 24, value: 1320},
    {number: 25, value: 3840},
    {number: 26, value: 970},
    {number: 27, value: 2510},
    {number: 28, value: 1790},
    {number: 29, value: 4080},
    {number: 30, value: 830},
    {number: 31, value: 3270},
    {number: 32, value: 1590},
    {number: 33, value: 4310},
    {number: 34, value: 680},
    {number: 35, value: 2930},
    {number: 36, value: 1470},

  ]

  // Get stats from props or use default, then sort by value in descending order
  const displayStats = (stats.length > 0 ? stats : defaultStats).sort((a, b) => b.value - a.value)

  return (
    <div className="flex flex-col h-full p-2 text-white overflow-hidden">
      <h3 className="text-2xl font-bold text-center text-[#C69E61]">Current Number Bets</h3>
      <Divider  />
      <div className="grid grid-cols-2 gap-1 mt-2 mb-2 mx-5">
        <h4 className="text-2xl font-thin text-white text-center">Number</h4>
        <h4 className="text-2xl font-thin text-white text-center">ZAR</h4>
      </div>

      {/* Scrollable container with fade effect */}
      <ScrollFade direction="y" size="sm" className="h-0 flex-grow mb-4 mx-5">
        <div className="grid grid-cols-2 gap-1">
          {displayStats.map((stat: StatsData) => (
            <React.Fragment key={stat.number}>
              <span className={cn(
                "text-xl font-semibold text-center",
                [1,3,5,7,9,12,14,16,18,19,21,23,25,27,30,32,34,36].includes(stat.number) ? 'text-red-600' : '',
                stat.number === 0 ? 'text-green-600' : ''
              )}>
                {stat.number}
              </span>
              <span className="text-xl text-center">{formatCurrency(stat.value)}</span>
            </React.Fragment>
          ))}
        </div>
      </ScrollFade>

      <Divider  />

      <ChevronDown className="mx-auto -mb-2 h-6 w-6" />
    </div>
  )
}

export default StatisticsPanel
