#!/usr/bin/env node

/**
 * Development Server Script
 *
 * This script starts the Vite development server and opens the specified browser
 * to the correct URL (localhost:3000) instead of a browser-named route.
 */

import { spawn } from "child_process"
import { platform } from "os"

const browser = process.argv[2] || "chrome"
const mode = process.argv[3] || "dev"
const port = 3000
const url = `http://localhost:${port}`

/**
 * Get the browser command based on the platform and browser name
 */
function getBrowserCommand(browserName) {
  const os = platform()

  const browserCommands = {
    win32: {
      chrome: "start chrome",
      firefox: "start firefox",
      edge: "start msedge",
      zen: "start zen",
      safari: "start safari",
    },
    darwin: {
      chrome: 'open -a "Google Chrome"',
      firefox: 'open -a "Firefox"',
      edge: 'open -a "Microsoft Edge"',
      zen: 'open -a "Zen Browser"',
      safari: 'open -a "Safari"',
    },
    linux: {
      chrome: "google-chrome",
      firefox: "firefox",
      edge: "microsoft-edge",
      zen: "zen-browser",
      safari: "safari",
    },
  }

  const commands = browserCommands[os]
  if (!commands) {
    console.warn(`⚠️  Unsupported platform: ${os}. Using default browser.`)
    return null
  }

  const command = commands[browserName.toLowerCase()]
  if (!command) {
    console.warn(
      `⚠️  Unsupported browser: ${browserName}. Available browsers: ${Object.keys(
        commands
      ).join(", ")}`
    )
    return commands.chrome || Object.values(commands)[0]
  }

  return command
}

/**
 * Open the URL in the specified browser
 */
function openBrowser(browserName, targetUrl) {
  const command = getBrowserCommand(browserName)

  if (!command) {
    console.log(`🌐 Opening ${targetUrl} in default browser...`)
    const os = platform()
    const fallbackCommand =
      os === "win32"
        ? `start ${targetUrl}`
        : os === "darwin"
        ? `open ${targetUrl}`
        : `xdg-open ${targetUrl}`

    spawn(fallbackCommand, { shell: true, detached: true, stdio: "ignore" })
    return
  }

  console.log(`🚀 Opening ${targetUrl} in ${browserName}...`)

  const fullCommand = `${command} ${targetUrl}`
  spawn(fullCommand, { shell: true, detached: true, stdio: "ignore" })
}

/**
 * Wait for the server to be ready by checking if the port is accessible
 */
async function waitForServer(targetUrl, maxAttempts = 30, interval = 1000) {
  for (let i = 0; i < maxAttempts; i++) {
    try {
      const response = await fetch(targetUrl)
      if (response.ok) {
        return true
      }
    } catch (error) {
      // Server not ready yet, continue waiting
    }

    await new Promise((resolve) => setTimeout(resolve, interval))
  }

  console.warn(
    `⚠️  Server did not start within ${maxAttempts} seconds. Opening browser anyway...`
  )
  return false
}

/**
 * Start the Vite development server
 */
function startViteServer(targetMode) {
  console.log(`🔧 Starting Vite development server in ${targetMode} mode...`)

  const viteProcess = spawn("npx", ["vite", "--mode", targetMode], {
    stdio: "inherit",
    shell: true,
    env: {
      ...process.env,
      BROWSER: browser, // Set the browser environment variable for consistency
    },
  })

  // Handle process termination
  process.on("SIGINT", () => {
    console.log("\n🛑 Shutting down development server...")
    viteProcess.kill("SIGINT")
    process.exit(0)
  })

  process.on("SIGTERM", () => {
    viteProcess.kill("SIGTERM")
    process.exit(0)
  })

  return viteProcess
}

/**
 * Main execution
 */
async function main() {
  if (process.argv.includes("--help") || process.argv.includes("-h")) {
    console.log(`
🚀 Development Server Script

Usage:
  node scripts/dev-server.js [browser] [mode]

Arguments:
  browser    Browser to open (chrome, firefox, edge, zen, safari)
             Default: chrome
  mode       Vite mode (dev, stg, prod)
             Default: dev

Examples:
  node scripts/dev-server.js chrome dev
  node scripts/dev-server.js firefox stg
  node scripts/dev-server.js edge prod
  
Supported Browsers:
  • chrome    - Google Chrome
  • firefox   - Mozilla Firefox  
  • edge      - Microsoft Edge
  • zen       - Zen Browser
  • safari    - Safari (macOS only)
`)
    process.exit(0)
  }

  console.log(
    `🎯 Starting development server with ${browser} browser in ${mode} mode`
  )

  // Start the Vite server
  const viteProcess = startViteServer(mode)

  // Wait a bit for the server to start, then open the browser
  setTimeout(async () => {
    console.log(`⏳ Waiting for server to be ready...`)
    await waitForServer(url)
    openBrowser(browser, url)
  }, 2000) // Wait 2 seconds before checking server readiness

  // Keep the process alive
  viteProcess.on("exit", (code) => {
    console.log(`\n📊 Vite process exited with code ${code}`)
    process.exit(code)
  })
}

main().catch((error) => {
  console.error("❌ Failed to start development server:", error)
  process.exit(1)
})
