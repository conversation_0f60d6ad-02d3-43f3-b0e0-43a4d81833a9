import { useCallback, useEffect, useRef } from "react"
import { useGameStateStore } from "@/stores/game-state-store"
import { RoundChangeData, StreamSyncData } from "./types"
import { useSignalRConnection } from "./use-signalr-connection"

/**
 * Hook for managing round data from SignalR
 */
export const useRoundData = () => {
  const {
    hubConnectionRef,
    isConnected,
    setupSignalR,
    setIsConnected,
    isMountedRef,
    registerEventHandlers,
  } = useSignalRConnection()

  const setRoundData = useGameStateStore((state) => state.setRoundData)
  const isSessionActive = useGameStateStore((state) => state.isSessionActive)

  const currentRoundIdRef = useRef<number | null>(null)
  const roundStartTimeRef = useRef<number | null>(null)

  const handleRoundChange = useCallback(
    (data: RoundChangeData) => {
      if (!isMountedRef.current) return

      setRoundData(data.round)
      currentRoundIdRef.current = data.round.rouletteDrawId
      roundStartTimeRef.current = data.roundStartTime

      return {
        round: data.round,
        position: data.position,
        previousNumber: data.previousNumber,
      }
    },
    [setRoundData, isMountedRef]
  )

  const handleStreamSync = useCallback(
    (data: StreamSyncData) => {
      if (!isMountedRef.current) return
      return data
    },
    [isMountedRef]
  )

  useEffect(() => {
    let unregisterEvents: (() => void) | undefined
    let isCleanedUp = false

    if (isSessionActive && !isCleanedUp) {
      ;(async () => {
        const hubConnection = await setupSignalR()

        // Register event handlers
        unregisterEvents = registerEventHandlers(
          handleRoundChange,
          handleStreamSync
        )

        if (hubConnection.state !== "Connected") {
          await hubConnection.start()
        }
        setIsConnected(true)
        await hubConnection.invoke("RequestCurrentRound")
      })()
    }

    return () => {
      isCleanedUp = true
      unregisterEvents?.()
    }
  }, [
    isSessionActive,
    setupSignalR,
    handleRoundChange,
    handleStreamSync,
    setIsConnected,
    registerEventHandlers,
  ])

  return {
    currentRoundIdRef,
    roundStartTimeRef,
  }
}
