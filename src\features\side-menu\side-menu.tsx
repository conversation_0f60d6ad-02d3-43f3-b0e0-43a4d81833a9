import { <PERSON>actN<PERSON>, useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, She<PERSON><PERSON><PERSON>ger } from "@/components/ui/sheet"
import { Help } from "@/features/side-menu/help"
import { MenuButton } from "@/features/side-menu/menu-button"
import { MinMaxBet } from "@/features/side-menu/minmax-display"
import { SelectedMenuHeader } from "@/features/side-menu/selected-menu-header"
import { Settings } from "@/features/side-menu/settings"
import { Sheet<PERSON>eader } from "@/features/side-menu/sheet-header"
import { UserBetHistory } from "@/features/side-menu/user-bet-history"
import { SVGIcon } from "@/hooks"
import { useMobile } from "@/hooks/use-mobile"
import { useSettingsStore } from "@/stores/settings-store"

/**
 * Menu item interface for the side menu
 */
interface MenuItem {
  name: string
  icon: ReactNode
}

/**
 * Side menu component for the game
 */
const SideMenu = () => {
  const { setIsMuted, isMuted, isVideoDisplayed, setIsVideoDisplayed } =
    useSettingsStore()
  const [selectedMenu, setSelectedMenu] = useState<string>("")

  const menuButtons: MenuItem[] = [
    { name: "History", icon: <SVGIcon url='/assets/svgs/history.svg' /> },
    { name: "Help", icon: <SVGIcon url='/assets/svgs/circle-help.svg' /> },
    { name: "Settings", icon: <SVGIcon url='/assets/svgs/settings.svg' /> },
  ]

  const renderSelectedMenu = () => {
    switch (selectedMenu) {
      case "History":
        return <UserBetHistory />
      case "Help":
        return <Help />
      case "Settings":
        return <Settings />
      default:
        return null
    }
  }

  const buttonClasses =
    "rounded-full border-2 p-1 opacity-100 transition-opacity hover:opacity-90 disabled:pointer-events-none data-[state=open]:bg-secondary"

  const isMobile = useMobile()

  return (
    <Sheet>
      {import.meta.env.VITE_APP_BUILD === "online" && (
        <SheetTrigger asChild>
          <button className='self-start flex h-auto w-auto gap-2 text-white'>
            <SVGIcon url='/assets/svgs/menu.svg' className='w-auto h-11' />
            {!isMobile && <MinMaxBet min={0} max={0} />}
          </button>
        </SheetTrigger>
      )}
      <SheetContent
        className='border-none min-w-1/3 bg-black/80 backdrop-blur-sm'
        side='left'
      >
        {selectedMenu ? (
          <SelectedMenuHeader
            selectedMenu={selectedMenu}
            menuButtons={menuButtons}
            buttonClasses={buttonClasses}
            onBack={() => setSelectedMenu("")}
          />
        ) : (
          <>
            <SheetHeader
              buttonClasses={buttonClasses}
              isMuted={isMuted}
              setIsMuted={setIsMuted}
            />
            <div className='my-2 flex items-center justify-around border-y border-amber-400 py-4 text-white'>
              <MenuButton
                icon={
                  isVideoDisplayed
                    ? "/assets/svgs/video.svg"
                    : "/assets/svgs/video-off.svg"
                }
                label='Video'
                onClick={() => setIsVideoDisplayed(!isVideoDisplayed)}
                className='rounded-lg border border-amber-400 p-2'
              />
              {menuButtons.map((menu, index) => (
                <MenuButton
                  key={index}
                  icon={`/assets/svgs/${
                    menu.name.toLowerCase() === "help"
                      ? "circle-help"
                      : menu.name.toLowerCase()
                  }.svg`}
                  label={menu.name}
                  isSelected={selectedMenu === menu.name}
                  onClick={() => setSelectedMenu(menu.name)}
                />
              ))}
            </div>
          </>
        )}
        {renderSelectedMenu()}
      </SheetContent>
    </Sheet>
  )
}

export default SideMenu
