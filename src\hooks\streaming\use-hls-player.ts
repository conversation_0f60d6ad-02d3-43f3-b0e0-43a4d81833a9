/* eslint-disable @typescript-eslint/no-explicit-any */
import Hls, { ErrorData, Level } from "hls.js"
import { useCallback, useEffect, useRef, useState } from "react"
import { Backoff } from "@/lib/backoff"
import { useSettingsStore } from "@/stores/settings-store"
import { debounce } from "../utility"
import { QUALITY_HEIGHT_MAP } from "./types"

/**
 * Hook for managing HLS.js video player
 */
export const useHlsPlayer = () => {
  const videoQuality = useSettingsStore((state) => state.videoQuality)

  const videoRef = useRef<HTMLVideoElement | null>(null)
  const hlsRef = useRef<Hls | null>(null)
  const qualityLevelsRef = useRef<Level[]>([])
  const retryTimeoutsRef = useRef<number[]>([])
  const isMountedRef = useRef(true)
  const isPlayingRef = useRef<boolean>(false)

  const [isBuffering, setIsBuffering] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const clearRetryTimeouts = useCallback(() => {
    retryTimeoutsRef.current.forEach((timeout) => clearTimeout(timeout))
    retryTimeoutsRef.current = []
  }, [])

  const findClosestQualityLevel = useCallback(
    (targetHeight: number): number => {
      if (!qualityLevelsRef.current.length || targetHeight === -1) return -1

      return qualityLevelsRef.current.reduce((prev, curr, index) => {
        if (!prev) return index
        const prevDiff = Math.abs(
          qualityLevelsRef.current[prev].height - targetHeight
        )
        const currDiff = Math.abs(curr.height - targetHeight)
        return currDiff < prevDiff ? index : prev
      }, 0)
    },
    []
  )

  const isValidQualityLevel = (targetHeight: number): boolean => {
    return qualityLevelsRef.current.some(
      (level) => level.height === targetHeight
    )
  }

  const debouncedChangeQuality = useCallback(
    debounce((targetHeight: number) => {
      if (
        !hlsRef.current ||
        !isValidQualityLevel(targetHeight) ||
        !isMountedRef.current
      )
        return

      const levelIndex = findClosestQualityLevel(targetHeight)
      if (levelIndex === -1) return

      hlsRef.current.currentLevel = levelIndex
      hlsRef.current.autoLevelCapping = levelIndex
    }, 2000),
    [findClosestQualityLevel]
  )

  const handleVideoEnd = useCallback(() => {
    if (!isMountedRef.current) return
    isPlayingRef.current = false
  }, [])

  const getHlsConfig = () => ({
    enableWorker: true,
    lowLatencyMode: true,
    liveSyncDurationCount: 1,
    liveMaxLatencyDurationCount: 3,
    backBufferLength: 3,
    maxMaxBufferLength: 60,
    manifestLoadingTimeOut: 10000,
    manifestLoadingMaxRetry: 3,
    fragLoadingTimeOut: 10000,
    fragLoadingMaxRetry: 3,
    startFragPrefetch: true,
    autoStartLoad: true,
    capLevelToPlayerSize: true,
  })

  const playVideoWithRetry = useCallback(async () => {
    const backoff = new Backoff({
      initialDelay: 100,
      maxDelay: 10000,
      maxAttempts: 10,
      factor: 2,
      shouldAbort: () => !isMountedRef.current,
      onMaxAttempts: () => {
        setError("Failed to play video")
      },
    })

    try {
      await backoff.execute(async () => {
        if (!videoRef?.current) throw new Error("Video element not found")
        await videoRef.current.play()
        isPlayingRef.current = true
        setError(null)
      })
    } catch {
      setError("Failed to play video")
    }
  }, [])

  const cleanup = useCallback(() => {
    if (!isMountedRef.current) return
    isPlayingRef.current = false

    if (videoRef?.current) {
      videoRef.current.removeEventListener("ended", handleVideoEnd)
      videoRef.current.pause()
      videoRef.current.src = ""
      videoRef.current.load()
    }

    if (hlsRef.current) {
      hlsRef.current.off(Hls.Events.ERROR)
      hlsRef.current.off(Hls.Events.MANIFEST_PARSED)
      hlsRef.current.off(Hls.Events.FRAG_LOADING)
      hlsRef.current.off(Hls.Events.FRAG_LOADED)
      hlsRef.current.destroy()
      hlsRef.current = null
    }

    clearRetryTimeouts()
    qualityLevelsRef.current = []
    setIsBuffering(false)
    setError(null)
  }, [clearRetryTimeouts, handleVideoEnd])

  const setupVideo = useCallback(
    (videoUrl: string, startPosition = 0) => {
      if (!isMountedRef.current) return

      if (!Hls.isSupported()) {
        if (videoRef?.current?.canPlayType("application/vnd.apple.mpegurl")) {
          videoRef.current.src = videoUrl
        } else {
          setError("Unsupported video format")
        }
        return
      }

      cleanup()

      if (videoRef?.current) {
        videoRef.current.addEventListener("ended", handleVideoEnd)
      }

      const hls = new Hls(getHlsConfig())
      hlsRef.current = hls

      // Setup HLS events
      let retryCount = 0

      hls.on(Hls.Events.ERROR, (_: any, data: ErrorData) => {
        if (!isMountedRef.current) return

        if (data.fatal) {
          const retryDelay = Math.min(1000 * Math.pow(2, retryCount), 10000)
          retryCount++

          const timeoutId = setTimeout(() => {
            if (isMountedRef.current) {
              hls.startLoad()
            }
          }, retryDelay)

          retryTimeoutsRef.current.push(timeoutId as unknown as number)
        }
      })

      hls.on(Hls.Events.MANIFEST_PARSED, () => {
        if (!isMountedRef.current) return

        qualityLevelsRef.current = hls.levels

        const lowestLevelIndex = 0

        if (videoQuality === "auto") {
          hls.startLevel = lowestLevelIndex
          hls.currentLevel = -1
          hls.autoLevelCapping = -1
        } else {
          const targetHeight = QUALITY_HEIGHT_MAP[videoQuality]
          const levelIndex = findClosestQualityLevel(targetHeight)
          hls.currentLevel = levelIndex
          hls.autoLevelCapping = levelIndex
        }

        if (!videoRef?.current) return
        videoRef.current.currentTime = startPosition

        hls.nextLoadLevel =
          videoQuality === "auto" ? lowestLevelIndex : hls.currentLevel

        playVideoWithRetry()
      })

      hls.on(
        Hls.Events.FRAG_LOADING,
        () => isMountedRef.current && setIsBuffering(true)
      )
      hls.on(
        Hls.Events.FRAG_LOADED,
        () => isMountedRef.current && setIsBuffering(false)
      )
      hls.on(
        Hls.Events.ERROR,
        () => isMountedRef.current && setIsBuffering(false)
      )

      try {
        const cacheBusterUrl = `${videoUrl}?t=${Date.now()}`
        hls.loadSource(cacheBusterUrl)
        if (videoRef?.current) {
          hls.attachMedia(videoRef.current)
        }
      } catch {
        setError("Failed to load video source")
      }
    },
    [
      cleanup,
      handleVideoEnd,
      videoQuality,
      findClosestQualityLevel,
      playVideoWithRetry,
    ]
  )

  // Handle quality changes
  useEffect(() => {
    if (!isMountedRef.current) return

    const targetHeight = QUALITY_HEIGHT_MAP[videoQuality]
    debouncedChangeQuality(targetHeight)
  }, [videoQuality, debouncedChangeQuality])

  // Cleanup on unmount
  useEffect(() => {
    isMountedRef.current = true

    return () => {
      isMountedRef.current = false
      cleanup()
    }
  }, [cleanup])

  return {
    videoRef,
    isPlayingRef,
    isBuffering,
    error,
    setupVideo,
    cleanup,
  }
}
