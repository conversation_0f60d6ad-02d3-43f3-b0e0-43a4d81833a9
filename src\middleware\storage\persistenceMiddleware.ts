import { tryCatch } from "@maxmorozoff/try-catch-tuple"
import { AES, enc } from "crypto-js"
import { logger } from "@/lib/logger"

export const createSecureStorage = (encryptionKey: string) => {
  const encryptData = (data: unknown): string => {
    return AES.encrypt(JSON.stringify(data), encryptionKey).toString()
  }

  const validateEncryptedData = (encrypted: string): boolean => {
    if (!encrypted || encrypted.trim() === "") return false

    // Basic validation for encrypted data format
    try {
      // AES encrypted data should be base64 encoded
      const base64Regex = /^[A-Za-z0-9+/]*={0,2}$/
      return base64Regex.test(encrypted)
    } catch {
      return false
    }
  }

  const decryptData = (encrypted: string | null): string | null => {
    if (!encrypted) {
      logger.debug("No encrypted data provided", { context: "Storage" })
      return null
    }

    if (!validateEncryptedData(encrypted)) {
      logger.warn("Invalid encrypted data format, clearing corrupted data", {
        context: "Storage",
        data: { encryptedLength: encrypted.length },
      })
      return null
    }

    const [decrypted, error] = tryCatch(
      () => AES.decrypt(encrypted, encryptionKey).toString(enc.Utf8),
      "Decrypt Data"
    )

    if (error) {
      logger.error(
        "Failed to decrypt data, clearing corrupted storage",
        error,
        {
          context: "Storage",
          data: { encryptedLength: encrypted.length },
        }
      )
      return null
    }

    // Validate decrypted content
    if (!decrypted || decrypted.trim() === "") {
      logger.warn("Decryption resulted in empty data", {
        context: "Storage",
        data: { originalLength: encrypted.length },
      })
      return null
    }

    // Validate JSON structure
    try {
      JSON.parse(decrypted)
      return decrypted
    } catch (parseError) {
      logger.error(
        "Decrypted data is not valid JSON, clearing corrupted storage",
        parseError,
        {
          context: "Storage",
          data: {
            decryptedLength: decrypted.length,
            preview: decrypted.substring(0, 50) + "...",
          },
        }
      )
      return null
    }
  }

  return {
    getItem: (key: string): string | null => {
      try {
        const item = localStorage.getItem(key)
        const decryptedData = decryptData(item)

        // If decryption failed, clear the corrupted data
        if (item && !decryptedData) {
          logger.info(`Clearing corrupted storage for key: ${key}`, {
            context: "Storage",
          })
          localStorage.removeItem(key)
        }

        return decryptedData
      } catch (error) {
        logger.error(`Failed to get item: ${key}`, error, {
          context: "Storage",
        })

        // Clear potentially corrupted data on error
        try {
          localStorage.removeItem(key)
          logger.info(`Cleared storage for key after error: ${key}`, {
            context: "Storage",
          })
        } catch (clearError) {
          logger.error(
            `Failed to clear corrupted storage: ${key}`,
            clearError,
            {
              context: "Storage",
            }
          )
        }

        return null
      }
    },

    setItem: (key: string, value: unknown): void => {
      try {
        const encrypted = encryptData(value)
        localStorage.setItem(key, encrypted)
      } catch (error) {
        if (error instanceof Error && error.name === "QuotaExceededError") {
          // Handle quota exceeded error more gracefully
          console.warn(
            `localStorage quota exceeded for key: ${key}. Attempting cleanup...`
          )

          try {
            // Try to free up space by removing old logs
            localStorage.removeItem("app_logs")

            // Retry the operation once
            localStorage.setItem(key, encrypted)
            console.info(`Successfully saved ${key} after cleanup`)
          } catch (retryError) {
            // If still failing, log to console only to avoid recursive logging
            console.error(
              `Failed to save ${key} even after cleanup:`,
              retryError
            )

            // Optionally, try to save a minimal version of the data
            try {
              const minimalData = this.createMinimalData(value)
              if (minimalData) {
                const minimalEncrypted = encryptData(minimalData)
                localStorage.setItem(key, minimalEncrypted)
                console.info(`Saved minimal version of ${key}`)
              }
            } catch (minimalError) {
              console.error(
                `Failed to save minimal version of ${key}:`,
                minimalError
              )
            }
          }
        } else {
          // For non-quota errors, use console.error to avoid recursive logging
          console.error(`Failed to set item: ${key}`, error)
        }
      }
    },

    // Helper method to create minimal data for critical stores
    createMinimalData: (value: unknown): unknown | null => {
      if (typeof value === "object" && value !== null) {
        const obj = value as Record<string, any>

        // For settings store, keep only essential settings
        if (obj.state && obj.state.isMuted !== undefined) {
          return {
            state: {
              isMuted: obj.state.isMuted,
              masterVolume: obj.state.masterVolume || 0.7,
              sfxVolume: obj.state.sfxVolume || 0.8,
              musicVolume: obj.state.musicVolume || 0.5,
              dealerVoice: obj.state.dealerVoice || "RSA",
              isVideoDisplayed: obj.state.isVideoDisplayed || false,
              videoQuality: obj.state.videoQuality || "auto",
              initialNeighbours: obj.state.initialNeighbours || 1,
            },
            version: obj.version || 0,
          }
        }

        // For betting store, keep only balance and essential data
        if (obj.state && obj.state.balance !== undefined) {
          return {
            state: {
              balance: obj.state.balance,
              betTypes: obj.state.betTypes || [],
              autoplay: {
                maxRounds: obj.state.autoplay?.maxRounds || 10,
                stopOnWin: obj.state.autoplay?.stopOnWin || false,
                stopOnLoss: obj.state.autoplay?.stopOnLoss || false,
                stopAboveBalance: obj.state.autoplay?.stopAboveBalance || 0,
                stopBelowBalance: obj.state.autoplay?.stopBelowBalance || 0,
                delayBetweenRounds:
                  obj.state.autoplay?.delayBetweenRounds || 2000,
                active: false,
                currentRound: 0,
              },
            },
            version: obj.version || 0,
          }
        }
      }

      return null
    },

    removeItem: (key: string): void => {
      try {
        localStorage.removeItem(key)
      } catch (error) {
        logger.error(`Failed to remove item: ${key}`, error, {
          context: "Storage",
        })
      }
    },

    clear: (): void => {
      try {
        localStorage.clear()
      } catch (error) {
        logger.error("Failed to clear storage", error, { context: "Storage" })
      }
    },
  }
}

// Create a default secure storage instance
// Use a combination of user agent and host for encryption key
const ENCRYPTION_KEY = window.navigator.userAgent + window.location.host
export const secureStorage = createSecureStorage(ENCRYPTION_KEY)
