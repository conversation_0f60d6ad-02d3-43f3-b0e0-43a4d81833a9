/* eslint-disable @typescript-eslint/no-explicit-any */
import { motion } from "motion/react"
import { useEffect, useRef } from "react"
import { fetchBalance, useRoundPhase } from "@/hooks"
import { formatCurrency } from "@/lib/formatting"
import { imageCache } from "@/lib/image-cache"
import { EnhancedErrorBoundary, logger } from "@/middleware"
import { useBettingStore } from "@/stores/betting-store"
import { useGameStateStore } from "@/stores/game-state-store"
import { bonusSelectors } from "@/config/bonus-config"
import { SelectableCell } from "@/config/selector-config"
import { wheelConfig, getResultingImagePath } from "@/config/wheel-config"

type ResultItem = { value: string }

// Component for displaying winning amount
const WinningAmount = ({ amount }: { amount: number }) => {
  if (amount <= 0) return null

  return (
    <p className='gradient-text absolute -bottom-10 left-1/2 -translate-x-1/2 text-center text-5xl font-extrabold leading-none drop-shadow-[0_0_55px_rgba(251,191,36,1)]'>
      You Won!
      <br />
      {formatCurrency(amount)}
    </p>
  )
}

// Component for displaying a result number with bonus
const ResultNumber = ({
  number,
  bonus,
  className = "",
  textSize = "text-4xl lg:text-5xl",
  bonusSize = "w-14 lg:w-20",
}: {
  number: string
  bonus: SelectableCell
  className?: string
  textSize?: string
  bonusSize?: string
}) => (
  <div className={`text-center ${className}`}>
    <p className={`font-bold leading-tight ${textSize}`}>{number}</p>
    <img
      src={bonus?.bonusSource}
      className={`mt-2 aspect-square h-full object-cover ${bonusSize}`}
      alt='bonus-icon'
    />
  </div>
)

// Hook for getting result data
const useResultData = (roundData: any) => {
  const getResultedChips = (): ResultItem[] => {
    const wheelNumber = roundData?.rouletteNumber.toString()
    const currentIndex = wheelConfig.findIndex(
      (num) => num.value.toString() === wheelNumber
    )

    if (currentIndex === -1) return []

    const prevIndex =
      currentIndex === 0 ? wheelConfig.length - 1 : currentIndex - 1
    const nextIndex =
      currentIndex === wheelConfig.length - 1 ? 0 : currentIndex + 1

    return [
      { value: wheelConfig[prevIndex].value.toString() },
      { value: wheelConfig[currentIndex].value.toString() },
      { value: wheelConfig[nextIndex].value.toString() },
    ]
  }

  const getResultedBonuses = (): SelectableCell[] => {
    const foundIndex = bonusSelectors.findIndex(
      (bonus) =>
        roundData &&
        bonus.cell_id.toString().split("-")[1] ===
          roundData?.bonusNumber.toString()
    )

    if (foundIndex === -1) return []

    const prevIndex =
      foundIndex === 0 ? bonusSelectors.length - 1 : foundIndex - 1
    const nextIndex =
      foundIndex === bonusSelectors.length - 1 ? 0 : foundIndex + 1

    return [
      bonusSelectors[prevIndex],
      bonusSelectors[foundIndex],
      bonusSelectors[nextIndex],
    ]
  }

  return {
    resultedChips: getResultedChips(),
    resultedBonuses: getResultedBonuses(),
  }
}

let resultImagesPreloaded = false
const preloadResultImages = () => {
  if (resultImagesPreloaded) return
  resultImagesPreloaded = true

  // Create all possible combinations of three adjacent numbers
  const possibleResults = wheelConfig.map((current, idx) => {
    const prevIdx = idx === 0 ? wheelConfig.length - 1 : idx - 1
    const nextIdx = idx === wheelConfig.length - 1 ? 0 : idx + 1

    return [
      { value: wheelConfig[prevIdx].value.toString() },
      { value: current.value.toString() },
      { value: wheelConfig[nextIdx].value.toString() },
    ]
  })

  // Get all unique image paths
  const imagePaths = possibleResults.map((resultChips) =>
    getResultingImagePath(resultChips)
  )

  // Preload using image cache to prevent redundant requests
  imageCache.preloadMultiple(imagePaths).catch((error) => {
    logger.warn("Failed to preload result images", {
      context: "ResultOverlay",
      data: { error: error.message },
    })
    // Reset flag to allow retry
    resultImagesPreloaded = false
  })
}

export const ResultOverlayInner = () => {
  const { Resulting } = useRoundPhase()
  const setBalance = useBettingStore((state) => state.setBalance)
  const setPlacedChips = useBettingStore((state) => state.setPlacedChips)
  const winnings = useBettingStore((state) => state.winnings)
  const roundData = useGameStateStore((state) => state.roundData)
  const { resultedChips, resultedBonuses } = useResultData(roundData)
  const preloadedRef = useRef(false)

  // Preload images on component mount
  useEffect(() => {
    if (!preloadedRef.current) {
      preloadResultImages()
      preloadedRef.current = true
    }
  }, [])

  // Handle balance update
  useEffect(() => {
    const token = new URLSearchParams(location.search).get("token")
    if (!token) return
    fetchBalance(token, setBalance, setPlacedChips)
  }, [Resulting])

  const shouldShow = Resulting && resultedChips.length > 0

  // Preload the specific image we'll need for this round
  useEffect(() => {
    if (resultedChips.length > 0) {
      const imgPath = getResultingImagePath(resultedChips)
      // Use image cache to prevent redundant requests
      imageCache.preload(imgPath).catch((error) => {
        logger.warn("Failed to preload specific result image", {
          context: "ResultOverlay",
          data: { imgPath, error: error.message },
        })
      })
    }
  }, [resultedChips])

  return (
    <motion.section
      initial={{ opacity: 0 }}
      animate={{ opacity: shouldShow ? 1 : 0 }}
      transition={{ duration: 0.25 }}
      className='pointer-events-none max-w-[100svw] overflow-hidden fixed inset-0 z-[40] flex select-none items-center justify-center bg-black/85'
      style={{ pointerEvents: shouldShow ? "auto" : "none" }}
    >
      {/* {shouldShow && ( */}
      <div className='relative flex items-center justify-center w-full h-full'>
        <motion.div
          initial={{ opacity: 0, y: 100, scale: 0.6 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          exit={{ opacity: 0, y: 100, scale: 0.6 }}
          transition={{ duration: 0.5 }}
          className='relative w-[800px] h-[500px] origin-center transform scale-100 lg:scale-100 md:scale-90 sm:scale-75'
          style={{
            transform: `scale(${Math.min(
              window.innerWidth / 1000,
              window.innerHeight / 600
            )})`,
          }}
        >
          <img
            className='w-full h-auto object-contain lg:max-h-[400px] max-h-[300px]'
            src={getResultingImagePath(resultedChips)}
            alt='resulting'
          />

          {/* Main Result */}
          <div className='absolute lg:left-1/2 lg:top-1/4 top-[20%] left-1/2 -translate-x-1/2 -translate-y-1/2 items-center'>
            <ResultNumber
              number={resultedChips[1]?.value || ""}
              bonus={resultedBonuses[1]}
              textSize='lg:text-6xl text-5xl font-bold'
              bonusSize='lg:w-24 w-20'
            />
          </div>

          {/* Previous Result */}
          <ResultNumber
            number={resultedChips[0]?.value || ""}
            bonus={resultedBonuses[0]}
            className='absolute lg:left-[32%] lg:top-[42%] left-[24%] top-[32%]  -translate-x-1/2 -translate-y-1/2 -rotate-[18deg] opacity-60'
          />

          {/* Next Result */}
          <ResultNumber
            number={resultedChips[2]?.value || ""}
            bonus={resultedBonuses[2]}
            className='absolute lg:right-[22%] lg:top-[42%] right-[11%] top-[32%] -translate-x-1/2 -translate-y-1/2 rotate-[15deg] opacity-60'
          />

          <WinningAmount amount={winnings} />
        </motion.div>
      </div>
      {/* )} */}
    </motion.section>
  )
}

// Main component with error boundary
export const ResultOverlay = () => {
  return (
    <EnhancedErrorBoundary context='ResultOverlay'>
      <ResultOverlayInner />
    </EnhancedErrorBoundary>
  )
}
