import { useCallback, useEffect } from "react"
import { authMiddleware, logger, toastService } from "@/middleware"
import { useAuthStore, useToken, useTokenData } from "@/stores/auth-store"
import { useBettingStore } from "@/stores/betting-store"
import { fetchBalance } from "../api/use-api"

/**
 * Custom hook for authentication functionality
 */
export const useAuth = () => {
  // Use selectors for better performance
  const token = useToken()
  const tokenData = useTokenData()
  const { setToken, setTokenData, clearAuth } = useAuthStore()
  const { setBalance, setPlacedChips } = useBettingStore()

  // Check if token is valid
  const isAuthenticated = useCallback(() => {
    if (!token) return false
    return authMiddleware.isTokenValid(token)
  }, [token])

  // Initialize authentication
  useEffect(() => {
    if (!token) return

    const initAuth = async () => {
      // Measure authentication initialization performance
      try {
        // Decode token
        const decodedToken = authMiddleware.decodeToken(token)

        if (!decodedToken) {
          logger.warn("Failed to decode token", { context: "Auth" })
          return
        }

        // Set token data
        setTokenData(decodedToken)

        // Fetch initial balance
        const balance = await fetchBalance(token, setBalance, setPlacedChips)

        logger.info("Authentication initialized", {
          context: "Auth",
          data: {
            playerID: decodedToken.PlayerID,
            balance,
          },
        })
      } catch (error) {
        logger.error("Error initializing authentication", error, {
          context: "Auth",
        })
        toastService.error(
          "Authentication Error",
          "Failed to initialize authentication. Please refresh the page."
        )
      }
    }

    initAuth()
  }, [token, setTokenData, setBalance, setPlacedChips])

  // Login function
  const login = useCallback(
    async (newToken: string) => {
      try {
        // Validate token
        if (!authMiddleware.isTokenValid(newToken)) {
          logger.error("Invalid token provided", null, { context: "Auth" })
          toastService.error("Authentication Error", "Invalid token provided")
          return false
        }

        // Set token
        setToken(newToken)

        // Decode and set token data
        const decodedToken = authMiddleware.decodeToken(newToken)
        if (decodedToken) {
          setTokenData(decodedToken)
        }

        logger.info("User logged in", { context: "Auth" })
        return true
      } catch (error) {
        logger.error("Login error", error, { context: "Auth" })
        toastService.error("Login Failed", "An error occurred during login")
        return false
      }
    },
    [setToken, setTokenData]
  )

  // Logout function
  const logout = useCallback(() => {
    clearAuth()
    toastService.info("Logged Out", "You have been logged out")
  }, [clearAuth])

  return {
    token,
    tokenData,
    isAuthenticated,
    login,
    logout,
  }
}

/**
 * Get player information from token data
 */
export const usePlayerInfo = () => {
  // Use the selector from enhanced auth store
  return useAuthStore((state) => ({
    playerId: state.tokenData?.PlayerID,
    operatorId: state.tokenData?.OperatorID,
    currencyCode: state.tokenData?.CurrencyCode,
    brandId: state.tokenData?.BrandID,
    isAuthenticated: !!state.token && !!state.tokenData,
  }))
}
