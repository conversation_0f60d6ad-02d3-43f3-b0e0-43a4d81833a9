import React, { useCallback } from "react"
import {
  FixedSizeList,
  VariableSizeList,
  ListChildComponentProps,
} from "react-window"

// Types for virtualized list props
export interface VirtualizedListProps<T> {
  items: T[]
  height: number
  width: number | string
  itemSize: number | ((index: number) => number)
  renderItem: (
    item: T,
    index: number,
    style: React.CSSProperties
  ) => React.ReactNode
  className?: string
  overscanCount?: number
  measurePerformance?: boolean
}

// Component for fixed size virtualized list
export const VirtualizedList = <T extends unknown>({
  items,
  height,
  width,
  itemSize,
  renderItem,
  className = "",
  overscanCount = 5,
}: VirtualizedListProps<T>) => {
  const renderRow = useCallback(
    ({ index, style }: ListChildComponentProps) => {
      const item = items[index]
      return renderItem(item, index, style)
    },
    [items, renderItem]
  )

  // Use FixedSizeList for fixed item sizes, VariableSizeList for dynamic sizes
  return typeof itemSize === "number" ? (
    <FixedSizeList
      className={className}
      height={height}
      width={width}
      itemCount={items.length}
      itemSize={itemSize}
      overscanCount={overscanCount}
    >
      {renderRow}
    </FixedSizeList>
  ) : (
    <VariableSizeList
      className={className}
      height={height}
      width={width}
      itemCount={items.length}
      itemSize={itemSize}
      overscanCount={overscanCount}
    >
      {renderRow}
    </VariableSizeList>
  )
}
