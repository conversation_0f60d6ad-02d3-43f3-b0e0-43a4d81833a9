import { motion } from "motion/react"
import GameSection from "@/components/ui/game-section"
import { BonusBetsPanel, BonusSymbolsGrid } from "@/features/bonus-bets"
import { NeighboursButtons } from "@/features/roulette-boards/neighbours"
import RouletteTable from "@/features/roulette-boards/roulette-table"
import SideMenu from "@/features/side-menu/side-menu"
import VideoPlayer from "@/features/streaming/video-player"
import { History } from "@/hooks"
import { cn } from "@/lib/utils"
import BettingOverlay from "../common/betting-overlay"
import GameHistoryDisplay from "./game-history-display"
import {
  LAYOUT_CONSTANTS,
  GAME_SECTION_STYLES,
  PHASE_DEPENDENT_STYLES,
  GamePhaseStates,
} from "./layout-constants"

interface MobileTopSectionProps {
  gameHistoryResults: History[]
}

interface MobileBettingAreaProps extends GamePhaseStates {
  gameHistoryResults: History[]
  gameType: string
}

interface BonusSymbolsAnimationProps {
  isMobile: boolean
}

const MobileTopSection = ({ gameHistoryResults }: MobileTopSectionProps) => (
  <>
    <BettingOverlay />
    <div className='col-span-full w-full row-start-1 relative max-h-[40vh] overflow-hidden'>
      <div className='h-auto max-h-[5vh] z-30'>
        <GameHistoryDisplay gameHistoryResults={gameHistoryResults} />
      </div>
      <div className='overflow-hidden transition-all duration-500 ease-in-out max-h-[24vh]'>
        <VideoPlayer />
      </div>
      <span className='absolute top-8 left-0 z-30 max-w-8'>
        <SideMenu />
      </span>
    </div>
  </>
)

const BonusSymbolsAnimation = ({ isMobile }: BonusSymbolsAnimationProps) => (
  <motion.div
    key='bonus-symbols-grid'
    initial={{ opacity: 0, y: -20 }}
    animate={{ opacity: 1, y: 0 }}
    exit={{ opacity: 0, y: 20 }}
    transition={{
      duration: LAYOUT_CONSTANTS.ANIMATION.DURATION / 1000,
      type: "spring",
      stiffness: LAYOUT_CONSTANTS.ANIMATION.SPRING.STIFFNESS,
      damping: LAYOUT_CONSTANTS.ANIMATION.SPRING.DAMPING,
    }}
    className='w-full flex items-end justify-center'
  >
    <BonusSymbolsGrid isMobile={isMobile} />
  </motion.div>
)

const MobileBettingArea = ({
  isBettingPhase,
  gameType,
}: MobileBettingAreaProps) => {
  const bettingPhaseStyles = isBettingPhase
    ? PHASE_DEPENDENT_STYLES.BETTING_ACTIVE
    : PHASE_DEPENDENT_STYLES.BETTING_INACTIVE

  return (
    <div
      className={cn(
        "col-span-full row-start-2 px-4 grid gap-4 relative z-30",
        "max-h-[70vh] min-h-0 overflow-hidden",
        "transition-all duration-500 ease-in-out",
        bettingPhaseStyles.MOBILE_TRANSFORM,
        bettingPhaseStyles.GRID_LAYOUT
      )}
    >
      <section className='flex flex-col gap-2'>
        <GameSection
          className={cn(
            GAME_SECTION_STYLES.BACKGROUND_OVERLAY,
            GAME_SECTION_STYLES.BONUS_BORDER,
            "w-auto min-h-0 transition-all duration-500 ease-in-out",
            "flex flex-col overflow-hidden",
            isBettingPhase ? bettingPhaseStyles.SHADOW : ""
          )}
          title='Bonus Bets'
        >
          <div className='flex-1 min-h-0 overflow-hidden'>
            <BonusBetsPanel />
          </div>
        </GameSection>

        {!isBettingPhase && (
          <GameSection
            className={cn(
              GAME_SECTION_STYLES.BACKGROUND_OVERLAY,
              GAME_SECTION_STYLES.BONUS_BORDER,
              "p-0 h-auto w-auto min-h-0 transition-all duration-500 ease-in-out",
              "flex flex-col overflow-none"
            )}
            title='Bonus Bets'
          >
            <BonusSymbolsAnimation isMobile={true} />
          </GameSection>
        )}
      </section>

      <GameSection
        hasInsetShadow={false}
        className={cn(
          "overflow-hidden min-h-0 transition-all duration-500 ease-in-out flex flex-col",
          gameType === "special" ? "" : GAME_SECTION_STYLES.BACKGROUND_OVERLAY
        )}
        title='Betting Table'
      >
        <div className='flex-1 flex flex-col min-h-0 overflow-hidden'>
          <RouletteTable />
          <NeighboursButtons />
        </div>
      </GameSection>
    </div>
  )
}

export { MobileTopSection, MobileBettingArea }
