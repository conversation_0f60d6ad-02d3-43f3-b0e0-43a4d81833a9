import { create, StateCreator, StoreApi, UseBoundStore } from "zustand"
import { createJSONStorage, persist, PersistOptions } from "zustand/middleware"
import { immer } from "zustand/middleware/immer"
import { enableMapSet } from "immer"
import { logger } from "@/lib/logger"
import { secureStorage } from "./persistenceMiddleware"

// Enable Immer MapSet plugin to support Set and Map in draft state
enableMapSet()

// Type for enhanced store options
export interface EnhancedStoreOptions<T> {
  name?: string
  persist?: boolean
  secure?: boolean
  immer?: boolean
  partialize?: (state: T) => Partial<T>
  onRehydrateStorage?: (
    state: T | undefined
  ) => ((state: T | undefined, error: any) => void) | void
  version?: number
}

// Create an enhanced store with optional middleware
export function createEnhancedStore<T extends object>(
  initializer: StateCreator<T, [["zustand/immer", never]], []>,
  options: EnhancedStoreOptions<T> = {}
): UseBoundStore<StoreApi<T>> {
  const {
    name,
    persist: shouldPersist = false,
    secure = true,
    immer: useImmer = true,
    partialize,
    onRehydrateStorage,
    version,
  } = options

  // Start with the base initializer
  let storeCreator: StateCreator<T, [["zustand/immer", never]], []> =
    initializer

  // Add immer middleware if requested
  if (useImmer) {
    storeCreator = immer(storeCreator) as StateCreator<
      T,
      [["zustand/immer", never]],
      []
    >
  }

  // Add persistence if requested
  if (shouldPersist && name) {
    const persistOptions: PersistOptions<T> = {
      name,
      storage: createJSONStorage(() => (secure ? secureStorage : localStorage)),
      onRehydrateStorage: (state) => {
        logger.debug(`Rehydrating store: ${name}`, { context: "Store" })
        return onRehydrateStorage ? onRehydrateStorage(state) : undefined
      },
    }

    // Add partialize function if provided
    if (partialize) {
      persistOptions.partialize = partialize as (state: T) => T
    }

    // Add version if provided
    if (version !== undefined) {
      persistOptions.version = version
    }

    // We need to use type assertions here to make the middleware chain work correctly
    storeCreator = persist(
      storeCreator as any as StateCreator<T>,
      persistOptions
    ) as any as StateCreator<T, [["zustand/immer", never]], []>
  }

  // Create and return the store
  return create<T>(storeCreator as any as StateCreator<T>)
}

// Helper to create a selector hook with type safety
export function createSelector<T, U>(
  store: UseBoundStore<StoreApi<T>>,
  selector: (state: T) => U
) {
  return () => store(selector)
}
