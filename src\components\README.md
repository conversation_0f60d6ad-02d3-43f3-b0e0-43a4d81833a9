# Component Library

This directory contains all UI components used in the application, organized by their function and reusability.

## Directory Structure

```
components/
├── ui/                # Base UI components (atoms/primitives)
│   ├── data-display/  # Components for displaying data (tables, cards, etc.)
│   ├── feedback/      # Components for user feedback (alerts, toasts, etc.)
│   ├── inputs/        # Form inputs and controls
│   ├── layout/        # Layout components (grid, container, etc.)
│   ├── navigation/    # Navigation components (tabs, menus, etc.)
│   └── overlay/       # Overlay components (modals, popovers, etc.)
├── common/            # Composite components (molecules)
│   ├── cards/         # Card-based components
│   ├── panels/        # Panel components
│   ├── navigation/    # Navigation components
│   └── loaders/       # Loading components
└── features/          # Feature-specific components (organisms)
    ├── bonus-bets/    # Bonus bets components
    ├── past-results/  # Past results components
    ├── roulette-boards/ # Roulette board components
    ├── side-menu/     # Side menu components
    ├── statistics/    # Statistics components
    └── winners/       # Winners components
```

## Component Types

- **UI Components**: Basic building blocks (atoms/primitives)
- **Common Components**: Reusable composite components (molecules)
- **Feature Components**: Feature-specific components (organisms)

## Usage Guidelines

1. Always use UI components as building blocks for more complex components
2. Prefer composition over inheritance
3. Use variants for different styles of the same component
4. Keep components focused on a single responsibility
5. Document props with JSDoc comments
