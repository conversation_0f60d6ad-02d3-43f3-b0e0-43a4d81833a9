import { SheetClose } from "@/components/ui/sheet"
import { SVGIcon } from "@/hooks"
import { useMobile } from "@/hooks/use-mobile"

interface Menu {
  name: string
  icon: React.ReactNode
}

interface SelectedMenuHeaderProps {
  selectedMenu: string
  menuButtons: Menu[]
  buttonClasses: string
  onBack: () => void
}

export const SelectedMenuHeader = ({
  selectedMenu,
  menuButtons,
  buttonClasses,
  onBack,
}: SelectedMenuHeaderProps) => {
  const isMobile = useMobile()
  const selectedButton = menuButtons.find((menu) => menu.name === selectedMenu)

  return (
    <div className='mb-2 flex items-center justify-between space-x-4 border-b border-amber-400 p-2'>
      <div className='flex items-center space-x-2'>
        {selectedButton?.icon}
        <h1 className='text-2xl font-semibold text-white'>{selectedMenu}</h1>
      </div>
      {isMobile ? (
        <SheetClose className={buttonClasses}>
          <SVGIcon url='/assets/svgs/chevron-left.svg' className='h-5 w-5' />
        </SheetClose>
      ) : (
        <button className={buttonClasses} onClick={onBack}>
          <SVGIcon url='/assets/svgs/chevron-left.svg' className='h-5 w-5' />
        </button>
      )}
    </div>
  )
}
