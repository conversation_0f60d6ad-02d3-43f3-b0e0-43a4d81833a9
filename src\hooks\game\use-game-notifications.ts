import { useEffect, useRef } from "react"
import { toastService, logger } from "@/middleware"
import { useBettingStore } from "@/stores/betting-store"
import { useGameStateStore } from "@/stores/game-state-store"
import { useRoundPhase } from "./use-game-phases"

/**
 * Custom hook for game notifications
 * Displays toast notifications for important game events
 */
export const useGameNotifications = () => {
  const roundData = useGameStateStore((state) => state.roundData)
  const { Betting, Spinning, Resulting } = useRoundPhase()
  const totalBet = useBettingStore((state) => state.totalBet)
  const placedChips = useBettingStore((state) => state.placedChips)
  const betslipId = useBettingStore((state) => state.betslipId)

  // Refs to track state changes
  const prevPhaseRef = useRef<string>("")
  const notifiedBettingRef = useRef(false)
  const notifiedSpinningRef = useRef(false)
  const notifiedResultRef = useRef(false)
  const prevRoundIdRef = useRef<number | null>(null)

  // Handle phase changes
  useEffect(() => {
    let currentPhase = ""
    if (Betting) currentPhase = "Betting"
    if (Spinning) currentPhase = "Spinning"
    if (Resulting) currentPhase = "Resulting"

    // Skip if phase hasn't changed
    if (currentPhase === prevPhaseRef.current) return

    // Log phase change
    logger.info(`Game phase changed to ${currentPhase}`, {
      context: "GamePhase",
      data: {
        previousPhase: prevPhaseRef.current,
        currentPhase,
        roundId: roundData?.rouletteDrawId,
      },
    })

    // Update previous phase
    prevPhaseRef.current = currentPhase

    // Reset notification flags when a new round starts
    if (roundData?.rouletteDrawId !== prevRoundIdRef.current) {
      notifiedBettingRef.current = false
      notifiedSpinningRef.current = false
      notifiedResultRef.current = false
      prevRoundIdRef.current = roundData?.rouletteDrawId || null
    }

    // Show phase notifications
    if (Betting && !notifiedBettingRef.current) {
      toastService.info("Betting Open", "Place your bets now")
      notifiedBettingRef.current = true
    }

    if (Spinning && !notifiedSpinningRef.current) {
      if (placedChips.length > 0) {
        toastService.info("Betting Closed", "No more bets please")
      }
      notifiedSpinningRef.current = true
    }

    if (Resulting && !notifiedResultRef.current && roundData) {
      const { rouletteNumber, bonusNumber } = roundData

      if (betslipId && placedChips.length > 0) {
        // Show result notification with more details for players who bet
        toastService.info(
          "Round Result",
          `Number: ${rouletteNumber}, Bonus: ${bonusNumber}`
        )
      }

      notifiedResultRef.current = true
    }
  }, [Betting, Spinning, Resulting, roundData, placedChips.length, betslipId])

  // Notify about successful bet placement
  useEffect(() => {
    if (betslipId && Betting && totalBet > 0) {
      toastService.success("Bet Placed", `Total bet: ${totalBet.toFixed(2)}`)

      logger.info("Bet placed", {
        context: "Betting",
        data: {
          betslipId,
          totalBet,
          chipCount: placedChips.length,
        },
      })
    }
  }, [betslipId, Betting, totalBet, placedChips.length])

  return null
}
