import React from 'react'

interface CustomSliderProps {
  options: number[]
  selectedValue: number
  onChange: (value: number) => void
  className?: string
}

export function CustomSlider({
  options,
  selectedValue,
  onChange,
  className = 'w-64 md:w-80',
}: CustomSliderProps) {
  return (
    <div className="flex justify-center">
      <div className={`relative ${className}`}>
        {/* Track */}
        <div className="h-2 rounded-full bg-gray-800">
          <div className="absolute inset-0 h-2 rounded-full bg-linear-to-r from-gray-700 to-gray-600"></div>
        </div>
        
        {/* Option dots and labels */}
        <div className="relative -mt-4 flex w-full justify-between">
          {options.map((option) => {
            const isSelected = selectedValue === option
            return (
              <div key={option} className="flex flex-col items-center">
                <button
                  onClick={() => onChange(option)}
                  className={`h-6 w-6 rounded-full border-2 transition-colors
                    ${isSelected 
                      ? 'border-amber-400 bg-amber-400' 
                      : 'border-gray-600 bg-gray-700'}`}
                />
                <span className={`mt-1 text-sm font-medium
                  ${isSelected ? 'text-white' : 'text-gray-400'}`}>
                  {option}
                </span>
              </div>
            )
          })}
        </div>
      </div>
    </div>
  )
}