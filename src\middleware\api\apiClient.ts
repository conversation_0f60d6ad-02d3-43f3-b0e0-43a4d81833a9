import { tryCatch } from "@maxmorozoff/try-catch-tuple"
import axios, { AxiosError, AxiosRequestConfig, AxiosResponse } from "axios"
import { Backoff } from "@/lib/backoff"
import { logger } from "@/lib/logger"
import { formatError } from "../errorHandling"

// Use the standardized AppError type from errorHandling.ts
import type { AppError } from "../errorHandling"

// For backward compatibility
export type ApiError = AppError

export const createApiClient = (baseURL: string, defaultTimeout = 10000) => {
  const client = axios.create({
    baseURL,
    headers: {
      "Content-Type": "application/json",
      accept: "*/*",
    },
    timeout: defaultTimeout,
  })

  const handleApiError = (error: unknown): ApiError => {
    // Special handling for Axios errors to extract status and response data
    if (axios.isAxiosError(error)) {
      const axiosError = error as AxiosError

      return {
        message: axiosError.message || "Network error occurred",
        status: axiosError.response?.status,
        code: (axiosError.response?.data as { code?: number })?.code,
        details: axiosError.response?.data,
        context: "API",
      }
    }

    // Use the standardized error formatter for other errors
    return formatError(error, "API")
  }

  const handleResponse = <T>(response: AxiosResponse): T | null => {
    if (response.data !== undefined) {
      return response.data
    } else {
      logger.warn("Empty response data", {
        context: "API",
        data: response.config.url,
      })
      return null
    }
  }

  const apiCall = async <T>(
    method: string,
    endpoint: string,
    data: unknown = null,
    params: Record<string, string | number | boolean | undefined> | null = null,
    headers?: Record<string, string>,
    retries = 2
  ): Promise<[T | null | undefined, ApiError | null]> => {
    const backoff = new Backoff({
      initialDelay: 1000,
      maxDelay: 10000,
      maxAttempts: retries + 1,
      factor: 2,
      jitter: true,
      onMaxAttempts: () => {
        logger.error(`Request failed after ${retries + 1} attempts`, null, {
          context: "API",
          data: endpoint,
        })
      },
    })

    const [result, error] = await tryCatch(
      backoff.execute(async () => {
        const config: AxiosRequestConfig = {
          method,
          url: endpoint,
          data,
          params,
          ...(headers && { headers }),
        }

        const response = await client(config)
        const result = handleResponse<T>(response)

        if (result === null) {
          logger.error("Empty response data", null, {
            context: "API",
            data: endpoint,
          })
        }

        return result
      }),
      `API Call: ${method} ${endpoint}`
    )

    if (error) {
      const formattedError = handleApiError(error)
      const isServerError =
        formattedError.status && formattedError.status >= 500
      const shouldRetry =
        (axios.isAxiosError(error) && !error.response) || isServerError

      if (!shouldRetry) {
        return [null, formattedError]
      }
    }

    return [result, null]
  }

  return {
    get: <T>(
      endpoint: string,
      params: Record<
        string,
        string | number | boolean | undefined
      > | null = null,
      headers?: Record<string, string>
    ) => apiCall<T>("get", endpoint, null, params, headers),

    post: <T>(
      endpoint: string,
      data: unknown,
      headers?: Record<string, string>
    ) => apiCall<T>("post", endpoint, data, null, headers),

    put: <T>(
      endpoint: string,
      data: unknown,
      headers?: Record<string, string>
    ) => apiCall<T>("put", endpoint, data, null, headers),

    delete: <T>(endpoint: string, headers?: Record<string, string>) =>
      apiCall<T>("delete", endpoint, null, null, headers),

    patch: <T>(
      endpoint: string,
      data: unknown,
      headers?: Record<string, string>
    ) => apiCall<T>("patch", endpoint, data, null, headers),
  }
}

// Create a default API client instance
const API_BASE_URL = import.meta.env.VITE_APP_API_URL
export const apiClient = createApiClient(API_BASE_URL)
