/**
 * Dynamic import utility for code splitting
 * 
 * This utility helps with dynamic imports for better code splitting.
 * It's a lightweight wrapper around the dynamic import() function
 * that provides better error handling and logging.
 */

import { logger } from '@/middleware'

/**
 * Dynamically import a module
 * 
 * @param importFn - Function that returns a dynamic import
 * @param options - Options for the dynamic import
 * @returns Promise that resolves to the imported module
 */
export async function dynamicImport<T>(
  importFn: () => Promise<T>,
  options: {
    moduleName: string
    onError?: (error: Error) => void
  }
): Promise<T> {
  const { moduleName, onError } = options

  try {
    logger.debug(`Dynamically importing module: ${moduleName}`, { context: 'DynamicImport' })
    return await importFn()
  } catch (error) {
    logger.error(`Failed to dynamically import module: ${moduleName}`, error, { context: 'DynamicImport' })
    
    if (onError && error instanceof Error) {
      onError(error)
    }
    
    throw error
  }
}

/**
 * Preload a module without waiting for it
 * 
 * @param importFn - Function that returns a dynamic import
 * @param options - Options for preloading
 */
export function preloadModule<T>(
  importFn: () => Promise<T>,
  options: {
    moduleName: string
    priority?: 'high' | 'low'
  }
): void {
  const { moduleName, priority = 'low' } = options
  
  // Use requestIdleCallback for low priority, or setTimeout for high priority
  const schedulePreload = priority === 'low' && window.requestIdleCallback
    ? window.requestIdleCallback
    : (cb: () => void) => setTimeout(cb, priority === 'high' ? 0 : 100)
  
  schedulePreload(() => {
    logger.debug(`Preloading module: ${moduleName}`, { context: 'DynamicImport' })
    importFn().catch(error => {
      logger.error(`Failed to preload module: ${moduleName}`, error, { context: 'DynamicImport' })
    })
  })
}

export default {
  import: dynamicImport,
  preload: preloadModule
}
