import { jwtDecode } from "jwt-decode"
import { logger } from "@/lib/logger"
import { TokenData } from "@/stores/auth-store"

export const createAuthMiddleware = () => {
  const getAuthHeader = (token: string) => ({
    Authorization: `Bear<PERSON> ${token}`,
  })

  const decodeToken = (token: string): TokenData | null => {
    try {
      return jwtDecode(token) as TokenData
    } catch (error) {
      logger.error("Failed to decode token", error)
      return null
    }
  }

  const isTokenValid = (token: string): boolean => {
    try {
      const decoded = jwtDecode(token) as { exp: number }
      return decoded.exp * 1000 > Date.now()
    } catch (error) {
      logger.error("Failed to validate token", error)
      return false
    }
  }

  return {
    getAuthHeader,
    decodeToken,
    isTokenValid,
  }
}

// Create a default auth middleware instance
export const authMiddleware = createAuthMiddleware()
