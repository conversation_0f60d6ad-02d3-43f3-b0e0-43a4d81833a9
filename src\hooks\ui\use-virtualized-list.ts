import { useState, useEffect, useCallback } from "react"
import { logger } from "@/middleware"

/**
 * Custom hook for virtualized list configuration
 */
export const useVirtualizedList = <T>(
  items: T[],
  options: {
    itemHeight?: number | ((index: number) => number)
    overscanCount?: number
    measurePerformance?: boolean
    logItemChanges?: boolean
    context?: string
  } = {}
) => {
  const {
    itemHeight = 50,
    overscanCount = 5,
    logItemChanges = false,
    context = "VirtualizedList",
  } = options

  const [windowWidth, setWindowWidth] = useState(window.innerWidth)
  const [windowHeight, setWindowHeight] = useState(window.innerHeight)

  // Update window dimensions on resize
  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth)
      setWindowHeight(window.innerHeight)
    }

    window.addEventListener("resize", handleResize)
    return () => {
      window.removeEventListener("resize", handleResize)
    }
  }, [])

  // Log item changes if enabled
  useEffect(() => {
    if (logItemChanges) {
      logger.debug(`Items updated (count: ${items.length})`, {
        context,
        data: { itemCount: items.length },
      })
    }
  }, [items.length, logItemChanges, context])

  // Calculate responsive item height
  const getResponsiveItemHeight = useCallback(
    (index: number): number => {
      if (typeof itemHeight === "function") {
        return itemHeight(index)
      }

      // Adjust item height based on screen size
      if (windowWidth < 640) {
        return itemHeight * 1.5 // Larger on small screens
      }

      return itemHeight
    },
    [itemHeight, windowWidth]
  )

  // Calculate list height based on container height or item count
  const getListHeight = useCallback(
    (containerHeight: number): number => {
      // If container height is provided, use it
      if (containerHeight > 0) {
        return containerHeight
      }

      // Otherwise calculate based on item count (with a maximum)
      const totalItems = Math.min(items.length, 10)
      const avgItemHeight = typeof itemHeight === "number" ? itemHeight : 50 // Default if dynamic height

      return totalItems * avgItemHeight
    },
    [items.length, itemHeight]
  )

  return {
    windowWidth,
    windowHeight,
    getResponsiveItemHeight,
    getListHeight,
    overscanCount,
  }
}
