import { useEffect, useRef, useState } from "react"
import { useGameStateStore } from "@/stores/game-state-store"
import { useSettingsStore } from "@/stores/settings-store"
import { SoundKeys, soundMap } from "@/config/audio-config"
import { useRoundPhase } from "./use-game-phases"

export const useAudioController = () => {
  const roundData = useGameStateStore((state) => state.roundData)
  const playSound = useSettingsStore((state) => state.playSound)
  const dealerVoice = useSettingsStore((state) => state.dealerVoice)
  const { Betting, Spinning, Resulting } = useRoundPhase()
  const timerRef = useRef(15)
  const [remaining, setRemaining] = useState(15)
  const initialSoundsPlayedRef = useRef(false)

  const getVoiceKey = (baseKey: string): SoundKeys => {
    const voicePrefix = dealerVoice.toLowerCase()
    const voiceKey = `${voicePrefix}-${baseKey}` as SoundKeys

    return soundMap[voiceKey] ? voiceKey : (`rsa-${baseKey}` as SoundKeys)
  }

  useEffect(() => {
    let intervalId: NodeJS.Timeout | null = null

    if (Betting) {
      timerRef.current = 15
      setRemaining(15)

      intervalId = setInterval(() => {
        timerRef.current -= 1
        setRemaining(timerRef.current)

        if (timerRef.current <= 0) {
          clearInterval(intervalId as NodeJS.Timeout)
        }
      }, 1000)
    } else {
      if (intervalId) {
        clearInterval(intervalId)
      }
    }

    return () => {
      if (intervalId) {
        clearInterval(intervalId)
      }
    }
  }, [Betting])

  useEffect(() => {
    if (roundData && !initialSoundsPlayedRef.current) {
      const ambience = "ambience" as SoundKeys
      const music = "bg-music" as SoundKeys
      const greeting = getVoiceKey("greeting")

      const playInitialSounds = async () => {
        initialSoundsPlayedRef.current = true
        await playSound(greeting, false, true)
        await playSound(ambience, true)
        await playSound(music, true)
      }

      playInitialSounds()
    }
  }, [roundData, playSound])

  useEffect(() => {
    const runAudio = () => {
      if (!roundData) return

      switch (true) {
        case Betting: {
          if (remaining === 14) {
            playSound(getVoiceKey("open"), false, true)
          }

          if (remaining === 11) {
            playSound(getVoiceKey("ten-sec"), false, true)
          }

          if (remaining === 6) {
            playSound(getVoiceKey("five-sec"), false, true)
          }

          if (remaining === 2) {
            playSound(getVoiceKey("closed"), false, true)
          }
          break
        }
        case Spinning: {
          break
        }
        case Resulting: {
          const playSounds = async () => {
            await playSound(
              getVoiceKey(`number-${roundData?.rouletteNumber}`),
              false,
              true
            )
            await playSound(
              getVoiceKey(`bonus-${roundData?.bonusNumber}`),
              false,
              true
            )
          }
          playSounds()
          break
        }
        default:
          return null
      }
    }
    runAudio()
  }, [Betting, remaining, Spinning, Resulting, playSound, dealerVoice])
}
