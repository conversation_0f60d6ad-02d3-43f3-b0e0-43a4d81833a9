import { ReactNode } from "react"
import { cn } from "@/lib/utils"
import { EnhancedErrorBoundary } from "@/middleware"

interface GameSectionProps {
  className: string
  title: string
  hasBorder?: boolean
  hasInsetShadow?: boolean
  children?: ReactNode
  style?: React.CSSProperties
}

/**
 * Component for each game section with error boundary
 */
const GameSection = ({
  className,
  title,
  hasInsetShadow = true,
  children,
  style,
}: GameSectionProps) => {
  // Define the default inset shadow
  const insetShadowStyle = hasInsetShadow
    ? "inset 10px 10px 10px -5px rgba(0, 0, 0, 0.5)"
    : ""

  // Combine box shadows if both exist
  const combinedStyle = {
    ...style,
  }

  if (hasInsetShadow) {
    // If user provided a boxShadow, combine it with the inset shadow
    if (style?.boxShadow) {
      combinedStyle.boxShadow = `${insetShadowStyle}, ${style.boxShadow}`
    } else {
      combinedStyle.boxShadow = insetShadowStyle
    }
  }

  // Check if style has overflow property set
  const hasOverflowStyle = style && "overflow" in style

  return (
    <EnhancedErrorBoundary context={`GameSection-${title}`}>
      <section
        className={cn(
          "w-full h-full rounded-lg",
          hasInsetShadow && !hasOverflowStyle && "relative overflow-hidden",
          className
        )}
        style={combinedStyle}
      >
        {children}
      </section>
    </EnhancedErrorBoundary>
  )
}

export default GameSection
