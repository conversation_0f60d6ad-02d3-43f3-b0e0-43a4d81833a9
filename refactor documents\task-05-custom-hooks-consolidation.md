# Task 05: Custom Hooks Consolidation

## Task Overview

**Priority**: Medium | **Risk**: Low-Medium | **Impact**: Medium  
**Estimated Duration**: 1.5 weeks | **Dependencies**: Task 04 (Store Optimization)

### Description
Consolidate overlapping custom hooks functionality and create hook composition patterns to reduce duplication, improve developer experience, and enhance code reusability. This addresses scattered hook logic, duplicate patterns, and opportunities for better encapsulation.

### Expected Impact
- Reduce hook count by ~30% through consolidation
- Improve developer experience with composed hooks
- Better encapsulation of related functionality
- Enhanced hook reusability across components

## Current State Analysis

### Hook Organization Issues

**Game Hooks Overlap**:
- `useRoundPhase` (game phases)
- `useAudioController` (audio + game phases)
- `useGameNotifications` (notifications + game phases)
- `useGameDataManagement` (data + game phases)

**API Hooks Pattern Duplication**:
```typescript
// Similar patterns across multiple API hooks
export const useHistoryQuery = (lookBehind: number = 10) => {
  return useApiQuery<History[]>(
    ['history', JSON.stringify(lookBehind)],
    `${ENDPOINTS.UPCOMING_ROUNDS}?lookAheadMinutes=-${lookBehind}`,
    null,
    undefined,
    {
      staleTime: 1000 * 60,
      refetchInterval: 1000 * 60 * 5,
      // ... similar configuration
    }
  )
}

export const useWinningsQuery = (betslipId: number) => {
  return useApiQuery<WinningsResponse>(
    ['winnings', betslipId],
    `${ENDPOINTS.WINNINGS}/${betslipId}`,
    null,
    undefined,
    {
      staleTime: 1000 * 30,
      refetchInterval: 1000 * 60,
      // ... similar configuration
    }
  )
}
```

**UI Interaction Hooks Duplication**:
- `useMobile` (device detection)
- `useResponsiveSize` (responsive utilities)
- `useDimensions` (dimension tracking)
- `useTouchGestures` (touch handling)

### Streaming Hooks Complexity
Multiple hooks managing related streaming concerns:
- `useSignalRConnection`
- `useVideoSource`
- `useHlsPlayer`
- `useRoundData`
- `useStreamConnection` (orchestrator)

## Target Architecture

### New Hook Structure
```
src/hooks/
├── composed/
│   ├── useGameState.ts          (phases + round data + timing)
│   ├── useTableInteractions.ts  (mouse + touch + keyboard)
│   ├── useAudioSystem.ts        (controller + settings + playback)
│   ├── useResponsiveLayout.ts   (mobile + dimensions + gestures)
│   └── useStreamingSystem.ts    (signalr + video + data)
├── factories/
│   ├── createApiHook.ts         (API hook factory)
│   ├── createQueryHook.ts       (Query hook factory)
│   └── createInteractionHook.ts (Interaction hook factory)
├── primitives/
│   ├── usePhaseTimer.ts         (core timing logic)
│   ├── useDeviceDetection.ts    (core device detection)
│   └── useEventHandler.ts       (core event handling)
└── utilities/
    ├── hookComposition.ts       (composition utilities)
    └── hookValidation.ts        (validation utilities)
```

### Composed Game State Hook
```typescript
// useGameState.ts (replaces multiple separate hooks)
interface GameState {
  // Phase information
  phases: {
    isBetting: boolean
    isSpinning: boolean
    isResulting: boolean
  }
  
  // Round data
  roundData: RouletteRound | null
  gameType: GameType
  
  // Timing information
  timeRemaining: number
  phaseProgress: number
  
  // Utilities
  isPhaseActive: (phase: GamePhase) => boolean
  getPhaseTimeRemaining: (phase: GamePhase) => number
}

export const useGameState = (): GameState => {
  const roundData = useGameStateStore(state => state.roundData)
  const gameType = useGameStateStore(state => state.gameType)
  const phases = useRoundPhase()
  const timing = usePhaseTimer(roundData)
  
  return useMemo(() => ({
    phases: {
      isBetting: phases.Betting,
      isSpinning: phases.Spinning,
      isResulting: phases.Resulting
    },
    roundData,
    gameType,
    timeRemaining: timing.remaining,
    phaseProgress: timing.progress,
    isPhaseActive: (phase: GamePhase) => phases[phase],
    getPhaseTimeRemaining: timing.getPhaseTime
  }), [phases, roundData, gameType, timing])
}
```

### API Hook Factory
```typescript
// createApiHook.ts
interface ApiHookConfig<T> {
  endpoint: string
  queryKey: (params: any) => string[]
  transform?: (data: any) => T
  staleTime?: number
  refetchInterval?: number
  enabled?: boolean
}

export const createApiHook = <T, P = void>(
  config: ApiHookConfig<T>
) => {
  return (params?: P) => {
    return useApiQuery<T>(
      config.queryKey(params),
      `${config.endpoint}${params ? `/${params}` : ''}`,
      null,
      undefined,
      {
        staleTime: config.staleTime || 1000 * 60,
        refetchInterval: config.refetchInterval || 1000 * 60 * 5,
        enabled: config.enabled !== false,
        select: config.transform,
        showErrorToast: true,
        errorToastTitle: `Failed to Load ${config.endpoint}`
      }
    )
  }
}

// Usage: Create specific API hooks
export const useHistoryQuery = createApiHook<History[], number>({
  endpoint: ENDPOINTS.UPCOMING_ROUNDS,
  queryKey: (lookBehind) => ['history', String(lookBehind)],
  transform: (data) => data?.map(round => ({
    rouletteNumber: round.rouletteNumber,
    bonusNumber: round.bonusNumber
  })) || [],
  staleTime: 1000 * 60
})

export const useWinningsQuery = createApiHook<WinningsResponse, number>({
  endpoint: ENDPOINTS.WINNINGS,
  queryKey: (betslipId) => ['winnings', String(betslipId)],
  staleTime: 1000 * 30
})
```

### Responsive Layout Composition
```typescript
// useResponsiveLayout.ts (combines multiple responsive hooks)
interface ResponsiveLayout {
  // Device information
  isMobile: boolean
  isTablet: boolean
  isDesktop: boolean
  
  // Dimensions
  viewport: { width: number; height: number }
  breakpoint: string
  
  // Interactions
  touchGestures: TouchGestureHandlers
  
  // Utilities
  getLayoutClasses: (config: LayoutConfig) => string
  isBreakpointActive: (breakpoint: string) => boolean
}

export const useResponsiveLayout = (): ResponsiveLayout => {
  const device = useDeviceDetection()
  const dimensions = useDimensions()
  const gestures = useTouchGestures()
  const breakpoint = useBreakpoint(dimensions.viewport.width)
  
  return useMemo(() => ({
    isMobile: device.isMobile,
    isTablet: device.isTablet,
    isDesktop: device.isDesktop,
    viewport: dimensions.viewport,
    breakpoint: breakpoint.current,
    touchGestures: gestures,
    getLayoutClasses: (config) => generateLayoutClasses(config, device, breakpoint),
    isBreakpointActive: (bp) => breakpoint.isActive(bp)
  }), [device, dimensions, gestures, breakpoint])
}
```

## Step-by-Step Implementation Plan

### Phase 1: Hook Factories (Days 1-3)

#### Day 1: Create API Hook Factory
- [ ] Create `createApiHook.ts` with generic API hook factory
- [ ] Implement configuration-based hook generation
- [ ] Add TypeScript generics for type safety
- [ ] Create factory validation utilities

#### Day 2: Create Query Hook Factory
- [ ] Create `createQueryHook.ts` for React Query patterns
- [ ] Implement common query configurations
- [ ] Add error handling and loading states
- [ ] Create query composition utilities

#### Day 3: Create Interaction Hook Factory
- [ ] Create `createInteractionHook.ts` for event handling
- [ ] Implement common interaction patterns
- [ ] Add touch/mouse event abstractions
- [ ] Create interaction validation

### Phase 2: Composed Hooks (Days 4-7)

#### Day 4: Create Game State Composition
- [ ] Create `useGameState.ts` combining game-related hooks
- [ ] Implement phase, timing, and round data composition
- [ ] Add game state utilities and helpers
- [ ] Create game state validation

#### Day 5: Create Responsive Layout Composition
- [ ] Create `useResponsiveLayout.ts` combining responsive hooks
- [ ] Implement device, dimensions, and gesture composition
- [ ] Add layout utility functions
- [ ] Create responsive validation

#### Day 6: Create Audio System Composition
- [ ] Create `useAudioSystem.ts` combining audio hooks
- [ ] Implement controller, settings, and playback composition
- [ ] Add audio state management
- [ ] Create audio validation

#### Day 7: Create Streaming System Composition
- [ ] Create `useStreamingSystem.ts` combining streaming hooks
- [ ] Implement SignalR, video, and data composition
- [ ] Add streaming state management
- [ ] Create streaming validation

### Phase 3: Migration and Optimization (Days 8-10)

#### Day 8: Migrate API Hooks
- [ ] Replace existing API hooks with factory-generated ones
- [ ] Update component imports and usage
- [ ] Verify functionality preservation
- [ ] Add migration tests

#### Day 9: Migrate Composed Hooks
- [ ] Update components to use composed hooks
- [ ] Replace multiple hook calls with single composed hooks
- [ ] Optimize hook dependencies
- [ ] Add composition tests

#### Day 10: Performance Optimization
- [ ] Add proper memoization to composed hooks
- [ ] Optimize hook dependencies and re-renders
- [ ] Implement selective subscriptions
- [ ] Add performance monitoring

### Phase 4: Testing and Documentation (Days 11)

#### Day 11: Testing and Documentation
- [ ] Unit tests for all hook factories
- [ ] Integration tests for composed hooks
- [ ] Performance benchmarking
- [ ] Create hook documentation and examples

## Code Examples

### Before: Multiple Separate Hooks
```typescript
// Component using multiple hooks
const MyComponent = () => {
  const { Betting, Spinning, Resulting } = useRoundPhase()
  const roundData = useGameStateStore(state => state.roundData)
  const gameType = useGameStateStore(state => state.gameType)
  const isMobile = useMobile()
  const dimensions = useDimensions()
  const gestures = useTouchGestures()
  const { data: history } = useHistoryQuery(10)
  const { data: winnings } = useWinningsQuery(betslipId)
  
  // Component logic using all these hooks...
}
```

### After: Composed Hooks
```typescript
// Component using composed hooks
const MyComponent = () => {
  const gameState = useGameState()
  const layout = useResponsiveLayout()
  const { history, winnings } = useGameData({ historyLimit: 10, betslipId })
  
  // Cleaner component logic with composed state...
}
```

### Hook Factory Example
```typescript
// Before: Repetitive API hooks
export const useHistoryQuery = (lookBehind: number = 10) => {
  return useApiQuery<History[]>(
    ['history', JSON.stringify(lookBehind)],
    `${ENDPOINTS.UPCOMING_ROUNDS}?lookAheadMinutes=-${lookBehind}`,
    // ... 20 lines of similar configuration
  )
}

// After: Factory-generated hook
export const useHistoryQuery = createApiHook<History[], number>({
  endpoint: ENDPOINTS.UPCOMING_ROUNDS,
  queryKey: (lookBehind) => ['history', String(lookBehind)],
  transform: (data) => data?.map(transformHistoryItem) || [],
  staleTime: 1000 * 60
})
```

## Testing Strategy

### Unit Tests
```typescript
// createApiHook.test.ts
describe('createApiHook', () => {
  it('creates API hook with correct configuration', () => {
    const useTestHook = createApiHook<TestData, string>({
      endpoint: '/test',
      queryKey: (param) => ['test', param],
      staleTime: 5000
    })
    
    const { result } = renderHook(() => useTestHook('param'))
    
    expect(result.current.isLoading).toBe(true)
    // Verify hook behavior...
  })
})

// useGameState.test.ts
describe('useGameState', () => {
  it('composes game state correctly', () => {
    const { result } = renderHook(() => useGameState())
    
    expect(result.current).toHaveProperty('phases')
    expect(result.current).toHaveProperty('roundData')
    expect(result.current).toHaveProperty('timeRemaining')
    expect(typeof result.current.isPhaseActive).toBe('function')
  })
})
```

### Integration Tests
```typescript
// hook-composition.test.ts
describe('Hook Composition', () => {
  it('maintains performance with composed hooks', () => {
    const renderCount = jest.fn()
    
    const TestComponent = () => {
      const gameState = useGameState()
      renderCount()
      return <div>{gameState.timeRemaining}</div>
    }
    
    render(<TestComponent />)
    
    // Change unrelated state
    act(() => {
      // Trigger unrelated state change
    })
    
    // Should not cause unnecessary re-renders
    expect(renderCount).toHaveBeenCalledTimes(1)
  })
})
```

## Risk Mitigation

### Potential Issues
1. **Over-abstraction**: Making simple hooks complex
2. **Performance**: Composed hooks causing re-renders
3. **Type Safety**: Generic factories losing type information
4. **Migration**: Breaking existing hook usage

### Mitigation Strategies
1. **Gradual Migration**: Implement one composition at a time
2. **Backward Compatibility**: Keep existing hooks during transition
3. **Performance Monitoring**: Benchmark composed hooks
4. **Type Validation**: Comprehensive TypeScript testing

### Rollback Procedure
```typescript
// Feature flag for hook composition
const USE_COMPOSED_HOOKS = import.meta.env.VITE_USE_COMPOSED_HOOKS === 'true'

export const useGameState = () => {
  return USE_COMPOSED_HOOKS 
    ? useComposedGameState() 
    : useLegacyGameHooks()
}
```

## Success Criteria

### Quantitative Metrics
- [ ] Hook count reduced by 30% through consolidation
- [ ] API hook code duplication reduced by 80%
- [ ] Component hook imports reduced by 50%
- [ ] No performance regression in hook usage
- [ ] Test coverage >85% for composed hooks

### Qualitative Metrics
- [ ] Improved developer experience
- [ ] Better hook reusability
- [ ] Cleaner component interfaces
- [ ] Enhanced maintainability
- [ ] Better separation of concerns

### Acceptance Criteria
- [ ] All existing functionality preserved
- [ ] Hook composition works correctly
- [ ] API hooks maintain same behavior
- [ ] Performance benchmarks met
- [ ] Type safety maintained
- [ ] Migration completed successfully
- [ ] Code review approval
- [ ] QA testing passed
