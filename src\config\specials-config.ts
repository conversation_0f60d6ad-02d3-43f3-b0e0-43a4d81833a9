import { SelectableCell } from "./selector-config"

export const Zeros: SelectableCell[] = [
  {
    cell_id: "Zeros",
    chips_placed: [],
    cell_selectors: ["12", "35", "3", "26", "0", "32", "15"],
    isSelectable: true,
  },
  {
    cell_id: 33,
    chips_placed: [],
    cell_selectors: ["12"],
    isSelectable: true,
  },
  {
    cell_id: 99,
    chips_placed: [],
    cell_selectors: ["35"],
    isSelectable: true,
  },
  {
    cell_id: 27,
    chips_placed: [],
    cell_selectors: ["3"],
    isSelectable: true,
  },
  {
    cell_id: 93,
    chips_placed: [],
    cell_selectors: ["26"],
    isSelectable: true,
  },
  {
    cell_id: 0,
    chips_placed: [],
    cell_selectors: ["0"],
    isSelectable: true,
  },
  {
    cell_id: 97,
    chips_placed: [],
    cell_selectors: ["32"],
    isSelectable: true,
  },
  {
    cell_id: 35,
    chips_placed: [],
    cell_selectors: ["15"],
    isSelectable: true,
  },
]

export const Orphalins: SelectableCell[] = [
  {
    cell_id: "Orphelins",
    chips_placed: [],
    cell_selectors: ["1", "20", "14", "31", "9", "6", "34", "17"],
    isSelectable: true,
  },
  {
    cell_id: 127,
    chips_placed: [],
    cell_selectors: ["1"],
    isSelectable: true,
  },
  {
    cell_id: 89,
    chips_placed: [],
    cell_selectors: ["20"],
    isSelectable: true,
  },
  {
    cell_id: 85,
    chips_placed: [],
    cell_selectors: ["14"],
    isSelectable: true,
  },
  {
    cell_id: 147,
    chips_placed: [],
    cell_selectors: ["31"],
    isSelectable: true,
  },
  {
    cell_id: 31,
    chips_placed: [],
    cell_selectors: ["9"],
    isSelectable: true,
  },
  {
    cell_id: 29,
    chips_placed: [],
    cell_selectors: ["6"],
    isSelectable: true,
  },
  {
    cell_id: 149,
    chips_placed: [],
    cell_selectors: ["34"],
    isSelectable: true,
  },
  {
    cell_id: 87,
    chips_placed: [],
    cell_selectors: ["17"],
    isSelectable: true,
  },
]

export const Tiers: SelectableCell[] = [
  {
    cell_id: "Tiers",
    chips_placed: [],
    cell_selectors: [
      "33",
      "16",
      "24",
      "5",
      "10",
      "23",
      "8",
      "30",
      "11",
      "36",
      "13",
      "27",
    ],
    isSelectable: true,
  },
  {
    cell_id: 47,
    chips_placed: [],
    cell_selectors: ["33"],
    isSelectable: true,
  },
  {
    cell_id: 137,
    chips_placed: [],
    cell_selectors: ["16"],
    isSelectable: true,
  },
  {
    cell_id: 41,
    chips_placed: [],
    cell_selectors: ["24"],
    isSelectable: true,
  },
  {
    cell_id: 79,
    chips_placed: [],
    cell_selectors: ["5"],
    isSelectable: true,
  },
  {
    cell_id: 133,
    chips_placed: [],
    cell_selectors: ["10"],
    isSelectable: true,
  },
  {
    cell_id: 91,
    chips_placed: [],
    cell_selectors: ["23"],
    isSelectable: true,
  },
  {
    cell_id: 81,
    chips_placed: [],
    cell_selectors: ["8"],
    isSelectable: true,
  },
  {
    cell_id: 45,
    chips_placed: [],
    cell_selectors: ["30"],
    isSelectable: true,
  },
  {
    cell_id: 83,
    chips_placed: [],
    cell_selectors: ["11"],
    isSelectable: true,
  },
  {
    cell_id: 49,
    chips_placed: [],
    cell_selectors: ["36"],
    isSelectable: true,
  },
  {
    cell_id: 135,
    chips_placed: [],
    cell_selectors: ["13"],
    isSelectable: true,
  },
  {
    cell_id: 43,
    chips_placed: [],
    cell_selectors: ["27"],
    isSelectable: true,
  },
]

export const Voisins: SelectableCell[] = [
  {
    cell_id: "Voisins",
    chips_placed: [],
    cell_selectors: ["22", "18", "29", "7", "28", "25", "2", "21", "4", "19"],
    isSelectable: true,
  },

  {
    cell_id: 141,
    chips_placed: [],
    cell_selectors: ["22"],
    isSelectable: true,
  },
  {
    cell_id: 37,
    chips_placed: [],
    cell_selectors: ["18"],
    isSelectable: true,
  },
  {
    cell_id: 95,
    chips_placed: [],
    cell_selectors: ["29"],
    isSelectable: true,
  },
  {
    cell_id: 131,
    chips_placed: [],
    cell_selectors: ["7"],
    isSelectable: true,
  },
  {
    cell_id: 145,
    chips_placed: [],
    cell_selectors: ["28"],
    isSelectable: true,
  },
  {
    cell_id: 143,
    chips_placed: [],
    cell_selectors: ["25"],
    isSelectable: true,
  },
  {
    cell_id: 77,
    chips_placed: [],
    cell_selectors: ["2"],
    isSelectable: true,
  },
  {
    cell_id: 39,
    chips_placed: [],
    cell_selectors: ["21"],
    isSelectable: true,
  },
  {
    cell_id: 129,
    chips_placed: [],
    cell_selectors: ["4"],
    isSelectable: true,
  },
  {
    cell_id: 139,
    chips_placed: [],
    cell_selectors: ["19"],
    isSelectable: true,
  },
]
