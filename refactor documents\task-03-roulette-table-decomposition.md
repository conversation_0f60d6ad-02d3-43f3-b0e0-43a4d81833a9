# Task 03: Roulette Table Component Decomposition

## Task Overview

**Priority**: Medium-High | **Risk**: High | **Impact**: Very High
**Estimated Duration**: 2 weeks | **Dependencies**: Task 01 (Layout Consolidation)

### Description
Decompose the monolithic `RouletteTable` component by separating rendering logic, interaction handling, and game state management into focused, testable components. This addresses mixed responsibilities, complex event handling, and difficult-to-test behaviors in the core game component.

### Expected Impact
- Improve component testability by 80%
- Enable better code reuse across mobile/desktop
- Simplify maintenance of table interactions
- Reduce coupling between rendering and business logic

## Current State Analysis

### Primary Issues in `src/features/roulette-boards/roulette-table.tsx`

**Lines 20-76**: Mixed interaction and rendering logic
```typescript
const RouletteTable = () => {
  // State management
  const [hoveredCell, setHoveredCell] = useState<SelectableCell | null>(null)
  const tableRef = useRef<HTMLElement | null>(null)

  // Event handlers mixed with component logic
  const handleMouseEnter = useCallback((cell: SelectableCell) => {
    if (Betting && isMountedRef.current) {
      setHoveredCell(cell)
    }
  }, [Betting])

  const handleCellClick = useCallback((cell: SelectableCell) => {
    if (Betting && selectedChip && cell.isSelectable && isMountedRef.current) {
      const num = Math.floor(Math.random() * 2) + 1
      playSound(`chip-${num}` as SoundKeys, false)
      addChip(cell, selectedChip)
    }
  }, [Betting, selectedChip, addChip, playSound])

  // Complex global event handling
  useEffect(() => {
    const handleMouseLeave = (e: MouseEvent) => {
      if (isMountedRef.current && tableRef.current &&
          !tableRef.current.contains(e.target as Node)) {
        setHoveredCell(null)
      }
    }
    document.addEventListener("mousemove", handleMouseLeave)
    return () => document.removeEventListener("mousemove", handleMouseLeave)
  }, [])
```

**Lines 77-168**: Complex special bets rendering with business logic
```typescript
// Special button configuration mixed with rendering
const specialButtonGroups = [
  {
    position: "left-4",
    buttons: [
      {
        id: "voisins",
        specialBet: Voisins,
        label: "Voisins du Zéro",
        className: "bg-gradient-to-r from-yellow-600 to-yellow-500"
      },
      // ... more configuration
    ]
  }
]

// Rendering logic mixed with interaction logic
const renderSpecialButton = (button: SpecialButton) => (
  <button
    key={button.id}
    onClick={() => placeSpecialBet(button.specialBet)}
    className={cn("text-white font-bold py-2 px-4 rounded", button.className)}
    disabled={!Betting || !selectedChip}
  >
    {button.label}
  </button>
)
```

**Lines 170-207**: Conditional mobile/desktop logic intertwined
```typescript
return (
  <div className={cn("relative", STYLES.mainContainer)} ref={tableRef}>
    {gameType === "special" && !isMobile && (
      <>
        {specialButtonGroups.map((group, groupIndex) => (
          // Complex conditional rendering
        ))}
      </>
    )}
    {gameType === "special" &&
      (isMobile ? <MobileSpecialsTable /> : <SpecialsTable />)}

    <div id='normal-table' className={cn(
      "relative w-full h-full",
      gameType === "special" && !isMobile &&
      "absolute z-20 scale-60 top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
    )}>
      <RenderLayer hoveredCell={hoveredCell} />
      <FunctionLayer
        handleCellClick={handleCellClick}
        handleMouseEnter={handleMouseEnter}
      />
    </div>
  </div>
)
```

### Related Component Issues
- `FunctionLayer` and `RenderLayer` are tightly coupled
- `MobileSpecialsTable` and `SpecialsTable` have duplicate logic
- Complex prop drilling between layers
- Difficult to test individual behaviors in isolation

## Target Architecture

### New Component Structure
```
src/features/roulette-boards/
├── RouletteTable.tsx (orchestrator, <100 lines)
├── components/
│   ├── TableContainer.tsx (layout and positioning)
│   ├── TableRenderer.tsx (pure rendering logic)
│   ├── InteractionLayer.tsx (event handling)
│   ├── SpecialBetsOverlay.tsx (special bets UI)
│   ├── MobileTableAdapter.tsx (mobile-specific logic)
│   └── shared/
│       ├── TableTypes.ts (type definitions)
│       └── TableUtils.ts (utility functions)
├── hooks/
│   ├── useTableInteractions.ts (interaction logic)
│   ├── useTableState.ts (state management)
│   ├── useSpecialBets.ts (special bets logic)
│   └── useTableRendering.ts (rendering utilities)
└── config/
    ├── SpecialBetsConfig.ts (special bets configuration)
    └── TableConstants.ts (table-specific constants)
```

### Target RouletteTable Structure
```typescript
const RouletteTable: React.FC = () => {
  const tableState = useTableState()
  const interactions = useTableInteractions(tableState)
  const specialBets = useSpecialBets()
  const isMobile = useMobile()

  return (
    <TableContainer gameType={tableState.gameType} isMobile={isMobile}>
      <SpecialBetsOverlay
        visible={tableState.gameType === "special"}
        isMobile={isMobile}
        specialBets={specialBets}
      />

      {isMobile ? (
        <MobileTableAdapter
          tableState={tableState}
          interactions={interactions}
        />
      ) : (
        <TableRenderer
          tableState={tableState}
          interactions={interactions}
        />
      )}

      <InteractionLayer {...interactions} />
    </TableContainer>
  )
}
```

### Interaction Hook Design
```typescript
interface TableInteractions {
  hoveredCell: SelectableCell | null
  handleCellClick: (cell: SelectableCell) => void
  handleCellHover: (cell: SelectableCell | null) => void
  handleCellLeave: () => void
  isInteractionEnabled: boolean
}

const useTableInteractions = (tableState: TableState): TableInteractions => {
  const [hoveredCell, setHoveredCell] = useState<SelectableCell | null>(null)
  const { addChip, selectedChip } = useBettingStore()
  const { playSound } = useSettingsStore()
  const { Betting } = useRoundPhase()

  const handleCellClick = useCallback((cell: SelectableCell) => {
    if (!Betting || !selectedChip || !cell.isSelectable) return

    playSound(`chip-${Math.floor(Math.random() * 2) + 1}` as SoundKeys, false)
    addChip(cell, selectedChip)
  }, [Betting, selectedChip, addChip, playSound])

  const handleCellHover = useCallback((cell: SelectableCell | null) => {
    if (Betting) setHoveredCell(cell)
  }, [Betting])

  return {
    hoveredCell,
    handleCellClick,
    handleCellHover,
    handleCellLeave: () => setHoveredCell(null),
    isInteractionEnabled: Betting && !!selectedChip
  }
}
```

## Step-by-Step Implementation Plan

### Phase 1: Foundation and Types (Days 1-3)

#### Day 1: Create Type Definitions and Interfaces
- [ ] Create `TableTypes.ts` with comprehensive interfaces
- [ ] Define interaction event types
- [ ] Add table state type definitions
- [ ] Create special bets type definitions

#### Day 2: Extract Table State Management
- [ ] Create `useTableState` hook
- [ ] Extract game type and phase logic
- [ ] Implement table configuration state
- [ ] Add state validation

#### Day 3: Create Interaction Hook
- [ ] Create `useTableInteractions` hook
- [ ] Extract event handling logic from RouletteTable
- [ ] Implement cell interaction management
- [ ] Add interaction state validation

### Phase 2: Component Extraction (Days 4-8)

#### Day 4: Create TableContainer Component
- [ ] Extract layout and positioning logic
- [ ] Implement responsive container behavior
- [ ] Add proper CSS class management
- [ ] Create container prop interfaces

#### Day 5: Extract InteractionLayer Component
- [ ] Create pure interaction handling component
- [ ] Implement event delegation patterns
- [ ] Add touch/mouse event handling
- [ ] Create interaction testing utilities

#### Day 6: Create TableRenderer Component
- [ ] Extract pure rendering logic
- [ ] Implement render layer composition
- [ ] Add rendering optimization
- [ ] Create rendering prop interfaces

#### Day 7: Build SpecialBetsOverlay Component
- [ ] Extract special bets rendering logic
- [ ] Create special bets configuration system
- [ ] Implement overlay positioning
- [ ] Add special bets interaction handling

#### Day 8: Create MobileTableAdapter
- [ ] Extract mobile-specific logic
- [ ] Implement mobile table optimizations
- [ ] Add mobile interaction patterns
- [ ] Create mobile-specific utilities

### Phase 3: Integration and Testing (Days 9-12)

#### Day 9: Integrate New Components
- [ ] Refactor RouletteTable to use new components
- [ ] Implement component composition
- [ ] Add proper prop passing
- [ ] Verify functionality preservation

#### Day 10: Create Special Bets Hook
- [ ] Create `useSpecialBets` hook
- [ ] Extract special bets logic
- [ ] Implement special bets state management
- [ ] Add special bets validation

#### Day 11: Performance Optimization
- [ ] Add proper memoization
- [ ] Optimize re-rendering
- [ ] Implement interaction debouncing
- [ ] Add performance monitoring

#### Day 12: Integration Testing
- [ ] Test component interactions
- [ ] Verify mobile/desktop behavior
- [ ] Test special bets functionality
- [ ] Validate game phase transitions

### Phase 4: Testing and Documentation (Days 13-14)

#### Day 13: Comprehensive Testing
- [ ] Unit tests for all hooks
- [ ] Component integration tests
- [ ] Interaction testing
- [ ] Performance testing

#### Day 14: Documentation and Cleanup
- [ ] Create component documentation
- [ ] Add usage examples
- [ ] Clean up old code
- [ ] Final optimization

## Code Examples

### Before: Monolithic RouletteTable
```typescript
const RouletteTable = () => {
  // 50+ lines of mixed state, effects, and handlers
  const [hoveredCell, setHoveredCell] = useState<SelectableCell | null>(null)
  const tableRef = useRef<HTMLElement | null>(null)

  const handleMouseEnter = useCallback((cell: SelectableCell) => {
    // Interaction logic mixed with component
  }, [Betting])

  const handleCellClick = useCallback((cell: SelectableCell) => {
    // Business logic mixed with event handling
  }, [Betting, selectedChip, addChip, playSound])

  // Complex rendering with mixed concerns
  return (
    <div className={cn("relative", STYLES.mainContainer)} ref={tableRef}>
      {/* 150+ lines of complex conditional rendering */}
    </div>
  )
}
```

### After: Decomposed RouletteTable
```typescript
const RouletteTable: React.FC = () => {
  const tableState = useTableState()
  const interactions = useTableInteractions(tableState)
  const specialBets = useSpecialBets()
  const isMobile = useMobile()

  return (
    <TableContainer gameType={tableState.gameType} isMobile={isMobile}>
      <SpecialBetsOverlay
        visible={tableState.gameType === "special"}
        isMobile={isMobile}
        specialBets={specialBets}
      />

      {isMobile ? (
        <MobileTableAdapter
          tableState={tableState}
          interactions={interactions}
        />
      ) : (
        <TableRenderer
          tableState={tableState}
          interactions={interactions}
        />
      )}

      <InteractionLayer {...interactions} />
    </TableContainer>
  )
}
```

### New InteractionLayer Component
```typescript
interface InteractionLayerProps {
  hoveredCell: SelectableCell | null
  handleCellClick: (cell: SelectableCell) => void
  handleCellHover: (cell: SelectableCell | null) => void
  isInteractionEnabled: boolean
}

export const InteractionLayer: React.FC<InteractionLayerProps> = ({
  hoveredCell,
  handleCellClick,
  handleCellHover,
  isInteractionEnabled
}) => {
  const containerRef = useRef<HTMLDivElement>(null)

  useGlobalMouseLeave(containerRef, () => handleCellHover(null))

  return (
    <div
      ref={containerRef}
      className="absolute inset-0 pointer-events-none"
      data-testid="interaction-layer"
    >
      <FunctionLayer
        onCellClick={handleCellClick}
        onCellHover={handleCellHover}
        disabled={!isInteractionEnabled}
      />
    </div>
  )
}
```

## Testing Strategy

### Unit Tests
```typescript
// useTableInteractions.test.ts
describe('useTableInteractions', () => {
  it('handles cell clicks correctly', () => {
    const mockAddChip = jest.fn()
    const { result } = renderHook(() => useTableInteractions(mockTableState))

    act(() => {
      result.current.handleCellClick(mockSelectableCell)
    })

    expect(mockAddChip).toHaveBeenCalledWith(mockSelectableCell, mockChip)
  })

  it('disables interactions when not betting', () => {
    mockUseRoundPhase.mockReturnValue({ Betting: false })
    const { result } = renderHook(() => useTableInteractions(mockTableState))

    expect(result.current.isInteractionEnabled).toBe(false)
  })
})

// TableRenderer.test.tsx
describe('TableRenderer', () => {
  it('renders table correctly', () => {
    render(<TableRenderer tableState={mockTableState} interactions={mockInteractions} />)

    expect(screen.getByTestId('render-layer')).toBeInTheDocument()
    expect(screen.getByTestId('function-layer')).toBeInTheDocument()
  })
})
```

### Integration Tests
```typescript
// RouletteTable.integration.test.tsx
describe('RouletteTable Integration', () => {
  it('handles complete interaction flow', async () => {
    const user = userEvent.setup()
    render(<RouletteTable />)

    // Test cell hover
    await user.hover(screen.getByTestId('cell-1'))
    expect(screen.getByTestId('cell-1')).toHaveClass('hovered')

    // Test cell click
    await user.click(screen.getByTestId('cell-1'))
    expect(mockAddChip).toHaveBeenCalled()
  })

  it('switches between mobile and desktop correctly', () => {
    const { rerender } = render(<RouletteTable />)

    mockUseMobile.mockReturnValue(true)
    rerender(<RouletteTable />)
    expect(screen.getByTestId('mobile-table-adapter')).toBeInTheDocument()

    mockUseMobile.mockReturnValue(false)
    rerender(<RouletteTable />)
    expect(screen.getByTestId('table-renderer')).toBeInTheDocument()
  })
})
```

### Performance Tests
```typescript
describe('RouletteTable Performance', () => {
  it('renders within performance budget', () => {
    const startTime = performance.now()
    render(<RouletteTable />)
    const endTime = performance.now()

    expect(endTime - startTime).toBeLessThan(16) // 60fps budget
  })

  it('handles rapid interactions efficiently', async () => {
    const user = userEvent.setup()
    render(<RouletteTable />)

    // Simulate rapid cell interactions
    for (let i = 0; i < 100; i++) {
      await user.hover(screen.getByTestId(`cell-${i % 37}`))
    }

    // Should not cause performance issues
    expect(screen.getByTestId('interaction-layer')).toBeInTheDocument()
  })
})
```

## Risk Mitigation

### High-Risk Areas
1. **Event Handling**: Complex mouse/touch interactions
2. **State Synchronization**: Multiple components sharing state
3. **Performance**: Potential re-rendering issues
4. **Mobile Compatibility**: Touch interactions and responsive behavior

### Mitigation Strategies
1. **Gradual Migration**: Implement components one at a time
2. **Feature Flags**: Enable/disable new components
3. **Comprehensive Testing**: Focus on interaction testing
4. **Performance Monitoring**: Benchmark all changes
5. **Rollback Plan**: Maintain original component as backup

### Rollback Procedure
```typescript
const USE_DECOMPOSED_TABLE = import.meta.env.VITE_USE_DECOMPOSED_TABLE === 'true'

const RouletteTable = () => {
  return USE_DECOMPOSED_TABLE ? <NewRouletteTable /> : <LegacyRouletteTable />
}
```

## Success Criteria

### Quantitative Metrics
- [ ] Component testability improved by 80%
- [ ] RouletteTable reduced to <100 lines
- [ ] 5+ focused components created
- [ ] Test coverage >90% for table components
- [ ] No performance regression (<16ms render time)
- [ ] Mobile/desktop parity maintained

### Qualitative Metrics
- [ ] Better separation of concerns
- [ ] Improved code reusability
- [ ] Enhanced maintainability
- [ ] Cleaner component interfaces
- [ ] Better developer experience

### Acceptance Criteria
- [ ] All table interactions work correctly
- [ ] Mobile and desktop behavior preserved
- [ ] Special bets functionality maintained
- [ ] Game phase transitions work properly
- [ ] Performance benchmarks met
- [ ] Accessibility maintained
- [ ] Code review approval
- [ ] QA testing passed

### Validation Checklist
- [ ] Cell click interactions function properly
- [ ] Hover states work on desktop
- [ ] Touch interactions work on mobile
- [ ] Special bets overlay displays correctly
- [ ] Game type switching works
- [ ] Audio feedback plays correctly
- [ ] Visual feedback (chip placement) works
- [ ] Error states handled gracefully
