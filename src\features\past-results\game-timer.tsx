import { motion } from "motion/react"
import React, {
  useEffect,
  useRef,
  useState,
  useMemo,
  useCallback,
  memo,
} from "react"
import { useRoundPhase } from "@/hooks/game/use-game-phases"
import { useGameStateStore } from "@/stores/game-state-store"

// Constants for better readability and maintenance
const TIME_THRESHOLDS = {
  HIGH: 66,
  MEDIUM: 33,
  LOW: 0,
}

const STATUS_MESSAGES = {
  CLOSED: "Betting Closed",
  CLOSING: "Betting Closing",
  OPEN: "Betting Open",
}

const TIMER_COLORS = {
  HIGH: "text-green-500",
  MEDIUM: "text-yellow-500",
  LOW: "text-red-500",
}

const UPDATE_INTERVAL = 100 // ms

interface GameTimerProps {
  initialTime?: number
  showLabel?: boolean
}

// Memoized progress bar component to prevent unnecessary re-renders
const TimerProgressBar = memo<{ percent: number }>(({ percent }) => (
  <div className='relative w-full h-3 bg-black rounded-full overflow-hidden mr-4'>
    <div className='h-full w-full absolute top-0 left-0 bg-gradient-to-r from-red-600 via-yellow-500 to-green-600' />
    <motion.div
      className='h-full absolute top-0 right-0 bg-[#191c20]'
      initial={{ width: "0%" }}
      animate={{ width: `${100 - percent}%` }}
      transition={{ duration: 0.5, ease: "easeOut" }}
    />
  </div>
))

TimerProgressBar.displayName = "TimerProgressBar"

// Memoized timer display component
const TimerDisplay = memo<{ time: number; colorClass: string }>(
  ({ time, colorClass }) => {
    const prevTimeRef = useRef(time)
    const hasChanged = prevTimeRef.current !== time

    useEffect(() => {
      prevTimeRef.current = time
    }, [time])

    return (
      <div
        className={`text-3xl font-bold transition-colors duration-300 ${colorClass}
        flex items-center justify-center rounded-full shadow-lg bg-[#191c20] p-2 aspect-square w-14 h-14`}
        style={{ boxShadow: "inset 0px 0px 5px 5px rgba(0, 0, 0, 0.5)" }}
      >
        <motion.span
          key={time}
          initial={
            hasChanged ? { scale: 1.2, opacity: 0 } : { scale: 1, opacity: 1 }
          }
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.3, ease: "easeOut" }}
        >
          {time}
        </motion.span>
      </div>
    )
  }
)

TimerDisplay.displayName = "TimerDisplay"

// Memoized status label component
const StatusLabel = memo<{ timeRemaining: number; colorClass: string }>(
  ({ timeRemaining, colorClass }) => {
    const statusText = useMemo(() => {
      if (timeRemaining === 0) return STATUS_MESSAGES.CLOSED
      if (timeRemaining <= 2) return STATUS_MESSAGES.CLOSING
      return STATUS_MESSAGES.OPEN
    }, [timeRemaining])

    return (
      <motion.div
        className='mb-1'
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.2, duration: 0.3 }}
      >
        <motion.span
          key={statusText}
          initial={{ y: -10, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          exit={{ y: 10, opacity: 0 }}
          transition={{ duration: 0.2 }}
          className={colorClass}
        >
          {statusText}
        </motion.span>
      </motion.div>
    )
  }
)

StatusLabel.displayName = "StatusLabel"

const GameTimer: React.FC<GameTimerProps> = ({
  initialTime = 30,
  showLabel = true,
}) => {
  const { Betting, closeTime } = useRoundPhase()
  const roundData = useGameStateStore((state) => state.roundData)

  const [timerState, setTimerState] = useState({
    timeRemaining: initialTime,
    percentRemaining: 100,
  })

  const timerRef = useRef<NodeJS.Timeout | null>(null)
  const startTimeRef = useRef<number | null>(null)

  // Memoize color class to prevent recalculation on each render
  const timerColorClass = useMemo(() => {
    const { percentRemaining } = timerState
    if (percentRemaining > TIME_THRESHOLDS.HIGH) return TIMER_COLORS.HIGH
    if (percentRemaining > TIME_THRESHOLDS.MEDIUM) return TIMER_COLORS.MEDIUM
    return TIMER_COLORS.LOW
  }, [timerState.percentRemaining])

  // Use callback for timer update to ensure it doesn't change on each render
  const updateTimer = useCallback(() => {
    if (!closeTime || !startTimeRef.current) return

    const currentTime = new Date().getTime()
    const remainingMs = Math.max(0, closeTime - currentTime)
    const remainingSec = Math.ceil(remainingMs / 1000)

    const percent = Math.max(
      0,
      Math.min(100, (remainingSec / startTimeRef.current) * 100)
    )

    setTimerState({
      timeRemaining: remainingSec,
      percentRemaining: percent,
    })

    if (remainingMs <= 0 && timerRef.current) {
      clearInterval(timerRef.current)
      timerRef.current = null
    }
  }, [closeTime])

  useEffect(() => {
    if (timerRef.current) {
      clearInterval(timerRef.current)
      timerRef.current = null
    }

    if (!Betting || !closeTime || !roundData) {
      setTimerState({
        timeRemaining: 0,
        percentRemaining: 0,
      })
      return
    }

    const now = new Date().getTime()
    const initialTimeMs = Math.max(0, closeTime - now)
    const initialTimeSec = Math.ceil(initialTimeMs / 1000)

    setTimerState({
      timeRemaining: initialTimeSec,
      percentRemaining: 100,
    })

    startTimeRef.current = initialTimeSec

    // Set up interval for timer updates
    timerRef.current = setInterval(updateTimer, UPDATE_INTERVAL)

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current)
        timerRef.current = null
      }
    }
  }, [Betting, closeTime, roundData, updateTimer])

  const { timeRemaining, percentRemaining } = timerState

  return (
    <motion.div
      className='flex justify-between items-center p-2 bg-[#292f36] rounded-xl border shadow-2xl border-green-900/50'
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      style={{ boxShadow: "0px 0px 5px 5px rgba(0, 0, 0, 0.5)" }}
    >
      <div className='px-2 pb-1 text-2xl leading-tight font-bold uppercase tracking-wide w-full'>
        {showLabel && (
          <StatusLabel
            timeRemaining={timeRemaining}
            colorClass={timerColorClass}
          />
        )}
        <TimerProgressBar percent={percentRemaining} />
      </div>
      <TimerDisplay time={timeRemaining} colorClass={timerColorClass} />
    </motion.div>
  )
}

export default memo(GameTimer)
