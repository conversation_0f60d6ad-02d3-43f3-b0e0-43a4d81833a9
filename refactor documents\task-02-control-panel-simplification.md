# Task 02: Control Panel Architecture Simplification

## Task Overview

**Priority**: High | **Risk**: Medium | **Impact**: High  
**Estimated Duration**: 2 weeks | **Dependencies**: Task 01 (Layout Consolidation)

### Description
Refactor the complex control panel system by replacing the 210+ line `useControlPanels` hook with a declarative configuration system. This addresses repetitive button configuration, scattered state management, and duplicate rendering logic across different panel types.

### Expected Impact
- Reduce `useControlPanels` hook from 210+ to <50 lines (75% reduction)
- Improve button configuration reusability by 60%
- Simplify adding new control buttons by 50%
- Enable better testing and maintenance of control panel logic

## Current State Analysis

### Primary Issues in `src/hooks/game/use-control-panels.tsx`

**Lines 97-107**: Repetitive bet options configuration
```typescript
const betOptionsButtons = useMemo<ControlPanelButton[]>(
  () => [
    {
      id: "repeat",
      src: "/assets/images/nav-buttons/Repeat.svg",
      alt: "Repeat Last Bet",
      onClick: () => handleBetAction("repeat"),
      disabled: !Betting || autoplayActive,
    },
    // ... 8 more similar button configurations
  ],
  [handleBetAction, Betting, autoplayActive, showHotCold, showPastNumbers]
)
```

**Lines 110-125**: Chips configuration that could be generated
```typescript
const chipsButtons = useMemo<ControlPanelButton[]>(
  () =>
    rouletteChips.map((chip) => ({
      id: `chip-${chip.value}`,
      src: chip.src,
      alt: `${chip.value} Credits`,
      onClick: () => {
        useBettingStore.getState().setSelectedChip(chip)
      },
      value: chip.value,
      isChip: true,
      active: selectedChip?.value === chip.value,
      disabled: !Betting,
    })),
  [selectedChip, Betting]
)
```

**Lines 139-210**: Mixed concerns in controls configuration
- Special bet placement logic
- Game type toggles
- State-dependent button configurations

### Related Issues
- Button state management spread across multiple components
- Duplicate button rendering logic in `ControlPanel` component
- Complex dependency arrays causing unnecessary re-renders
- Difficult to add new button types or modify existing ones

## Target Architecture

### New File Structure
```
src/features/bottom-navigation/
├── control-panel.tsx (simplified component)
├── config/
│   ├── ControlPanelConfig.ts (declarative configuration)
│   ├── ButtonGroupFactories.ts (button creation utilities)
│   ├── ButtonStateManagers.ts (state management logic)
│   └── ControlPanelTypes.ts (type definitions)
├── hooks/
│   ├── useControlPanels.ts (simplified hook <50 lines)
│   ├── useButtonGroups.ts (button group management)
│   └── useControlPanelState.ts (state management)
└── utils/
    ├── ButtonConfigUtils.ts (configuration utilities)
    └── ControlPanelValidation.ts (validation logic)
```

### Declarative Configuration System
```typescript
// ControlPanelConfig.ts
export const CONTROL_PANEL_CONFIG = {
  betOptions: {
    type: 'bet-actions',
    buttons: [
      { id: 'repeat', action: 'repeat', icon: 'Repeat.svg' },
      { id: 'undo', action: 'undo', icon: 'Undo.svg' },
      { id: 'clear', action: 'clear', icon: 'Clear.svg' },
      { id: 'double', action: 'double', icon: 'Double.svg' },
      { id: 'max-bet', action: 'max-bet', icon: 'Max Bet.svg' }
    ],
    stateSelectors: ['Betting', 'autoplayActive'],
    enabledWhen: (state) => state.Betting && !state.autoplayActive
  },
  
  chips: {
    type: 'selection',
    source: 'rouletteChips',
    selectionHandler: 'setSelectedChip',
    stateSelector: 'selectedChip',
    enabledWhen: (state) => state.Betting
  },
  
  controls: {
    type: 'mixed',
    groups: [
      {
        id: 'game-mode',
        buttons: [{ id: 'specials', toggle: 'gameType', icon: 'Specials.svg' }]
      },
      {
        id: 'special-bets',
        buttons: [
          { id: 'big-series', specialBet: 'Voisins', icon: 'Big Series.svg' },
          { id: 'small-series', specialBet: 'Tiers', icon: 'Small Series.svg' },
          { id: 'orphans', specialBet: 'Orphalins', icon: 'Orphans.svg' },
          { id: 'zero-spiel', specialBet: 'Zeros', icon: 'Zero Spiel.svg' }
        ],
        enabledWhen: (state) => state.Betting
      }
    ]
  }
} as const
```

### Simplified Hook Implementation
```typescript
// useControlPanels.ts (target <50 lines)
export const useControlPanels = () => {
  const gameState = useGameState()
  const bettingActions = useBettingActions()
  const controlPanelState = useControlPanelState()
  
  return useMemo(() => {
    const context = { gameState, bettingActions, controlPanelState }
    
    return {
      betOptionsButtons: createButtonGroup('betOptions', context),
      chipsButtons: createButtonGroup('chips', context),
      controlsButtons: createButtonGroup('controls', context)
    }
  }, [gameState, bettingActions, controlPanelState])
}
```

## Step-by-Step Implementation Plan

### Phase 1: Configuration System (Days 1-3)

#### Day 1: Create Type Definitions
- [ ] Create `ControlPanelTypes.ts` with comprehensive interfaces
- [ ] Define button configuration types
- [ ] Add state management type definitions
- [ ] Create validation schemas

#### Day 2: Build Configuration System
- [ ] Create `ControlPanelConfig.ts` with declarative configuration
- [ ] Implement button group definitions
- [ ] Add state selector configurations
- [ ] Create configuration validation

#### Day 3: Create Button Factories
- [ ] Create `ButtonGroupFactories.ts`
- [ ] Implement `createButtonGroup` function
- [ ] Add button state management utilities
- [ ] Create button rendering helpers

### Phase 2: State Management (Days 4-6)

#### Day 4: Extract State Management
- [ ] Create `useControlPanelState` hook
- [ ] Extract game state selectors
- [ ] Implement button state calculations
- [ ] Add state memoization

#### Day 5: Create Button Group Hook
- [ ] Create `useButtonGroups` hook
- [ ] Implement button group management
- [ ] Add button interaction handlers
- [ ] Create button validation logic

#### Day 6: Build Action Handlers
- [ ] Create `ButtonStateManagers.ts`
- [ ] Implement betting action handlers
- [ ] Add chip selection logic
- [ ] Create special bet handlers

### Phase 3: Integration (Days 7-10)

#### Day 7-8: Refactor useControlPanels Hook
- [ ] Simplify existing hook to use new system
- [ ] Remove repetitive configuration code
- [ ] Implement new button group creation
- [ ] Add comprehensive memoization

#### Day 9: Update ControlPanel Component
- [ ] Modify component to use new configuration
- [ ] Optimize rendering performance
- [ ] Add better error handling
- [ ] Implement accessibility improvements

#### Day 10: Integration Testing
- [ ] Test all button interactions
- [ ] Verify state management
- [ ] Check performance improvements
- [ ] Validate accessibility

### Phase 4: Testing and Documentation (Days 11-14)

#### Day 11-12: Comprehensive Testing
- [ ] Unit tests for all new utilities
- [ ] Integration tests for button interactions
- [ ] Performance testing
- [ ] Accessibility testing

#### Day 13-14: Documentation and Cleanup
- [ ] Create configuration documentation
- [ ] Add code examples and guides
- [ ] Clean up old code
- [ ] Final performance optimization

## Code Examples

### Before: Current useControlPanels Hook
```typescript
export const useControlPanels = () => {
  // 50+ lines of state selectors and dependencies
  
  const betOptionsButtons = useMemo<ControlPanelButton[]>(
    () => [
      {
        id: "repeat",
        src: "/assets/images/nav-buttons/Repeat.svg",
        alt: "Repeat Last Bet",
        onClick: () => handleBetAction("repeat"),
        disabled: !Betting || autoplayActive,
      },
      {
        id: "undo",
        src: "/assets/images/nav-buttons/Undo.svg",
        alt: "Undo Last Bet",
        onClick: () => handleBetAction("undo"),
        disabled: !Betting || autoplayActive,
      },
      // ... 6 more repetitive configurations
    ],
    [handleBetAction, Betting, autoplayActive, showHotCold, showPastNumbers]
  )
  
  // 100+ more lines of similar repetitive code
  
  return { betOptionsButtons, chipsButtons, controlsButtons }
}
```

### After: Simplified Hook with Configuration
```typescript
export const useControlPanels = () => {
  const context = useControlPanelContext()
  
  return useMemo(() => ({
    betOptionsButtons: createButtonGroup('betOptions', context),
    chipsButtons: createButtonGroup('chips', context),
    controlsButtons: createButtonGroup('controls', context)
  }), [context])
}

// Button group factory
const createButtonGroup = (groupId: string, context: ControlPanelContext) => {
  const config = CONTROL_PANEL_CONFIG[groupId]
  return ButtonGroupFactory.create(config, context)
}
```

### New Button Group Factory
```typescript
// ButtonGroupFactories.ts
export class ButtonGroupFactory {
  static create(config: ButtonGroupConfig, context: ControlPanelContext): ControlPanelButton[] {
    switch (config.type) {
      case 'bet-actions':
        return this.createBetActionButtons(config, context)
      case 'selection':
        return this.createSelectionButtons(config, context)
      case 'mixed':
        return this.createMixedButtons(config, context)
      default:
        throw new Error(`Unknown button group type: ${config.type}`)
    }
  }
  
  private static createBetActionButtons(
    config: BetActionConfig, 
    context: ControlPanelContext
  ): ControlPanelButton[] {
    return config.buttons.map(buttonConfig => ({
      id: buttonConfig.id,
      src: `/assets/images/nav-buttons/${buttonConfig.icon}`,
      alt: this.generateAltText(buttonConfig),
      onClick: () => context.bettingActions[buttonConfig.action](),
      disabled: !config.enabledWhen(context.gameState)
    }))
  }
}
```

## Testing Strategy

### Unit Tests
```typescript
// ButtonGroupFactory.test.ts
describe('ButtonGroupFactory', () => {
  it('creates bet action buttons correctly', () => {
    const config = CONTROL_PANEL_CONFIG.betOptions
    const context = createMockContext({ Betting: true, autoplayActive: false })
    
    const buttons = ButtonGroupFactory.create(config, context)
    
    expect(buttons).toHaveLength(5)
    expect(buttons[0]).toMatchObject({
      id: 'repeat',
      disabled: false
    })
  })
  
  it('disables buttons when betting is not active', () => {
    const config = CONTROL_PANEL_CONFIG.betOptions
    const context = createMockContext({ Betting: false })
    
    const buttons = ButtonGroupFactory.create(config, context)
    
    buttons.forEach(button => {
      expect(button.disabled).toBe(true)
    })
  })
})

// useControlPanels.test.ts
describe('useControlPanels', () => {
  it('returns all button groups', () => {
    const { result } = renderHook(() => useControlPanels())
    
    expect(result.current).toHaveProperty('betOptionsButtons')
    expect(result.current).toHaveProperty('chipsButtons')
    expect(result.current).toHaveProperty('controlsButtons')
  })
  
  it('memoizes button groups correctly', () => {
    const { result, rerender } = renderHook(() => useControlPanels())
    const firstResult = result.current
    
    rerender()
    
    expect(result.current).toBe(firstResult)
  })
})
```

### Integration Tests
```typescript
// ControlPanel.integration.test.tsx
describe('Control Panel Integration', () => {
  it('handles bet action button clicks', async () => {
    const mockHandleBetAction = jest.fn()
    render(<ControlPanel buttons={betOptionsButtons} />)
    
    await user.click(screen.getByRole('button', { name: /repeat/i }))
    
    expect(mockHandleBetAction).toHaveBeenCalledWith('repeat')
  })
  
  it('updates button states based on game phase', () => {
    const { rerender } = render(<ControlPanel buttons={betOptionsButtons} />)
    
    // Test betting phase
    mockUseRoundPhase.mockReturnValue({ Betting: true })
    rerender(<ControlPanel buttons={betOptionsButtons} />)
    
    expect(screen.getByRole('button', { name: /repeat/i })).not.toBeDisabled()
    
    // Test non-betting phase
    mockUseRoundPhase.mockReturnValue({ Betting: false })
    rerender(<ControlPanel buttons={betOptionsButtons} />)
    
    expect(screen.getByRole('button', { name: /repeat/i })).toBeDisabled()
  })
})
```

### Performance Tests
```typescript
// Performance benchmarks
describe('Control Panel Performance', () => {
  it('renders button groups within performance budget', () => {
    const startTime = performance.now()
    
    render(<ControlPanel buttons={generateLargeButtonSet(100)} />)
    
    const endTime = performance.now()
    expect(endTime - startTime).toBeLessThan(16) // 60fps budget
  })
})
```

## Risk Mitigation

### Potential Issues
1. **Button State Synchronization**: Complex state dependencies
2. **Performance Regression**: Over-abstraction causing re-renders
3. **Configuration Complexity**: Making simple things complicated

### Mitigation Strategies
1. **Gradual Migration**: Implement one button group at a time
2. **Performance Monitoring**: Benchmark before/after changes
3. **Fallback System**: Keep original implementation as backup
4. **Configuration Validation**: Runtime validation of configurations

### Rollback Procedure
```typescript
// Feature flag for gradual rollout
const USE_NEW_CONTROL_PANELS = import.meta.env.VITE_USE_NEW_CONTROL_PANELS === 'true'

export const useControlPanels = () => {
  return USE_NEW_CONTROL_PANELS 
    ? useNewControlPanels() 
    : useLegacyControlPanels()
}
```

## Success Criteria

### Quantitative Metrics
- [ ] useControlPanels hook reduced from 210+ to <50 lines (75% reduction)
- [ ] Button configuration time reduced by 50%
- [ ] Test coverage >90% for control panel system
- [ ] No performance regression (<16ms render time)
- [ ] Memory usage improvement (reduce object allocations)

### Qualitative Metrics
- [ ] Simplified button addition process
- [ ] Better separation of concerns
- [ ] Improved maintainability
- [ ] Enhanced developer experience
- [ ] Cleaner, more readable code

### Acceptance Criteria
- [ ] All existing button functionality preserved
- [ ] Button state management works correctly
- [ ] Game phase transitions handled properly
- [ ] Special bet buttons function correctly
- [ ] Chip selection works as expected
- [ ] Performance benchmarks met
- [ ] Code review approval
- [ ] QA testing passed
