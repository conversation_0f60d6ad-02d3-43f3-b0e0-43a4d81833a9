import { useEffect } from "react"
import { useRoundPhase } from "@/hooks"
import { useHlsPlayer } from "@/hooks/streaming"
import { useSignalR } from "@/hooks/streaming/use-stream-connection"
import { EnhancedErrorBoundary, logger } from "@/middleware"

interface VideoOverlayProps {
  message: string
  isError?: boolean
}

const VideoOverlay: React.FC<VideoOverlayProps> = ({ message, isError }) => (
  <div className='absolute inset-0 flex items-center justify-center bg-black/40'>
    {isError ? (
      <div className='rounded bg-red-500 p-4 text-white'>{message}</div>
    ) : (
      <div className='h-12 w-12 animate-spin rounded-full border-b-2 border-t-2 border-blue-500' />
    )}
  </div>
)

const VideoPlayerInner: React.FC = () => {
  const { Betting } = useRoundPhase()

  const { setupVideo, cleanup, videoRef, isBuffering, error } = useHlsPlayer()

  useSignalR(setupVideo, cleanup)

  useEffect(() => {
    logger.debug("Video player rendered", {
      context: "VideoPlayer",
      data: {
        isBetting: Betting,
        hasError: !!error,
        isBuffering,
      },
    })
  }, [Betting, error, isBuffering])

  const containerClassName =
    "relative lg:w-[110%] w-[130%] h-auto bg-black transition-opacity duration-500 left-1/2 -translate-x-1/2 lg:top-1/2 lg:-translate-y-1/2 -top-10"

  useEffect(() => {
    if (error) {
      logger.error("Video player error", null, {
        context: "VideoPlayer",
        data: { error },
      })
    }
  }, [error])

  return (
    <div className={containerClassName}>
      <video
        ref={videoRef}
        controls={false}
        muted={false}
        className='h-auto w-full'
        playsInline
        preload='auto'
      />
      {/* {error && <VideoOverlay message={error} isError />} */}
    </div>
  )
}

const VideoPlayer: React.FC = () => {
  return (
    <EnhancedErrorBoundary context='VideoPlayer'>
      <VideoPlayerInner />
    </EnhancedErrorBoundary>
  )
}

export default VideoPlayer
