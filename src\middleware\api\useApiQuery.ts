import {
  useQuery,
  useMutation,
  UseMutationOptions,
  UseQueryOptions,
} from "@tanstack/react-query"

import { toastService } from "../ui/toastMiddleware"
import { apiClient } from "./apiClient"

// Type for API query options
export interface ApiQueryOptions<TData, TError>
  extends Omit<UseQueryOptions<TData, TError, TData>, "queryKey" | "queryFn"> {
  showErrorToast?: boolean
  errorToastTitle?: string
  measurePerformance?: boolean
  onSuccess?: (data: TData) => void
}

// Type for API mutation options
export interface ApiMutationOptions<TData, TVariables, TError>
  extends Omit<UseMutationOptions<TData, TError, TVariables>, "mutationFn"> {
  showErrorToast?: boolean
  showSuccessToast?: boolean
  errorToastTitle?: string
  successToastTitle?: string
  successToastMessage?: string
  measurePerformance?: boolean
}

// Custom hook for API queries
export const useApiQuery = <TData = unknown, TError = unknown>(
  queryKey: string[],
  endpoint: string,
  params?: Record<string, string | number | boolean | undefined> | null,
  headers?: Record<string, string>,
  options?: ApiQueryOptions<TData, TError>
) => {
  const {
    showErrorToast = true,
    errorToastTitle = "Error",
    ...queryOptions
  } = options || {}

  return useQuery<TData, TError>({
    queryKey,
    queryFn: async () => {
      const queryFn = async () => {
        const [data, error] = await apiClient.get<TData>(
          endpoint,
          params,
          headers
        )

        if (error) {
          if (showErrorToast) {
            toastService.error(
              errorToastTitle,
              error.message || "An error occurred while fetching data"
            )
          }
          throw error
        }

        return data as TData
      }
      return queryFn()
    },
    ...queryOptions,
  })
}

// Custom hook for API mutations
export const useApiMutation = <
  TData = unknown,
  TVariables = unknown,
  TError = unknown
>(
  mutationKey: string[],
  endpoint: string,
  method: "post" | "put" | "patch" | "delete" = "post",
  headers?: Record<string, string>,
  options?: ApiMutationOptions<TData, TVariables, TError>
) => {
  const {
    showErrorToast = true,
    showSuccessToast = false,
    errorToastTitle = "Error",
    successToastTitle = "Success",
    successToastMessage = "Operation completed successfully",
    ...mutationOptions
  } = options || {}

  return useMutation<TData, TError, TVariables>({
    mutationKey,
    mutationFn: async (variables) => {
      const mutationFn = async () => {
        const [data, error] =
          method === "delete"
            ? await apiClient.delete<TData>(endpoint, headers)
            : await apiClient[method]<TData>(endpoint, variables, headers)

        if (error) {
          if (showErrorToast) {
            toastService.error(
              errorToastTitle,
              error.message || "An error occurred while processing your request"
            )
          }
          throw error
        }

        if (showSuccessToast) {
          toastService.success(successToastTitle, successToastMessage)
        }

        return data as TData
      }

      return mutationFn()
    },
    ...mutationOptions,
  })
}
