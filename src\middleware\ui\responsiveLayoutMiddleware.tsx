import React, { createContext, useContext, useEffect, useState } from "react"
import { logger } from "@/lib/logger"

// Define breakpoints
export const BREAKPOINTS = {
  xs: 0,
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  "2xl": 1536,
}

export type Breakpoint = keyof typeof BREAKPOINTS

// Define layout context
interface LayoutContextType {
  width: number
  height: number
  breakpoint: Breakpoint
  isMobile: boolean
  isTablet: boolean
  isDesktop: boolean
  isLandscape: boolean
  isPortrait: boolean
}

const LayoutContext = createContext<LayoutContextType>({
  width: typeof window !== "undefined" ? window.innerWidth : 0,
  height: typeof window !== "undefined" ? window.innerHeight : 0,
  breakpoint: "xs",
  isMobile: false,
  isTablet: false,
  isDesktop: false,
  isLandscape: false,
  isPortrait: true,
})

// Get current breakpoint based on width
const getBreakpoint = (width: number): Breakpoint => {
  if (width >= BREAKPOINTS["2xl"]) return "2xl"
  if (width >= BREAKPOINTS.xl) return "xl"
  if (width >= BREAKPOINTS.lg) return "lg"
  if (width >= BREAKPOINTS.md) return "md"
  if (width >= BREAKPOINTS.sm) return "sm"
  return "xs"
}

interface ResponsiveLayoutProviderProps {
  children: React.ReactNode
  debounceTime?: number
}

export const ResponsiveLayoutProvider: React.FC<
  ResponsiveLayoutProviderProps
> = ({ children, debounceTime = 100 }) => {
  // Initialize with window dimensions or fallback values
  const [dimensions, setDimensions] = useState<{
    width: number
    height: number
  }>({
    width: typeof window !== "undefined" ? window.innerWidth : 0,
    height: typeof window !== "undefined" ? window.innerHeight : 0,
  })

  useEffect(() => {
    // Skip in SSR
    if (typeof window === "undefined") return

    let timeoutId: number | null = null

    const handleResize = () => {
      // Debounce resize events
      if (timeoutId) {
        clearTimeout(timeoutId)
      }

      timeoutId = window.setTimeout(() => {
        const width = window.innerWidth
        const height = window.innerHeight

        setDimensions({ width, height })

        // Log dimension changes in development
        if (import.meta.env.DEV) {
          logger.debug("Viewport dimensions changed", {
            context: "Layout",
            data: {
              width,
              height,
              breakpoint: getBreakpoint(width),
              orientation: width > height ? "landscape" : "portrait",
            },
          })
        }
      }, debounceTime) as unknown as number
    }

    // Set initial dimensions
    handleResize()

    // Add event listener
    window.addEventListener("resize", handleResize)

    // Clean up
    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId)
      }
      window.removeEventListener("resize", handleResize)
    }
  }, [debounceTime])

  // Compute derived values
  const { width, height } = dimensions
  const breakpoint = getBreakpoint(width)
  const isLandscape = width > height
  const isPortrait = !isLandscape

  // Device type based on breakpoint
  const isMobile = breakpoint === "xs" || breakpoint === "sm"
  const isTablet = breakpoint === "md" || breakpoint === "lg"
  const isDesktop = breakpoint === "xl" || breakpoint === "2xl"

  const contextValue: LayoutContextType = {
    width,
    height,
    breakpoint,
    isMobile,
    isTablet,
    isDesktop,
    isLandscape,
    isPortrait,
  }

  return (
    <LayoutContext.Provider value={contextValue}>
      {children}
    </LayoutContext.Provider>
  )
}

// Hook to use the layout context
export const useResponsiveLayout = () => {
  const context = useContext(LayoutContext)

  if (context === undefined) {
    throw new Error(
      "useResponsiveLayout must be used within a ResponsiveLayoutProvider"
    )
  }

  return context
}

// Component to conditionally render based on breakpoint
interface ResponsiveProps {
  children: React.ReactNode
  breakpoint: Breakpoint
  operator?: "up" | "down" | "only"
}

export const Responsive: React.FC<ResponsiveProps> = ({
  children,
  breakpoint,
  operator = "up",
}) => {
  const { width } = useResponsiveLayout()
  const currentBreakpoint = getBreakpoint(width)

  const breakpointValue = BREAKPOINTS[breakpoint]
  // const currentBreakpointValue = BREAKPOINTS[currentBreakpoint]

  let shouldRender = false

  switch (operator) {
    case "up":
      shouldRender = width >= breakpointValue
      break
    case "down":
      shouldRender =
        width <
        (BREAKPOINTS[
          Object.keys(BREAKPOINTS).find(
            (key) => BREAKPOINTS[key as Breakpoint] > breakpointValue
          ) as Breakpoint
        ] || Infinity)
      break
    case "only":
      shouldRender = currentBreakpoint === breakpoint
      break
  }

  return shouldRender ? <>{children}</> : null
}

// Component to conditionally render based on device type
interface DeviceProps {
  children: React.ReactNode
  device: "mobile" | "tablet" | "desktop"
}

export const Device: React.FC<DeviceProps> = ({ children, device }) => {
  const { isMobile, isTablet, isDesktop } = useResponsiveLayout()

  let shouldRender = false

  switch (device) {
    case "mobile":
      shouldRender = isMobile
      break
    case "tablet":
      shouldRender = isTablet
      break
    case "desktop":
      shouldRender = isDesktop
      break
  }

  return shouldRender ? <>{children}</> : null
}

// Component to conditionally render based on orientation
interface OrientationProps {
  children: React.ReactNode
  orientation: "landscape" | "portrait"
}

export const Orientation: React.FC<OrientationProps> = ({
  children,
  orientation,
}) => {
  const { isLandscape, isPortrait } = useResponsiveLayout()

  const shouldRender =
    (orientation === "landscape" && isLandscape) ||
    (orientation === "portrait" && isPortrait)

  return shouldRender ? <>{children}</> : null
}
