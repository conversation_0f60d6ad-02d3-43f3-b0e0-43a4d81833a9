"use client"

import { useState, useEffect, useRef } from "react"
import { useMobile } from "./use-mobile"

type TextSizeClass =
  | "text-xs"
  | "text-sm"
  | "text-base"
  | "text-lg"
  | "text-xl"
  | "text-2xl"
  | "text-3xl"
  | "text-4xl"
  | "text-5xl"

interface TextSizeConfig {
  numbers: TextSizeClass
  dozen: TextSizeClass
  evenMoney: TextSizeClass
  column: TextSizeClass
  zero: TextSizeClass
  configTag: string // Tag to identify which configuration is being used
}

/**
 * Custom hook to calculate appropriate text sizes for the roulette table
 * based on container dimensions and screen size
 *
 * @returns An object with text size classes for different elements and a config tag
 */
export function useTableTextSize() {
  const [textSizes, setTextSizes] = useState<TextSizeConfig>({
    numbers: "text-xl",
    dozen: "text-lg",
    evenMoney: "text-sm",
    column: "text-sm",
    zero: "text-2xl",
    configTag: "default",
  })
  const isMobile = useMobile()
  const containerRef = useRef<HTMLDivElement | null>(null)

  useEffect(() => {
    const updateTextSizes = () => {
      if (!containerRef.current) return

      // Get container dimensions
      const containerWidth = containerRef.current.clientWidth
      const containerHeight = containerRef.current.clientHeight
      const smallerDimension = Math.min(containerWidth, containerHeight)

      // Calculate appropriate text sizes based on container dimensions
      let numberSize: TextSizeClass
      let dozenSize: TextSizeClass
      let evenMoneySize: TextSizeClass
      let columnSize: TextSizeClass
      let zeroSize: TextSizeClass

      let configTag = "default"

      if (isMobile) {
        // Mobile sizing logic - more constrained
        if (smallerDimension < 300) {
          numberSize = "text-xs"
          dozenSize = "text-xs"
          evenMoneySize = "text-xs"
          columnSize = "text-xs"
          zeroSize = "text-lg"
          configTag = "mobile-small"
        } else if (smallerDimension < 400) {
          numberSize = "text-sm"
          dozenSize = "text-xs"
          evenMoneySize = "text-xs"
          columnSize = "text-xs"
          zeroSize = "text-xl"
          configTag = "mobile-medium"
        } else {
          numberSize = "text-base"
          dozenSize = "text-base"
          evenMoneySize = "text-sm"
          columnSize = "text-sm"
          zeroSize = "text-2xl"
          configTag = "mobile-large"
        }
      } else {
        // Desktop sizing logic - with consistent sizing
        if (smallerDimension < 400) {
          // For very small desktop containers
          numberSize = "text-3xl"
          dozenSize = "text-3xl"
          evenMoneySize = "text-3xl"
          columnSize = "text-3xl"
          zeroSize = "text-3xl"
          configTag = "desktop-small"
        } else if (smallerDimension < 600) {
          // For medium desktop containers
          numberSize = "text-4xl"
          dozenSize = "text-4xl"
          evenMoneySize = "text-4xl"
          columnSize = "text-4xl"
          zeroSize = "text-4xl"
          configTag = "desktop-medium"
        } else {
          // For large desktop containers (600px and above)
          numberSize = "text-5xl"
          dozenSize = "text-5xl"
          evenMoneySize = "text-5xl"
          columnSize = "text-5xl"
          zeroSize = "text-5xl"
          configTag = "desktop-large"
        }
      }

      setTextSizes({
        numbers: numberSize,
        dozen: dozenSize,
        evenMoney: evenMoneySize,
        column: columnSize,
        zero: zeroSize,
        configTag,
      })
    }

    // Initial update
    updateTextSizes()

    // Update on resize
    const resizeObserver = new ResizeObserver(updateTextSizes)
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current)
    }

    // Also update on window resize
    const handleWindowResize = () => {
      requestAnimationFrame(updateTextSizes)
    }
    window.addEventListener("resize", handleWindowResize)

    return () => {
      resizeObserver.disconnect()
      window.removeEventListener("resize", handleWindowResize)
    }
  }, [isMobile])

  return { textSizes, containerRef }
}
