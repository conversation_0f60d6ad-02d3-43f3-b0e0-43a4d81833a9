#!/usr/bin/env node

/**
 * Environment Setup Script
 * 
 * This script helps manage environment files and provides utilities
 * for switching between different environments during development.
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)
const rootDir = path.resolve(__dirname, '..')

// Environment file mappings
const ENV_FILES = {
  local: '.env.local',
  stg: '.env.stg',
  staging: '.env.stg',
  prod: '.env.prod',
  production: '.env.prod'
}

// Available browsers
const BROWSERS = ['chrome', 'firefox', 'edge', 'zen', 'safari']

/**
 * Display help information
 */
function showHelp() {
  console.log(`
🎯 Environment Setup Script

Usage:
  node scripts/env-setup.js [command] [options]

Commands:
  list                    List all available environment files
  copy <env> [target]     Copy environment file to .env.local
  validate <env>          Validate environment file
  info <env>              Show environment file information
  help                    Show this help message

Examples:
  node scripts/env-setup.js list
  node scripts/env-setup.js copy stg
  node scripts/env-setup.js copy prod .env.local
  node scripts/env-setup.js validate local
  node scripts/env-setup.js info stg

Available Environments:
  ${Object.keys(ENV_FILES).join(', ')}

Available Browsers (for dev scripts):
  ${BROWSERS.join(', ')}

NPM Scripts Quick Reference:
  pnpm dev                    # Local development with Chrome
  pnpm dev:stg               # Staging environment with Chrome
  pnpm dev:firefox:stg       # Staging environment with Firefox
  pnpm build:stg             # Build for staging
  pnpm build:prod            # Build for production
`)
}

/**
 * List all available environment files
 */
function listEnvironments() {
  console.log('\n📋 Available Environment Files:\n')
  
  Object.entries(ENV_FILES).forEach(([env, file]) => {
    const filePath = path.join(rootDir, file)
    const exists = fs.existsSync(filePath)
    const status = exists ? '✅' : '❌'
    const size = exists ? `(${fs.statSync(filePath).size} bytes)` : '(missing)'
    
    console.log(`  ${status} ${env.padEnd(12)} → ${file} ${size}`)
  })
  
  console.log('\n💡 Use "copy" command to set up your local environment')
}

/**
 * Copy environment file
 */
function copyEnvironment(sourceEnv, targetFile = '.env.local') {
  const sourceFile = ENV_FILES[sourceEnv]
  
  if (!sourceFile) {
    console.error(`❌ Unknown environment: ${sourceEnv}`)
    console.log(`Available environments: ${Object.keys(ENV_FILES).join(', ')}`)
    process.exit(1)
  }
  
  const sourcePath = path.join(rootDir, sourceFile)
  const targetPath = path.join(rootDir, targetFile)
  
  if (!fs.existsSync(sourcePath)) {
    console.error(`❌ Source file not found: ${sourceFile}`)
    process.exit(1)
  }
  
  try {
    fs.copyFileSync(sourcePath, targetPath)
    console.log(`✅ Copied ${sourceFile} → ${targetFile}`)
    console.log(`💡 You can now run: pnpm dev`)
  } catch (error) {
    console.error(`❌ Failed to copy file: ${error.message}`)
    process.exit(1)
  }
}

/**
 * Validate environment file
 */
function validateEnvironment(env) {
  const envFile = ENV_FILES[env]
  
  if (!envFile) {
    console.error(`❌ Unknown environment: ${env}`)
    process.exit(1)
  }
  
  const filePath = path.join(rootDir, envFile)
  
  if (!fs.existsSync(filePath)) {
    console.error(`❌ Environment file not found: ${envFile}`)
    process.exit(1)
  }
  
  try {
    const content = fs.readFileSync(filePath, 'utf8')
    const lines = content.split('\n').filter(line => line.trim() && !line.startsWith('#'))
    
    console.log(`\n🔍 Validating ${envFile}:\n`)
    
    const requiredVars = [
      'VITE_APP_API_URL',
      'VITE_APP_SIDE_BASE_URL',
      'VITE_APP_ENV',
      'VITE_APP_BUILD'
    ]
    
    const foundVars = new Set()
    
    lines.forEach(line => {
      const [key] = line.split('=')
      if (key) foundVars.add(key.trim())
    })
    
    requiredVars.forEach(varName => {
      const found = foundVars.has(varName)
      const status = found ? '✅' : '❌'
      console.log(`  ${status} ${varName}`)
    })
    
    console.log(`\n📊 Total variables: ${foundVars.size}`)
    console.log(`📊 Required variables: ${requiredVars.filter(v => foundVars.has(v)).length}/${requiredVars.length}`)
    
  } catch (error) {
    console.error(`❌ Failed to validate file: ${error.message}`)
    process.exit(1)
  }
}

/**
 * Show environment file information
 */
function showEnvironmentInfo(env) {
  const envFile = ENV_FILES[env]
  
  if (!envFile) {
    console.error(`❌ Unknown environment: ${env}`)
    process.exit(1)
  }
  
  const filePath = path.join(rootDir, envFile)
  
  if (!fs.existsSync(filePath)) {
    console.error(`❌ Environment file not found: ${envFile}`)
    process.exit(1)
  }
  
  try {
    const stats = fs.statSync(filePath)
    const content = fs.readFileSync(filePath, 'utf8')
    const lines = content.split('\n')
    const envVars = lines.filter(line => line.trim() && !line.startsWith('#') && line.includes('='))
    
    console.log(`\n📄 Environment File Information: ${envFile}\n`)
    console.log(`📁 Path: ${filePath}`)
    console.log(`📏 Size: ${stats.size} bytes`)
    console.log(`📅 Modified: ${stats.mtime.toLocaleString()}`)
    console.log(`📊 Total lines: ${lines.length}`)
    console.log(`🔧 Environment variables: ${envVars.length}`)
    
    if (envVars.length > 0) {
      console.log(`\n🔧 Variables:`)
      envVars.forEach(line => {
        const [key, value] = line.split('=')
        const maskedValue = value && value.length > 20 ? value.substring(0, 20) + '...' : value
        console.log(`  • ${key.trim()} = ${maskedValue}`)
      })
    }
    
  } catch (error) {
    console.error(`❌ Failed to read file info: ${error.message}`)
    process.exit(1)
  }
}

// Main execution
const [,, command, ...args] = process.argv

switch (command) {
  case 'list':
    listEnvironments()
    break
  case 'copy':
    copyEnvironment(args[0], args[1])
    break
  case 'validate':
    validateEnvironment(args[0])
    break
  case 'info':
    showEnvironmentInfo(args[0])
    break
  case 'help':
  case undefined:
    showHelp()
    break
  default:
    console.error(`❌ Unknown command: ${command}`)
    showHelp()
    process.exit(1)
}
