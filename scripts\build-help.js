#!/usr/bin/env node

console.log("\n🏗️  Build Options\n")

console.log("Available build commands:")
console.log("")

console.log("  pnpm build:dev    - Build for development environment")
console.log("  pnpm build:stg    - Build for staging environment")
console.log("  pnpm build:prod   - Build for production environment")
console.log("")

console.log("Environment files used:")
console.log("  • .env.dev   - Development configuration")
console.log("  • .env.stg   - Staging configuration")
console.log("  • .env.prod  - Production configuration")
console.log("")

console.log("Examples:")
console.log("  pnpm build:dev   # Build with development settings")
console.log("  pnpm build:stg   # Build for staging deployment")
console.log("  pnpm build:prod  # Build for production deployment")
console.log("")

console.log(
  "💡 Tip: Use the specific build command for your target environment."
)
console.log("")
