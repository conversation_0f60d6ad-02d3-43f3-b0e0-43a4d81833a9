import { useApiQuery } from "@/middleware"
import { useAuth } from "../auth"
import { PlayerBetHistory } from "./use-api"

// Constants
const ENDPOINTS = {
  GET_BET_HISTORY: import.meta.env.VITE_APP_GET_BET_HISTORY,
}

/**
 * Custom hook to fetch player bet history using React Query
 */
export const useBetHistoryQuery = (
  limit: number = 20,
  enabled: boolean = true
) => {
  const { token, isAuthenticated } = useAuth()

  return useApiQuery<PlayerBetHistory[]>(
    ["betHistory", JSON.stringify(limit)],
    ENDPOINTS.GET_BET_HISTORY,
    { limit },
    token ? { Authorization: `Bearer ${token}` } : undefined,
    {
      enabled: !!token && isAuthenticated() && enabled,
      staleTime: 1000 * 60, // 1 minute
      refetchInterval: 1000 * 60 * 5, // 5 minutes
      refetchOnWindowFocus: true,
      showErrorToast: true,
      errorToastTitle: "Failed to Load Bet History",
    }
  )
}

/**
 * Custom hook to fetch winning bet history
 */
export const useWinningBetHistoryQuery = (
  limit: number = 10,
  enabled: boolean = true
) => {
  const { data: allHistory, ...rest } = useBetHistoryQuery(limit * 2, enabled)

  // Filter to only winning bets
  const winningBets =
    allHistory?.filter((bet) => bet.potentialPayout > 0).slice(0, limit) || []

  return {
    ...rest,
    data: winningBets,
  }
}

/**
 * Custom hook to fetch bet history by bet type
 */
export const useBetHistoryByTypeQuery = (
  betTypeId: number,
  limit: number = 10,
  enabled: boolean = true
) => {
  const { data: allHistory, ...rest } = useBetHistoryQuery(limit * 3, enabled)

  // Filter by bet type
  const filteredBets =
    allHistory?.filter((bet) => bet.betTypeId === betTypeId).slice(0, limit) ||
    []

  return {
    ...rest,
    data: filteredBets,
  }
}
