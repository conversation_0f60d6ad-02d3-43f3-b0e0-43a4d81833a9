import { useCallback, useEffect, useRef } from "react"
import { useRoundPhase } from "../game"
import { RouletteRound, VideoSourceInfo } from "./types"

/**
 * Hook for managing video source selection based on game phase
 */
export const useVideoSource = () => {
  const { Betting, Spinning, Resulting } = useRoundPhase()
  const roundPhasesRef = useRef({ Betting, Spinning, Resulting })
  const isSideViewRef = useRef<boolean>(false)
  const lastVideoUrlRef = useRef<string | null>(null)
  const videoStartPositionRef = useRef<number>(0)
  const latestPositionRef = useRef<number>(0)

  const updateVideoSource = useCallback(
    (data: {
      round: RouletteRound
      position: number
      previousNumber: number
    }): VideoSourceInfo | null => {
      const sideViewUrl = `${
        import.meta.env.VITE_APP_SIDE_BASE_URL
      }video/sideviews/${data.previousNumber}/master.m3u8`

      const isSideView = roundPhasesRef.current.Betting
      const currentVideoUrl = isSideView ? sideViewUrl : data.round.videoUrl

      isSideViewRef.current = isSideView

      if (currentVideoUrl !== lastVideoUrlRef.current) {
        if (!isSideView) {
          const positionToUse =
            latestPositionRef.current > 0
              ? latestPositionRef.current
              : data.position
          videoStartPositionRef.current = positionToUse
        }

        lastVideoUrlRef.current = currentVideoUrl
        return {
          url: currentVideoUrl,
          startPosition: isSideView ? 0 : 0,
          isSideView,
        }
      }

      return null
    },
    []
  )

  // Update position reference
  const updatePosition = useCallback((position: number) => {
    latestPositionRef.current = position
  }, [])

  // Update ref when phase changes
  useEffect(() => {
    roundPhasesRef.current = { Betting, Spinning, Resulting }
  }, [Betting, Spinning, Resulting])

  return {
    updateVideoSource,
    updatePosition,
    isSideViewRef,
    videoStartPositionRef,
    latestPositionRef,
  }
}
