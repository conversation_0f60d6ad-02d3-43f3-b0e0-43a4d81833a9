import type {
  ToastProps
} from "@/components/ui/toast"
import { toast } from '@/hooks/ui/use-toast'

export type ToastType = 'success' | 'error' | 'warning' | 'info'

interface ToastConfig {
  duration?: number
  defaultDurations: Record<ToastType, number>
}

export const createToastService = (config: ToastConfig = {
  defaultDurations: {
    success: 3000,
    error: 5000,
    warning: 4000,
    info: 3000
  }
}) => {
  const showToast = (
    title: string, 
    description: string, 
    type: ToastType = 'info',
    options?: Partial<ToastProps>
  ) => {
    const duration = options?.duration || config.defaultDurations[type]
    
    toast({
      title,
      description,
      duration,
      ...options
    })
  }
  
  return {
    success: (title: string, description: string, options?: Partial<ToastProps>) => 
      showToast(title, description, 'success', options),
      
    error: (title: string, description: string, options?: Partial<ToastProps>) => 
      showToast(title, description, 'error', options),
      
    warning: (title: string, description: string, options?: Partial<ToastProps>) => 
      showToast(title, description, 'warning', options),
      
    info: (title: string, description: string, options?: Partial<ToastProps>) => 
      showToast(title, description, 'info', options)
  }
}

// Create a default toast service instance
export const toastService = createToastService()
