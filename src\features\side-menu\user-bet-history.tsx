import { useEffect, useState } from "react"
import { getPlayerBetHistoryNav, type BetHistoryNav } from "@/hooks"
import { formatNumber } from "@/lib/formatting"
import { useAuthStore } from "@/stores/auth-store"
import { tryCatch } from "@maxmorozoff/try-catch-tuple"

interface GroupedHistory {
  [date: string]: BetHistoryNav[]
}

export const UserBetHistory = () => {
  const [groupedHistory, setGroupedHistory] = useState<GroupedHistory | null>(
    null
  )
  const [loading, setLoading] = useState(true)
  const { tokenData, token } = useAuthStore()
  const currencyCode = useAuthStore((state) => state.tokenData?.CurrencyCode)

  useEffect(() => {
    const fetchHistory = async () => {
      if (!tokenData?.PlayerID || !token) return
      setLoading(true)

      const [data, error] = await tryCatch(
        () => getPlayerBetHistoryNav(tokenData.PlayerID, token),
        "Fetch Bet History"
      )
      if (error) {
        setLoading(false)
        setGroupedHistory(null)
        return
      }
      if (data && data.length > 0) {
        const grouped = data.reduce(
          (acc: GroupedHistory, bet: BetHistoryNav) => {
            const dateKey = bet.date.split("T")[0]
            if (!acc[dateKey]) acc[dateKey] = []
            acc[dateKey].push(bet)
            return acc
          },
          {}
        )

        setGroupedHistory(grouped)
      } else {
        setGroupedHistory(null)
      }
    }

    fetchHistory()
  }, [token, tokenData])

  if (loading) {
    return <div className='p-4 text-center'>Loading history...</div>
  }

  if (!groupedHistory || Object.keys(groupedHistory).length === 0) {
    return <div className='p-4 text-center'>No history available</div>
  }

  return (
    <div className='flex flex-col h-full overflow-y-auto bg-black text-white'>
      <div className='flex flex-col gap-0'>
        <div className='bg-transparent text-black py-1 px-2 grid grid-cols-4 text-xs font-semibold'>
          <p className='gradient-text font-semibold text-transparent bg-clip-text'>
            Date
          </p>
          <p className='gradient-text font-semibold text-transparent bg-clip-text'>
            Game
          </p>
          <p className='gradient-text font-semibold text-transparent bg-clip-text'>
            Bet
          </p>
          <p className='gradient-text font-semibold text-transparent bg-clip-text'>
            Win/Lose
          </p>
        </div>

        {Object.entries(groupedHistory).map(([date, bets]) => {
          const formattedDate = new Date(date).toLocaleDateString("en-US", {
            year: "numeric",
            month: "long",
            day: "numeric",
          })

          const summaryRow = bets.find((bet) => bet.game === null)
          const detailRows = bets.filter((bet) => bet.game !== null)

          return (
            <div key={date} className='flex flex-col'>
              <div className='bg-transparent border-y-2 border-amber-400 text-white py-1 px-2 grid grid-cols-4 text-xs'>
                <div>{formattedDate}</div>
                <div></div>
                <div>
                  {currencyCode}
                  {formatNumber(summaryRow?.bet ?? 0)}
                </div>
                <div>
                  {(summaryRow?.winLose ?? 0) < 0
                    ? `-${currencyCode}${formatNumber(
                        summaryRow?.winLose ?? 0
                      )}`
                    : `${currencyCode}${formatNumber(
                        summaryRow?.winLose ?? 0
                      )}`}
                </div>
              </div>

              {detailRows.map((bet, index) => (
                <div
                  key={index}
                  className='border-b border-gray-800 py-1 px-2 grid grid-cols-4 text-xs'
                >
                  <div>{bet.timeStamp || "N/A"}</div>
                  <div>{bet.game || "N/A"}</div>
                  <div>
                    {currencyCode} {formatNumber(bet.bet)}
                  </div>
                  <div>
                    {bet.winLose === 0 ? (
                      <span>R0</span>
                    ) : bet.winLose < 0 ? (
                      <span className='text-red-500'>
                        -{currencyCode} {formatNumber(bet.winLose ?? 0)}
                      </span>
                    ) : (
                      <span className='text-green-500'>
                        {currencyCode} {formatNumber(bet.winLose ?? 0)}
                      </span>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )
        })}
      </div>
    </div>
  )
}
