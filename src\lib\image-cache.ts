import React from "react"

/**
 * Image cache utility to prevent redundant asset loading
 * Helps solve the infinite asset request issue by caching loaded images
 */

interface ImageCacheEntry {
  promise: Promise<HTMLImageElement>
  loaded: boolean
  error: boolean
}

class ImageCache {
  private cache = new Map<string, ImageCacheEntry>()
  private loadingImages = new Set<string>()

  /**
   * Preload an image and cache the result
   * @param src Image source URL
   * @returns Promise that resolves when image is loaded
   */
  preload(src: string): Promise<HTMLImageElement> {
    // Return cached promise if already exists
    if (this.cache.has(src)) {
      return this.cache.get(src)!.promise
    }

    // Prevent duplicate loading requests
    if (this.loadingImages.has(src)) {
      // Wait for existing load to complete
      return new Promise((resolve, reject) => {
        const checkCache = () => {
          const entry = this.cache.get(src)
          if (entry) {
            entry.promise.then(resolve).catch(reject)
          } else {
            setTimeout(checkCache, 10)
          }
        }
        checkCache()
      })
    }

    this.loadingImages.add(src)

    const promise = new Promise<HTMLImageElement>((resolve, reject) => {
      const img = new Image()

      img.onload = () => {
        this.loadingImages.delete(src)
        const entry = this.cache.get(src)
        if (entry) {
          entry.loaded = true
        }
        resolve(img)
      }

      img.onerror = () => {
        this.loadingImages.delete(src)
        const entry = this.cache.get(src)
        if (entry) {
          entry.error = true
        }
        reject(new Error(`Failed to load image: ${src}`))
      }

      img.src = src
    })

    // Cache the promise immediately
    this.cache.set(src, {
      promise,
      loaded: false,
      error: false,
    })

    return promise
  }

  /**
   * Preload multiple images
   * @param sources Array of image source URLs
   * @returns Promise that resolves when all images are loaded
   */
  preloadMultiple(sources: string[]): Promise<HTMLImageElement[]> {
    return Promise.all(sources.map((src) => this.preload(src)))
  }

  /**
   * Check if an image is already loaded
   * @param src Image source URL
   * @returns true if image is loaded and cached
   */
  isLoaded(src: string): boolean {
    const entry = this.cache.get(src)
    return entry ? entry.loaded : false
  }

  /**
   * Check if an image failed to load
   * @param src Image source URL
   * @returns true if image failed to load
   */
  hasError(src: string): boolean {
    const entry = this.cache.get(src)
    return entry ? entry.error : false
  }

  /**
   * Clear the cache
   */
  clear(): void {
    this.cache.clear()
    this.loadingImages.clear()
  }

  /**
   * Remove a specific image from cache
   * @param src Image source URL
   */
  remove(src: string): void {
    this.cache.delete(src)
    this.loadingImages.delete(src)
  }

  /**
   * Get cache statistics
   */
  getStats() {
    return {
      totalCached: this.cache.size,
      loading: this.loadingImages.size,
      loaded: Array.from(this.cache.values()).filter((entry) => entry.loaded)
        .length,
      errors: Array.from(this.cache.values()).filter((entry) => entry.error)
        .length,
    }
  }
}

// Global image cache instance
export const imageCache = new ImageCache()

/**
 * React hook for preloading images with caching
 * @param sources Image source URLs or single URL
 * @returns Object with loading state and preload function
 */
export function useImagePreloader(sources: string | string[]) {
  const [loading, setLoading] = React.useState(false)
  const [loaded, setLoaded] = React.useState(false)
  const [error, setError] = React.useState<Error | null>(null)

  const preload = React.useCallback(async () => {
    if (loading) return

    setLoading(true)
    setError(null)

    try {
      const sourcesArray = Array.isArray(sources) ? sources : [sources]
      await imageCache.preloadMultiple(sourcesArray)
      setLoaded(true)
    } catch (err) {
      setError(
        err instanceof Error ? err : new Error("Failed to preload images")
      )
    } finally {
      setLoading(false)
    }
  }, [sources, loading])

  React.useEffect(() => {
    const sourcesArray = Array.isArray(sources) ? sources : [sources]
    const allLoaded = sourcesArray.every((src) => imageCache.isLoaded(src))

    if (allLoaded) {
      setLoaded(true)
    } else {
      preload()
    }
  }, [sources, preload])

  return {
    loading,
    loaded,
    error,
    preload,
  }
}
