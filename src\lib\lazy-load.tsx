import React, { Suspense, lazy, ComponentType, useEffect } from 'react'
import { useLocation, useNavigationType } from 'react-router-dom'
import { logger } from '@/middleware'

/**
 * Custom loading component for lazy-loaded components
 */
export const DefaultLoadingFallback: React.FC = () => (
  <div className="flex h-full w-full items-center justify-center p-4">
    <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
  </div>
)

/**
 * Custom error component for lazy-loaded components
 */
export const DefaultErrorFallback: React.FC<{ error: Error }> = ({ error }) => (
  <div className="flex h-full w-full flex-col items-center justify-center gap-2 p-4 text-red-500">
    <p>Failed to load component</p>
    <p className="text-sm">{error.message}</p>
  </div>
)

// Track preloaded modules to avoid duplicate preloading
const preloadedModules = new Set<string>()

/**
 * Options for lazy loading components
 */
export interface LazyLoadOptions {
  /**
   * Custom fallback component to show while loading
   */
  fallback?: React.ReactNode
  /**
   * Component name for logging and debugging
   */
  componentName?: string
  /**
   * Minimum loading time in milliseconds to prevent flickering
   */
  minimumLoadingTime?: number
  /**
   * Whether to prefetch the component
   */
  prefetch?: boolean
  /**
   * Priority of prefetching (high = immediate, medium = requestIdleCallback, low = visible)
   */
  prefetchPriority?: 'high' | 'medium' | 'low'
  /**
   * Routes that should trigger prefetching of this component
   */
  prefetchOnRoutes?: string[]
  /**
   * Whether to prefetch when the component is in viewport
   */
  prefetchOnVisible?: boolean
}

/**
 * Hook to prefetch components based on the current route
 */
export function usePrefetchOnRoute(
  routes: string[],
  factory: () => Promise<any>,
  componentName: string
) {
  const location = useLocation()
  const navigationType = useNavigationType()

  useEffect(() => {
    // Only prefetch on POP or PUSH navigation types (not REPLACE)
    if (navigationType === 'REPLACE') return

    // Check if current route matches any of the prefetch routes
    const shouldPrefetch = routes.some(route => {
      if (route.endsWith('*')) {
        // Handle wildcard routes
        const baseRoute = route.slice(0, -1)
        return location.pathname.startsWith(baseRoute)
      }
      return location.pathname === route
    })

    if (shouldPrefetch && !preloadedModules.has(componentName)) {
      logger.debug(`Route-based prefetching for ${componentName}`, { context: 'LazyLoad' })
      preloadedModules.add(componentName)
      factory().catch(error => {
        logger.error(`Failed to prefetch component: ${componentName}`, error, { context: 'LazyLoad' })
      })
    }
  }, [location, navigationType, routes, factory, componentName])
}

/**
 * Enhanced lazy loading utility that provides better error handling and loading states
 *
 * @param factory - Factory function that imports the component
 * @param options - Options for lazy loading
 * @returns Lazy-loaded component with proper loading and error states
 *
 * @example
 * ```tsx
 * const MyLazyComponent = lazyLoad(() => import('./MyComponent'), {
 *   componentName: 'MyComponent',
 *   fallback: <CustomLoadingComponent />,
 *   minimumLoadingTime: 300,
 *   prefetch: true
 * })
 * ```
 */
export function lazyLoad<T extends ComponentType<any>>(
  factory: () => Promise<{ default: T }>,
  options: LazyLoadOptions = {}
): React.ComponentType<React.ComponentProps<T>> {
  const {
    fallback = <DefaultLoadingFallback />,
    componentName = 'UnnamedComponent',
    minimumLoadingTime = 0,
    prefetch = false,
    prefetchPriority = 'medium',
    prefetchOnRoutes = [],
    prefetchOnVisible = false
  } = options

  // Prefetch the component if requested
  if (prefetch && !preloadedModules.has(componentName)) {
    preloadedModules.add(componentName)

    if (prefetchPriority === 'high') {
      // High priority - load immediately
      logger.debug(`High priority prefetching for ${componentName}`, { context: 'LazyLoad' })
      factory().catch(error => {
        logger.error(`Failed to prefetch component: ${componentName}`, error, { context: 'LazyLoad' })
      })
    } else if (prefetchPriority === 'medium') {
      // Medium priority - load during idle time
      const schedulePreload = window.requestIdleCallback || ((cb) => setTimeout(cb, 0))
      schedulePreload(() => {
        logger.debug(`Medium priority prefetching for ${componentName}`, { context: 'LazyLoad' })
        factory().catch(error => {
          logger.error(`Failed to prefetch component: ${componentName}`, error, { context: 'LazyLoad' })
        })
      })
    }
    // Low priority is handled by IntersectionObserver in the component
  }

  // Create the lazy component with minimum loading time
  const LazyComponent = lazy(() => {
    const start = Date.now()

    return factory()
      .then(module => {
        const elapsed = Date.now() - start
        const remaining = minimumLoadingTime - elapsed

        // If we haven't shown the loading state for the minimum time,
        // delay resolving the component
        if (remaining > 0) {
          return new Promise<{ default: T }>(resolve => {
            setTimeout(() => resolve(module), remaining)
          })
        }

        return module
      })
      .catch(error => {
        logger.error(`Failed to load component: ${componentName}`, error, { context: 'LazyLoad' })
        throw error
      })
  }) as React.LazyExoticComponent<React.ComponentType<React.ComponentProps<T>>>

  // Create a wrapper component that handles prefetching and rendering
  const WrappedComponent = (props: React.ComponentProps<T>) => {
    // Use route-based prefetching if routes are provided
    if (prefetchOnRoutes.length > 0) {
      usePrefetchOnRoute(prefetchOnRoutes, factory, componentName)
    }

    // Handle visibility-based prefetching
    useEffect(() => {
      if (prefetchOnVisible && !preloadedModules.has(`${componentName}-visible`)) {
        const observer = new IntersectionObserver(
          (entries) => {
            entries.forEach(entry => {
              if (entry.isIntersecting) {
                logger.debug(`Visibility-based prefetching for ${componentName}`, { context: 'LazyLoad' })
                preloadedModules.add(`${componentName}-visible`)
                factory().catch(error => {
                  logger.error(`Failed to prefetch component: ${componentName}`, error, { context: 'LazyLoad' })
                })
                observer.disconnect()
              }
            })
          },
          { rootMargin: '200px' } // Start loading when within 200px of viewport
        )

        // We need to wait for the DOM to be ready
        setTimeout(() => {
          const elements = document.querySelectorAll(`[data-component="${componentName}"]`)
          if (elements.length > 0) {
            elements.forEach(el => observer.observe(el))
          } else {
            // If no elements with the data attribute, observe the first element of the component
            const element = document.querySelector(`.${componentName.toLowerCase()}-container`)
            if (element) observer.observe(element)
          }
        }, 100)

        return () => observer.disconnect()
      }
    }, [])

    return (
      <Suspense fallback={fallback}>
        <div
          className={`${componentName.toLowerCase()}-container`}
          data-component={componentName}
        >
          <LazyComponent {...props} />
        </div>
      </Suspense>
    )
  }

  WrappedComponent.displayName = `LazyLoaded(${componentName})`

  return WrappedComponent
}

/**
 * Preloads a component without rendering it
 *
 * @param factory - Factory function that imports the component
 * @param componentName - Component name for logging
 */
export function preloadComponent<T extends ComponentType<any>>(
  factory: () => Promise<{ default: T }>,
  componentName = 'UnnamedComponent'
): void {
  logger.debug(`Preloading component: ${componentName}`, { context: 'LazyLoad' })

  factory().catch(error => {
    logger.error(`Failed to preload component: ${componentName}`, error, { context: 'LazyLoad' })
  })
}
