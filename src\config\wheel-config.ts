type WheelConfig = {
  value: string
  colour: string
}

type colorType = "black" | "red" | "green"

// Add new type and configuration for result images
type ResultImageConfig = {
  pattern: colorType[]
  imagePath: string
}

export const resultImageConfig: ResultImageConfig[] = [
  {
    pattern: ["black", "red", "black"],
    imagePath: "/assets/images/misc/resulting-brb.webp",
  },
  {
    pattern: ["red", "black", "red"],
    imagePath: "/assets/images/misc/resulting-rbr.webp",
  },
  {
    pattern: ["green", "red", "black"],
    imagePath: "/assets/images/misc/resulting-grb.webp",
  },
  {
    pattern: ["black", "green", "red"],
    imagePath: "/assets/images/misc/resulting-bgr.webp",
  },
  {
    pattern: ["red", "black", "green"],
    imagePath: "/assets/images/misc/resulting-rbg.webp",
  },
]

export const wheelConfig: WheelConfig[] = [
  { value: "0", colour: "green" },
  { value: "32", colour: "red" },
  { value: "15", colour: "black" },
  { value: "19", colour: "red" },
  { value: "4", colour: "black" },
  { value: "21", colour: "red" },
  { value: "2", colour: "black" },
  { value: "25", colour: "red" },
  { value: "17", colour: "black" },
  { value: "34", colour: "red" },
  { value: "6", colour: "black" },
  { value: "27", colour: "red" },
  { value: "13", colour: "black" },
  { value: "36", colour: "red" },
  { value: "11", colour: "black" },
  { value: "30", colour: "red" },
  { value: "8", colour: "black" },
  { value: "23", colour: "red" },
  { value: "10", colour: "black" },
  { value: "5", colour: "red" },
  { value: "24", colour: "black" },
  { value: "16", colour: "red" },
  { value: "33", colour: "black" },
  { value: "1", colour: "red" },
  { value: "20", colour: "black" },
  { value: "14", colour: "red" },
  { value: "31", colour: "black" },
  { value: "9", colour: "red" },
  { value: "22", colour: "black" },
  { value: "18", colour: "red" },
  { value: "29", colour: "black" },
  { value: "7", colour: "red" },
  { value: "28", colour: "black" },
  { value: "12", colour: "red" },
  { value: "35", colour: "black" },
  { value: "3", colour: "red" },
  { value: "26", colour: "black" },
]

// Add helper function to get resulting image path
export const getResultingImagePath = (values: { value: string }[]): string => {
  // Map the values to their corresponding colors using wheelConfig
  const colors = values.map((val) => {
    const wheelNumber = wheelConfig.find((config) => config.value === val.value)
    return wheelNumber?.colour as colorType
  })

  // Find matching pattern in resultImageConfig
  const matchingConfig = resultImageConfig.find((config) =>
    config.pattern.every((color, index) => color === colors[index])
  )

  return (
    matchingConfig?.imagePath || "/assets/images/misc/resulting-rbg.webp"
  )
}
