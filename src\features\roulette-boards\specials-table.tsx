import { useState } from "react"
import { useRoundPhase } from "@/hooks"
import { useBettingStore } from "@/stores/betting-store"
import { useSettingsStore } from "@/stores/settings-store"
import { rouletteSelectors, SelectableCell } from "@/config/selector-config"
const SpecialsTable = () => {
  const { Betting } = useRoundPhase()
  const [hoveredId, setHoveredId] = useState<string | null>(null)
  const [highlightedValues, setHighlightedValues] = useState<number[]>([])
  const initialNeighbours = useSettingsStore((state) => state.initialNeighbours)
  const addChip = useBettingStore((state) => state.addChip)
  const selectedChip = useBettingStore((state) => state.selectedChip)

  const selectors: SelectableCell[] = rouletteSelectors

  // The highlight array defining the circular arrangement
  const highlightArr = [
    16, 33, 1, 20, 14, 31, 9, 22, 18, 29, 7, 28, 12, 35, 3, 26, 0, 32, 15, 19,
    4, 21, 2, 25, 17, 34, 6, 27, 13, 36, 11, 30, 8, 23, 10, 5, 24,
  ]

  const getNeighbors = (index: number) => {
    const currentIndex = highlightArr.indexOf(index)
    const length = highlightArr.length

    const prevIndex = (currentIndex - initialNeighbours + length) % length
    const nextIndex = (currentIndex + initialNeighbours) % length

    return { prevIndex, nextIndex }
  }

  const getHighlightedIndices = (prevIndex: number, nextIndex: number) => {
    const length = highlightArr.length

    if (prevIndex < nextIndex) {
      return highlightArr.slice(prevIndex, nextIndex + 1)
    }

    return [
      ...highlightArr.slice(prevIndex, length),
      ...highlightArr.slice(0, nextIndex + 1),
    ]
  }

  const handleMouseEnter = (event: React.MouseEvent<SVGPathElement>) => {
    const cell = event.target as SVGSVGElement
    setHoveredId(cell.id)
    const { prevIndex, nextIndex } = getNeighbors(parseInt(cell.id))
    const highlightedCells = getHighlightedIndices(prevIndex, nextIndex)
    setHighlightedValues(highlightedCells)
  }

  const handleMouseLeave = () => {
    setHoveredId(null)
    setHighlightedValues([])
  }

  const handleClick = (event: React.MouseEvent<SVGPathElement>) => {
    if (!selectedChip || !Betting) return
    const cell = event.target as SVGSVGElement
    const { prevIndex, nextIndex } = getNeighbors(parseInt(cell.id))
    const highlightedCells = getHighlightedIndices(prevIndex, nextIndex)

    if (highlightedCells.includes(0)) {
      addChip(
        {
          cell_id: 0,
          chips_placed: [],
          bet_type_id: 1,
          cell_selectors: ["0"],
          isSelectable: true,
        },
        selectedChip
      )
    }

    // Find and add chips to placedChips
    selectors.forEach((selector) => {
      if (
        selector.cell_selectors.some(
          (value) =>
            highlightedCells.includes(parseInt(value)) &&
            selector.cell_selectors.length === 1
        )
      ) {
        addChip(selector, selectedChip)
      }
    })
  }

  const getPathFill = (cellId: string) => {
    const green = "hsl(122, 87%, 36%)"
    const darkGreen = "hsl(122, 87%, 30%)"
    const black = "hsl(0, 0%, 15%)"
    const red = "	hsl(0, 79%, 42%)"
    const darkRed = "	hsl(0, 79%, 35%)"
    const richBlack = "	hsl(0, 0%, 9%)"

    // Define red numbers (same as in table-constants.ts)
    const redNumbers = [
      1, 3, 5, 7, 9, 12, 14, 16, 18, 19, 21, 23, 25, 27, 30, 32, 34, 36,
    ]

    // Check if cell is hovered
    const isHovered = hoveredId && highlightedValues.includes(parseInt(cellId))

    // Determine base color based on cell ID
    const cellNum = parseInt(cellId)

    // Special case for zero
    if (cellNum === 0) {
      return isHovered ? darkGreen : green
    }

    // For red numbers
    if (redNumbers.includes(cellNum)) {
      return isHovered ? darkRed : red
    }

    // For black numbers
    return isHovered ? richBlack : black
  }

  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width={"100%"}
      height={"100%"}
      viewBox='0 0 1486.87 520.13'
    >
      <defs>
        <style>
          {
            ".cls-1{fill:#252525; transition: all 0.3s ease;}.cls-2{stroke:#e4b61c}.cls-2,.cls-5{fill:none;stroke-width:4px}.cls-8{fill:#fff; user-select:none; pointer-events: none; cursor: default; transition: all 0.3s ease;}.cls-10{fill:#be1616; transition: all 0.3s ease;}.cls-5{stroke:#1c83e4}.cls-11{transition: all 0.3s ease;}"
          }
        </style>
      </defs>
      <path
        style={{
          fill: "none",
        }}
        d='M1.87.13h1485v520H1.87z'
      />
      <path
        d='M258.05 0C113.65 0 0 119.47 0 259.63s110.35 259.93 258.04 259.99l969.19.38c138.9.05 256.62-120.2 256.62-260.37S1367.23 0 1228.32 0zm51.16 85.44h873.73c90.09 0 197.71 72.12 197.12 174.43-.56 98.9-93.3 177.65-207.98 177.43l-860.52-1.72c-110.65-.22-215.41-80.31-215.41-175.95S198.57 85.44 309.22 85.44Z'
        style={{
          fill: "rgba(34,34,34,.5)",
        }}
      />
      <path
        id='33-cell'
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onClick={handleClick}
        className='cls-1'
        style={{
          fill: getPathFill("33"),
        }}
        d='M377.89 79.64V7.23h-76.16v72.81c3.22-.11 6.38-.39 9.64-.39h66.52Z'
      />
      <path
        id='1-cell'
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onClick={handleClick}
        className='cls-10'
        style={{
          fill: getPathFill("1"),
        }}
        d='M458.14 79.64V7.23h-76.17v72.41z'
      />
      <path
        id='20-cell'
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onClick={handleClick}
        className='cls-1'
        style={{
          fill: getPathFill("20"),
        }}
        d='M538.39 79.64V7.23h-76.17v72.41z'
      />
      <path
        id='14-cell'
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onClick={handleClick}
        className='cls-10'
        style={{
          fill: getPathFill("14"),
        }}
        d='M618.64 79.64V7.23h-76.16v72.41z'
      />
      <path
        id='31-cell'
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onClick={handleClick}
        className='cls-1'
        style={{
          fill: getPathFill("31"),
        }}
        d='M698.89 79.64V7.23h-76.17v72.41z'
      />
      <path
        id='9-cell'
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onClick={handleClick}
        className='cls-10'
        style={{
          fill: getPathFill("9"),
        }}
        d='M779.14 79.64V7.23h-76.16v72.41z'
      />
      <path
        id='22-cell'
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onClick={handleClick}
        className='cls-1'
        style={{
          fill: getPathFill("22"),
        }}
        d='M859.39 79.64V7.23h-76.16v72.41z'
      />
      <path
        id='18-cell'
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onClick={handleClick}
        className='cls-10'
        style={{
          fill: getPathFill("18"),
        }}
        d='M939.64 79.64V7.23h-76.16v72.41z'
      />
      <path
        id='29-cell'
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onClick={handleClick}
        className='cls-1'
        style={{
          fill: getPathFill("29"),
        }}
        d='M1019.89 79.64V7.23h-76.17v72.41z'
      />
      <path
        id='7-cell'
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onClick={handleClick}
        className='cls-10'
        style={{
          fill: getPathFill("7"),
        }}
        d='M1100.14 79.64V7.23h-76.17v72.41z'
      />
      <path
        id='28-cell'
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onClick={handleClick}
        className='cls-1'
        style={{
          fill: getPathFill("28"),
        }}
        d='m1206.6 80-.19-72.77h-102.19v72.41h67.44c2.97 0 32.02.27 34.94.36'
      />
      <path
        id='12-cell'
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onClick={handleClick}
        className='cls-10'
        style={{
          fill: getPathFill("12"),
        }}
        d='M1210.62 80.87c29.98 3.04 51.42 11.04 70.66 23.19l54.73-71.09C1294.57 11 1257.24 7.28 1210.75 6.68l-.13 74.18Z'
      />
      <path
        id='35-cell'
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onClick={handleClick}
        className='cls-1'
        style={{
          fill: getPathFill("35"),
        }}
        d='m1345.12 155.45 79.22-49.41c-21.79-29.03-51.56-54.26-83.63-71.66l-54.21 73.38c17.23 11.17 46.4 32.53 58.62 47.7Z'
      />
      <path
        id='3-cell'
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onClick={handleClick}
        className='cls-10'
        style={{
          fill: getPathFill("3"),
        }}
        d='m1375.83 213.9 91.68-20.98c-8.46-30.75-22.5-59.07-40.99-83.94l-79.02 49.29c12.91 16.72 22.42 35.51 28.33 55.63'
      />
      <path
        id='26-cell'
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onClick={handleClick}
        className='cls-1'
        style={{
          fill: getPathFill("26"),
        }}
        d='m1379.65 295.58 92.82 11.79c1.23-10.19 4.4-36.16 4.4-46.69 0-22.26-3.14-43.73-8.48-64.3l-91.6 20.96c3.76 13.91 5.95 28.38 5.95 43.35 0 6.92-2.27 28.18-3.09 34.9Z'
      />
      <path
        id='0-cell'
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onClick={handleClick}
        className='cls-11'
        d='M1378.21 298.84c-4.63 27.9-20.09 53.64-50.51 86.56l72.81 55.29c20.29-19.72 30.13-34.86 45.26-60.89 15.33-27.29 21.57-35.93 25.96-68.31z'
        style={{
          fill: getPathFill("0"),
        }}
      />
      <path
        id='32-cell'
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onClick={handleClick}
        className='cls-10'
        style={{
          fill: getPathFill("32"),
        }}
        d='m1259.58 426.31 35.55 77.74c40.49-10.66 77.74-39.86 103.09-60.18l-73.71-56.08c-16.38 12.98-43.8 30.63-64.93 38.53Z'
      />
      <path
        id='15-cell'
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onClick={handleClick}
        className='cls-1'
        style={{
          fill: getPathFill("15"),
        }}
        d='M1184.47 441.19v72.94h40.96c22.81 0 44.81-3.33 65.82-9.07l-35.36-77.31c-21.75 7.7-45.78 12.25-71.42 13.44'
      />
      <path
        id='19-cell'
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onClick={handleClick}
        className='cls-10'
        style={{
          fill: getPathFill("19"),
        }}
        d='M1104.22 441.71v72.41h76.17v-72.77c-2.92.09-5.76.36-8.73.36z'
      />
      <path
        id='4-cell'
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onClick={handleClick}
        className='cls-1'
        style={{
          fill: getPathFill("4"),
        }}
        d='M1023.97 441.71v72.41h76.17v-72.41z'
      />
      <path
        id='21-cell'
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onClick={handleClick}
        className='cls-10'
        style={{
          fill: getPathFill("21"),
        }}
        d='M943.72 441.71v72.41h76.17v-72.41z'
      />
      <path
        id='2-cell'
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onClick={handleClick}
        className='cls-1'
        style={{
          fill: getPathFill("2"),
        }}
        d='M863.48 441.71v72.41h76.16v-72.41z'
      />
      <path
        id='25-cell'
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onClick={handleClick}
        className='cls-10'
        style={{
          fill: getPathFill("25"),
        }}
        d='M783.23 441.71v72.41h76.16v-72.41z'
      />
      <path
        id='17-cell'
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onClick={handleClick}
        className='cls-1'
        style={{
          fill: getPathFill("17"),
        }}
        d='M702.97 441.71v72.41h76.16v-72.41z'
      />
      <path
        id='34-cell'
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onClick={handleClick}
        className='cls-10'
        style={{
          fill: getPathFill("34"),
        }}
        d='M622.72 441.71v72.41h76.17v-72.41z'
      />
      <path
        id='6-cell'
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onClick={handleClick}
        className='cls-1'
        style={{
          fill: getPathFill("6"),
        }}
        d='M542.48 441.71v72.41h76.16v-72.41z'
      />
      <path
        id='27-cell'
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onClick={handleClick}
        className='cls-10'
        style={{
          fill: getPathFill("27"),
        }}
        d='M462.22 441.71v72.41h76.17v-72.41z'
      />
      <path
        id='13-cell'
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onClick={handleClick}
        className='cls-1'
        style={{
          fill: getPathFill("13"),
        }}
        d='M381.97 441.71v72.41h76.17v-72.41z'
      />
      <path
        id='36-cell'
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onClick={handleClick}
        className='cls-10'
        style={{
          fill: getPathFill("36"),
        }}
        d='M301.72 441.32v72.81h76.16v-72.41h-66.52c-3.26 0-6.42-.28-9.64-.39Z'
      />
      <path
        id='11-cell'
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onClick={handleClick}
        className='cls-1'
        style={{
          fill: getPathFill("11"),
        }}
        d='m224.42 427.37-35.79 76.88c21.94 6.3 45.01 9.89 68.96 9.89h40.04v-72.98c-25.91-1.28-50.6-5.94-73.22-13.79Z'
      />
      <path
        id='30-cell'
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onClick={handleClick}
        className='cls-10'
        style={{
          fill: getPathFill("30"),
        }}
        d='m157.06 390.61-66.52 59.12c26.89 24.16 58.8 42.7 94.16 53.49l35.99-77.33c-23.79-8.69-45.27-20.67-63.63-35.29Z'
      />
      <path
        id='8-cell'
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onClick={handleClick}
        className='cls-1'
        style={{
          fill: getPathFill("8"),
        }}
        d='m108.87 333.67-80.95 29.84c14.1 31.94 34.59 60.28 59.78 83.62l66.28-58.91c-18.89-15.63-34.18-34.12-45.1-54.55z'
      />
      <path
        id='23-cell'
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onClick={handleClick}
        className='cls-10'
        style={{
          fill: getPathFill("23"),
        }}
        d='m90.36 263.49-84.06.1c.39 34.25 7.42 66.88 20.02 96.58l80.75-29.76c-10.44-20.64-16.27-43.24-16.71-66.92'
      />
      <path
        id='10-cell'
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onClick={handleClick}
        className='cls-1'
        style={{
          fill: getPathFill("10"),
        }}
        d='m106.31 192.67-80.79-29.54C13.17 192.96 6.29 225.66 6.2 259.98l84.05-.11c.13-23.78 5.85-46.43 16.06-67.2'
      />
      <path
        id='5-cell'
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onClick={handleClick}
        className='cls-10'
        style={{
          fill: getPathFill("5"),
        }}
        d='M152.85 134.15 86.4 75.42C61.29 99.01 40.89 127.57 27 159.75l80.9 29.58c10.75-20.68 26.08-39.33 44.95-55.18'
      />
      <path
        id='24-cell'
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onClick={handleClick}
        className='cls-1'
        style={{
          fill: getPathFill("24"),
        }}
        d='m219.89 95.78-36.3-77.35c-35.5 11.01-67.47 29.9-94.35 54.39l66.59 58.85c18.41-14.88 40.08-27.03 64.05-35.89z'
      />
      <path
        id='16-cell'
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onClick={handleClick}
        className='cls-10'
        style={{
          fill: getPathFill("16"),
        }}
        d='M297.64 7.23H257.6c-25.12 0-47.8 3.67-70.06 10.17l36.09 76.91c22.85-8.02 47.79-12.81 74.02-14.1V7.23Z'
      />
      <path
        id='0'
        data-name='0n'
        className='cls-8 '
        d='M1400.43 367.95c0-3.33.53-6.51 1.58-9.54 1.07-3.05 2.33-5.21 3.78-6.49 1.1-.97 2.35-1.46 3.73-1.46 1.7 0 3.1.76 4.2 2.27 1.11 1.5 1.67 3.7 1.67 6.61 0 3.33-.53 6.51-1.58 9.55-1.04 3.04-2.29 5.19-3.75 6.47-1.09.95-2.33 1.42-3.74 1.42-1.68 0-3.08-.74-4.2-2.23-1.13-1.49-1.69-3.68-1.69-6.59Zm4.06 1.02c0 1.38.18 2.36.55 2.92s.85.84 1.42.84c.73 0 1.39-.4 1.99-1.21q1.17-1.635 2.04-6.24c.58-3.08.88-5.46.88-7.14 0-1.29-.19-2.22-.57-2.78-.37-.58-.85-.86-1.42-.86-.69 0-1.32.4-1.88 1.2-.89 1.25-1.65 3.56-2.27 6.91-.49 2.62-.74 4.75-.74 6.36'
      />
      <path
        id='10'
        data-name='10n'
        className='cls-8 '
        d='m40.77 215.28.77-4.5q5.355-2.85 8.28-6.63h2.51l-4.45 25.93h-4.17l3.08-17.86c-.84.67-1.83 1.28-2.97 1.85-1.12.56-2.14.97-3.06 1.21zm14.82 6.42c0-3.33.53-6.51 1.58-9.54 1.07-3.05 2.33-5.21 3.78-6.49 1.1-.97 2.34-1.46 3.73-1.46 1.7 0 3.1.76 4.2 2.27 1.11 1.5 1.67 3.7 1.67 6.61 0 3.33-.53 6.51-1.58 9.54-1.04 3.03-2.29 5.19-3.74 6.47-1.09.95-2.34 1.42-3.74 1.42-1.68 0-3.08-.74-4.2-2.23s-1.69-3.68-1.69-6.59Zm4.06 1.02c0 1.38.18 2.35.54 2.92.38.56.85.84 1.42.84.73 0 1.39-.4 1.99-1.21.79-1.09 1.46-3.17 2.04-6.24.59-3.08.88-5.46.88-7.14 0-1.29-.19-2.21-.56-2.78-.38-.58-.85-.86-1.42-.86-.69 0-1.32.4-1.88 1.2-.89 1.25-1.65 3.56-2.27 6.91-.49 2.62-.74 4.75-.74 6.36'
      />
      <path
        id='8'
        data-name='8n'
        className='cls-8 '
        d='M88.21 387.06c-.74-.6-1.29-1.28-1.67-2.06-.38-.77-.56-1.64-.56-2.6 0-1.28.3-2.47.9-3.59.61-1.12 1.41-1.98 2.39-2.57 1-.59 2.11-.88 3.34-.88 1.79 0 3.24.57 4.34 1.72 1.1 1.14 1.65 2.66 1.65 4.57 0 1.3-.3 2.44-.9 3.41q-.885 1.44-2.67 2.37c.87.65 1.51 1.43 1.93 2.36.43.91.65 1.96.65 3.15 0 2.13-.6 4.03-1.79 5.68-1.48 2.05-3.41 3.08-5.8 3.08-1.8 0-3.29-.65-4.46-1.95q-1.74-1.95-1.74-5.19 0-2.655 1.2-4.68c.81-1.35 1.88-2.29 3.2-2.83h-.01Zm1.57-4.32c0 .83.21 1.49.62 1.99q.63.72 1.56.72c.71 0 1.36-.33 1.93-1s.86-1.48.86-2.43q0-1.29-.63-2.04c-.41-.5-.93-.76-1.56-.76-.74 0-1.39.33-1.95 1-.55.66-.83 1.49-.83 2.51Zm-1.85 11.92c0 1 .22 1.76.67 2.3.45.53 1.01.79 1.69.79 1.01 0 1.81-.55 2.41-1.64.61-1.1.91-2.38.91-3.83q0-1.35-.69-2.16c-.69-.81-1.02-.81-1.69-.81-.86 0-1.62.48-2.3 1.44-.67.96-1 2.26-1 3.9Z'
      />
      <path
        id='11'
        data-name='11n'
        className='cls-8 '
        d='m242.32 472.03.77-4.5q5.355-2.85 8.28-6.63h2.51l-4.45 25.93h-4.17l3.08-17.86c-.84.67-1.83 1.28-2.97 1.85-1.12.56-2.15.97-3.06 1.21Zm14.41 0 .77-4.5q5.355-2.85 8.28-6.63h2.51l-4.45 25.93h-4.17l3.08-17.86c-.84.67-1.83 1.28-2.97 1.85s-2.14.97-3.06 1.21z'
      />
      <path
        id='13'
        data-name='13n'
        className='cls-8 '
        d='m405.33 475.86.77-4.5q5.355-2.85 8.28-6.63h2.51l-4.45 25.93h-4.17l3.08-17.86c-.84.67-1.83 1.28-2.97 1.85s-2.15.97-3.06 1.21zm14.43 8.07 3.95-.58q.24 2.055.9 2.85c.43.53 1.01.79 1.72.79.9 0 1.67-.38 2.3-1.12.63-.76.95-1.76.95-2.99 0-1.11-.26-1.96-.77-2.55-.52-.6-1.18-.9-2-.9-.2 0-.42.02-.65.07l.72-4.2.42.04c1.08 0 1.91-.33 2.5-.99.59-.67.88-1.61.88-2.83 0-.86-.2-1.52-.6-1.99s-.9-.7-1.51-.7c-1.3 0-2.21 1.18-2.72 3.53l-3.78-.91c.52-2.04 1.26-3.58 2.23-4.62 1.26-1.36 2.79-2.04 4.57-2.04s3.18.61 4.27 1.83q1.65 1.815 1.65 4.89c0 1.38-.33 2.61-1 3.67-.66 1.07-1.53 1.84-2.62 2.32.83.54 1.48 1.25 1.93 2.14.46.88.68 1.87.68 2.97 0 2.03-.63 3.87-1.88 5.54-1.49 1.97-3.36 2.95-5.62 2.95-1.73 0-3.19-.6-4.36-1.81-1.17-1.22-1.89-3.01-2.16-5.36'
      />
      <path
        id='6'
        data-name='6n'
        className='cls-8 '
        d='m587.85 471.15-3.87.39c-.08-1.03-.29-1.75-.61-2.14-.32-.41-.74-.62-1.28-.62-.83 0-1.56.48-2.2 1.44-.62.95-1.14 2.38-1.55 4.29.89-.72 1.83-1.07 2.83-1.07 1.61 0 2.94.67 4.01 2 1.08 1.34 1.62 3.2 1.62 5.59 0 2.71-.7 5.06-2.09 7.07-1.38 1.99-3.14 2.99-5.27 2.99q-1.74 0-3.15-1.02c-.93-.69-1.66-1.73-2.22-3.11-.54-1.39-.81-3.29-.81-5.68 0-4.85.87-8.82 2.62-11.9s3.92-4.62 6.52-4.62c1.53 0 2.78.52 3.73 1.56s1.52 2.65 1.72 4.83m-10.6 11.9c0 1.37.22 2.38.65 3.02.45.63 1 .95 1.65.95.81 0 1.5-.46 2.07-1.39.77-1.23 1.16-2.87 1.16-4.92 0-1.35-.22-2.34-.65-2.97-.43-.64-.96-.97-1.58-.97-.49 0-1 .22-1.51.65-.5.42-.93 1.14-1.28 2.16-.34 1.02-.51 2.17-.51 3.46Z'
      />
      <path
        id='17'
        data-name='17n'
        className='cls-8 '
        d='m725.7 476.08.77-4.5q5.355-2.85 8.28-6.63h2.51l-4.45 25.93h-4.17l3.08-17.86c-.84.67-1.83 1.28-2.97 1.85-1.12.56-2.15.97-3.06 1.21Zm15.98-5.95.79-4.66h13.94l-.61 3.59c-.73.72-1.49 1.69-2.3 2.92a39 39 0 0 0-2.97 5.4c-1.14 2.55-2.03 4.89-2.67 7-.89 2.94-1.45 5.12-1.67 6.52h-3.94c.24-1.71.78-3.93 1.64-6.64.86-2.72 1.85-5.23 2.97-7.54a45.7 45.7 0 0 1 3.87-6.57h-9.04v-.02Z'
      />
      <path
        id='2'
        data-name='2n'
        className='cls-8 '
        d='m900.04 473.12-4.06-.74c.35-2.46 1.13-4.31 2.34-5.54 1.21-1.24 2.69-1.86 4.45-1.86 1.9 0 3.43.63 4.59 1.88s1.74 2.93 1.74 5.04c0 1.64-.43 3.24-1.28 4.8-.63 1.15-1.92 2.83-3.87 5.05-1.77 1.99-2.98 3.49-3.62 4.5h7.33l-.79 4.61h-12.83c.21-1.91.82-3.73 1.83-5.47.68-1.17 2.27-3.21 4.76-6.1 1.82-2.11 2.98-3.57 3.48-4.39.66-1.1.98-2.11.98-3.02s-.22-1.63-.65-2.14c-.42-.53-.94-.79-1.57-.79-1.44 0-2.38 1.39-2.83 4.18z'
      />
      <path
        id='4'
        data-name='4n'
        className='cls-8 '
        d='M1062.52 485.55h-8.32l.72-4.22 11.45-16.3h3.55l-2.76 16.22h2.55l-.74 4.29h-2.55l-.91 5.26h-3.91l.92-5.26Zm.74-4.29 1.37-8.03-5.57 8.03z'
      />
      <path
        id='15'
        data-name='15n'
        className='cls-8 '
        d='m1214.13 471.74.78-4.5q5.355-2.85 8.28-6.63h2.51l-4.45 25.93h-4.16l3.07-17.86c-.84.67-1.83 1.28-2.97 1.85s-2.14.97-3.06 1.21m14.8 7.42 4.03-.44-.02.53c0 1.21.22 2.11.67 2.71.45.59 1 .88 1.67.88.98 0 1.88-.57 2.69-1.7.81-1.14 1.21-2.72 1.21-4.75 0-1.22-.23-2.13-.68-2.73-.46-.6-1.05-.9-1.76-.9-.55 0-1.09.15-1.6.46-.5.3-.99.77-1.46 1.39l-3.41-.32 3.22-13.17h10.63l-.79 4.61h-7.01l-.97 3.97c.4-.22.8-.39 1.2-.49.41-.12.81-.18 1.21-.18 1.57 0 2.88.65 3.94 1.95s1.6 3.09 1.6 5.36c0 1.97-.41 3.81-1.22 5.52-.79 1.71-1.81 2.99-3.06 3.85-1.24.84-2.53 1.27-3.88 1.27-1.76 0-3.23-.7-4.41-2.09-1.19-1.39-1.78-3.3-1.79-5.73'
      />
      <path
        id='26'
        data-name='26n'
        className='cls-8 '
        d='m1424 249.18-4.06-.74c.35-2.46 1.13-4.31 2.34-5.54 1.21-1.24 2.69-1.86 4.45-1.86 1.89 0 3.42.63 4.58 1.88s1.74 2.94 1.74 5.04c0 1.64-.42 3.24-1.28 4.8q-.945 1.725-3.87 5.04c-1.77 1.99-2.97 3.49-3.62 4.5h7.33l-.79 4.61h-12.83c.21-1.91.82-3.73 1.83-5.47.68-1.17 2.27-3.21 4.76-6.1 1.82-2.11 2.98-3.57 3.48-4.39.66-1.1.99-2.11.99-3.02s-.22-1.63-.65-2.14c-.43-.53-.95-.79-1.57-.79-1.44 0-2.38 1.39-2.83 4.18m25.6-1.74-3.87.39c-.08-1.03-.29-1.75-.62-2.14-.31-.41-.74-.62-1.28-.62-.83 0-1.56.48-2.2 1.44-.62.95-1.13 2.38-1.54 4.29.89-.71 1.83-1.07 2.83-1.07 1.6 0 2.94.67 4.01 2 1.08 1.34 1.62 3.2 1.62 5.59 0 2.71-.7 5.06-2.09 7.07-1.38 1.99-3.14 2.99-5.27 2.99q-1.74 0-3.15-1.02c-.93-.69-1.67-1.73-2.22-3.11-.54-1.4-.8-3.29-.8-5.68 0-4.85.87-8.82 2.61-11.9 1.75-3.08 3.92-4.62 6.53-4.62q2.295 0 3.72 1.56c1.425 1.56 1.53 2.65 1.73 4.83m-10.6 11.9c0 1.37.21 2.38.65 3.02.44.63.99.95 1.65.95.81 0 1.5-.46 2.07-1.39.78-1.23 1.16-2.87 1.16-4.92 0-1.35-.21-2.34-.65-2.97-.43-.65-.96-.97-1.58-.97-.49 0-1 .22-1.51.65-.51.42-.93 1.14-1.28 2.16-.34 1.02-.51 2.17-.51 3.46Z'
      />
      <path
        id='35'
        data-name='35n'
        className='cls-8 '
        d='m1339.31 99.85 3.95-.58q.255 2.055.9 2.85c.645.795 1.01.79 1.72.79.9 0 1.67-.38 2.31-1.12.63-.76.94-1.76.94-2.99 0-1.11-.25-1.96-.77-2.55-.51-.6-1.18-.9-2-.9-.2 0-.42.02-.65.07l.72-4.2.42.04c1.08 0 1.91-.33 2.5-.98.58-.67.87-1.61.87-2.83 0-.86-.2-1.52-.59-1.99-.4-.47-.91-.7-1.52-.7-1.3 0-2.2 1.18-2.72 3.53l-3.78-.91c.52-2.04 1.26-3.58 2.23-4.62 1.27-1.36 2.79-2.04 4.57-2.04s3.19.61 4.28 1.83q1.65 1.815 1.65 4.89c0 1.38-.33 2.61-1 3.67-.66 1.07-1.53 1.84-2.62 2.32.83.54 1.48 1.25 1.93 2.14.46.88.68 1.87.68 2.97 0 2.03-.63 3.87-1.88 5.54-1.49 1.97-3.36 2.95-5.62 2.95-1.73 0-3.19-.6-4.36-1.81-1.17-1.22-1.89-3.01-2.16-5.36Zm16.79-.65 4.02-.44-.02.53c0 1.21.23 2.11.67 2.71.45.59 1 .88 1.67.88.99 0 1.88-.57 2.69-1.71s1.21-2.72 1.21-4.75c0-1.22-.23-2.13-.68-2.72-.46-.6-1.05-.9-1.76-.9-.55 0-1.08.15-1.6.46-.5.3-.99.77-1.46 1.39l-3.41-.32 3.22-13.17h10.63l-.79 4.61h-7.01l-.97 3.97c.4-.22.8-.39 1.2-.49.41-.12.81-.18 1.21-.18 1.57 0 2.88.65 3.94 1.95s1.6 3.09 1.6 5.36c0 1.97-.41 3.81-1.22 5.52-.79 1.71-1.81 3-3.05 3.85-1.25.84-2.54 1.27-3.89 1.27-1.76 0-3.23-.7-4.41-2.09-1.17-1.22-1.89-3.01-2.16-5.36'
      />
      <path
        id='28'
        data-name='28n'
        className='cls-8 '
        d='m1145.74 38.59-4.06-.74c.35-2.46 1.13-4.31 2.34-5.54 1.2-1.24 2.69-1.86 4.44-1.86 1.9 0 3.43.63 4.59 1.88s1.74 2.94 1.74 5.04c0 1.64-.43 3.24-1.28 4.8q-.945 1.725-3.87 5.04c-1.77 1.99-2.97 3.49-3.62 4.5h7.33l-.79 4.61h-12.83c.21-1.91.82-3.73 1.83-5.47.68-1.17 2.26-3.21 4.76-6.1 1.82-2.11 2.98-3.57 3.48-4.39.66-1.1.98-2.11.98-3.02s-.21-1.63-.65-2.14c-.42-.53-.94-.79-1.56-.79-1.44 0-2.39 1.39-2.83 4.18m14.96 3.55c-.74-.6-1.3-1.28-1.67-2.06-.38-.77-.56-1.64-.56-2.6 0-1.28.29-2.47.89-3.59.61-1.12 1.41-1.98 2.39-2.57 1-.59 2.11-.88 3.34-.88 1.8 0 3.24.57 4.34 1.72 1.11 1.14 1.66 2.66 1.66 4.57 0 1.3-.3 2.44-.9 3.41q-.885 1.44-2.67 2.37c.87.64 1.51 1.43 1.93 2.36.44.91.65 1.96.65 3.15 0 2.13-.59 4.03-1.79 5.68-1.48 2.05-3.41 3.08-5.8 3.08-1.81 0-3.29-.65-4.47-1.95q-1.74-1.95-1.74-5.19 0-2.655 1.2-4.68c.81-1.35 1.88-2.29 3.2-2.83Zm1.56-4.33c0 .83.21 1.49.62 1.99q.63.72 1.56.72c.72 0 1.36-.33 1.94-1 .57-.67.86-1.48.86-2.43 0-.86-.21-1.54-.64-2.04-.41-.5-.93-.76-1.56-.76-.74 0-1.39.33-1.95 1-.55.66-.83 1.49-.83 2.51h.01Zm-1.84 11.92c0 1 .22 1.76.67 2.3.44.53 1 .79 1.68.79 1.01 0 1.81-.54 2.41-1.63.61-1.1.91-2.38.91-3.83 0-.9-.22-1.62-.68-2.16s-1.02-.81-1.69-.81c-.85 0-1.62.48-2.3 1.44-.67.96-1 2.26-1 3.9'
      />
      <path
        id='29'
        data-name='29n'
        className='cls-8 '
        d='m972.28 38.42-4.06-.74c.35-2.46 1.13-4.31 2.34-5.54 1.21-1.24 2.69-1.86 4.45-1.86 1.9 0 3.43.63 4.59 1.88s1.74 2.94 1.74 5.04c0 1.64-.43 3.24-1.28 4.8q-.945 1.725-3.87 5.04c-1.77 1.99-2.98 3.49-3.62 4.5h7.33l-.79 4.61h-12.83c.21-1.91.82-3.73 1.83-5.47.68-1.17 2.27-3.21 4.76-6.1 1.82-2.11 2.98-3.57 3.48-4.39.66-1.1.98-2.11.98-3.02s-.22-1.63-.65-2.14c-.42-.53-.94-.79-1.56-.79-1.44 0-2.39 1.39-2.83 4.18zm10.49 11.79 3.87-.39c.08 1.02.28 1.73.6 2.14q.495.6 1.32.6c.83 0 1.56-.47 2.18-1.42q.945-1.44 1.56-4.29c-.9.7-1.85 1.05-2.83 1.05-1.6 0-2.95-.66-4.03-1.99-1.07-1.34-1.6-3.2-1.6-5.59 0-2.67.69-5.02 2.06-7.03 1.37-2.02 3.16-3.02 5.36-3.02q1.725 0 3.09 1.02c.91.67 1.64 1.71 2.18 3.11.55 1.39.83 3.28.83 5.66 0 4.88-.87 8.85-2.61 11.92-1.75 3.07-3.92 4.61-6.52 4.61-1.55 0-2.81-.55-3.8-1.65-.98-1.1-1.54-2.68-1.65-4.73m10.62-11.92c0-1.36-.22-2.36-.67-2.99-.43-.64-.98-.97-1.64-.97-.81 0-1.51.46-2.09 1.39-.76 1.23-1.14 2.88-1.14 4.94q0 2.01.63 2.97c.43.63.96.95 1.58.95.5 0 1.01-.22 1.51-.65.52-.43.94-1.16 1.28-2.18.35-1.02.53-2.17.53-3.46Z'
      />
      <path
        id='22'
        data-name='22n'
        className='cls-8 '
        d='M811.58,38.64l-4.06-.74c.35-2.46,1.13-4.31,2.34-5.54,1.21-1.24,2.69-1.86,4.45-1.86,1.9,0,3.43.63,4.59,1.88,1.16,1.25,1.74,2.94,1.74,5.04,0,1.64-.43,3.24-1.28,4.8-.63,1.15-1.92,2.83-3.87,5.04-1.77,1.99-2.98,3.49-3.62,4.5h7.33l-.79,4.61h-12.83c.21-1.91.82-3.73,1.83-5.47.68-1.17,2.27-3.21,4.76-6.1,1.82-2.11,2.98-3.57,3.48-4.39.66-1.1.98-2.11.98-3.02s-.22-1.63-.65-2.14c-.42-.53-.94-.79-1.57-.79-1.44,0-2.39,1.39-2.83,4.18ZM828,38.64l-4.06-.74c.35-2.46,1.13-4.31,2.34-5.54,1.21-1.24,2.69-1.86,4.45-1.86,1.9,0,3.43.63,4.59,1.88,1.16,1.25,1.74,2.94,1.74,5.04,0,1.64-.43,3.24-1.28,4.8-.63,1.15-1.92,2.83-3.87,5.04-1.77,1.99-2.98,3.49-3.62,4.5h7.33l-.79,4.61h-12.83c.21-1.91.82-3.73,1.83-5.47.68-1.17,2.27-3.21,4.76-6.1,1.82-2.11,2.98-3.57,3.48-4.39.66-1.1.98-2.11.98-3.02s-.22-1.63-.65-2.14c-.42-.53-.94-.79-1.56-.79-1.44,0-2.39,1.39-2.83,4.18h-.01Z'
      />
      <path
        id='31'
        data-name='31n'
        className='cls-8 '
        d='m645.82 49.45 3.96-.58q.24 2.055.9 2.85c.43.53 1.01.79 1.72.79.9 0 1.67-.38 2.3-1.12.63-.76.95-1.76.95-2.99 0-1.11-.26-1.96-.77-2.55-.52-.6-1.18-.9-2-.9-.2 0-.42.02-.65.07l.72-4.2.42.04c1.08 0 1.91-.33 2.5-.98.59-.67.88-1.61.88-2.83 0-.86-.2-1.52-.6-1.99s-.9-.7-1.51-.7c-1.3 0-2.21 1.18-2.72 3.53l-3.78-.91c.52-2.04 1.26-3.58 2.23-4.62 1.27-1.36 2.79-2.04 4.57-2.04s3.18.61 4.27 1.83q1.65 1.815 1.65 4.89c0 1.38-.33 2.61-1 3.67-.66 1.07-1.53 1.84-2.62 2.32.83.54 1.48 1.25 1.93 2.14.46.88.68 1.87.68 2.97 0 2.03-.63 3.87-1.88 5.54-1.49 1.97-3.36 2.95-5.62 2.95-1.73 0-3.19-.6-4.36-1.81-1.17-1.22-1.89-3.01-2.16-5.36h-.01Zm18.41-8.07.77-4.5q5.355-2.85 8.28-6.63h2.51l-4.45 25.93h-4.17l3.08-17.86c-.84.67-1.83 1.28-2.97 1.85-1.12.56-2.15.97-3.06 1.21Z'
      />
      <path
        id='20'
        data-name='20n'
        className='cls-8 '
        d='m490.58 38.42-4.06-.74c.35-2.46 1.13-4.31 2.34-5.54 1.21-1.24 2.69-1.86 4.45-1.86 1.9 0 3.43.63 4.59 1.88s1.74 2.94 1.74 5.04c0 1.64-.43 3.24-1.28 4.8q-.945 1.725-3.87 5.04c-1.77 1.99-2.98 3.49-3.62 4.5h7.33l-.79 4.61h-12.83c.21-1.91.82-3.73 1.83-5.47.68-1.17 2.27-3.21 4.76-6.1 1.82-2.11 2.98-3.57 3.48-4.39.66-1.1.98-2.11.98-3.02s-.22-1.63-.65-2.14c-.42-.53-.94-.79-1.56-.79-1.44 0-2.39 1.39-2.83 4.18zm10.51 9.35c0-3.33.53-6.51 1.58-9.55 1.07-3.05 2.33-5.21 3.78-6.49 1.1-.97 2.34-1.46 3.73-1.46 1.7 0 3.1.76 4.2 2.27 1.11 1.5 1.67 3.7 1.67 6.61 0 3.33-.53 6.51-1.58 9.54-1.04 3.04-2.29 5.19-3.74 6.47-1.09.95-2.34 1.42-3.74 1.42-1.67 0-3.08-.74-4.2-2.23s-1.69-3.69-1.69-6.59Zm4.06 1.02c0 1.38.18 2.36.55 2.92.38.56.85.84 1.42.84.73 0 1.39-.4 1.99-1.21.79-1.09 1.46-3.17 2.04-6.24.59-3.08.88-5.46.88-7.14 0-1.29-.19-2.21-.56-2.78-.38-.57-.85-.86-1.42-.86-.69 0-1.32.4-1.88 1.2-.89 1.25-1.65 3.56-2.27 6.91-.49 2.62-.74 4.75-.74 6.36z'
      />
      <path
        id='33'
        data-name='33n'
        className='cls-8 '
        d='m324.08 49.62 3.95-.58q.24 2.055.9 2.85c.43.53 1.01.79 1.72.79.9 0 1.67-.38 2.3-1.12.63-.76.95-1.76.95-2.99 0-1.11-.26-1.96-.77-2.55-.52-.6-1.18-.9-2-.9-.2 0-.42.02-.65.07l.72-4.2.42.04c1.08 0 1.91-.33 2.5-.98.59-.67.88-1.61.88-2.83 0-.86-.2-1.52-.6-1.99s-.9-.7-1.51-.7c-1.3 0-2.21 1.18-2.73 3.53l-3.78-.91c.51-2.04 1.26-3.58 2.23-4.62 1.27-1.36 2.79-2.04 4.57-2.04s3.18.61 4.27 1.83q1.65 1.815 1.65 4.89c0 1.38-.33 2.61-1 3.67-.66 1.07-1.53 1.84-2.62 2.32.83.54 1.48 1.25 1.93 2.14.46.88.68 1.87.68 2.97 0 2.03-.63 3.87-1.88 5.54-1.49 1.97-3.36 2.95-5.62 2.95-1.73 0-3.19-.6-4.36-1.81-1.17-1.22-1.89-3.01-2.16-5.36Zm16.42 0 3.96-.58q.24 2.055.9 2.85c.43.53 1.01.79 1.72.79.9 0 1.67-.38 2.3-1.12.63-.76.95-1.76.95-2.99 0-1.11-.26-1.96-.77-2.55-.52-.6-1.18-.9-2-.9-.2 0-.42.02-.65.07l.72-4.2.42.04c1.08 0 1.91-.33 2.5-.98.59-.67.88-1.61.88-2.83 0-.86-.2-1.52-.6-1.99s-.9-.7-1.51-.7c-1.3 0-2.21 1.18-2.73 3.53l-3.78-.91c.51-2.04 1.26-3.58 2.23-4.62 1.27-1.36 2.79-2.04 4.57-2.04s3.18.61 4.27 1.83q1.65 1.815 1.65 4.89c0 1.38-.33 2.61-1 3.67-.66 1.07-1.53 1.84-2.62 2.32.83.54 1.48 1.25 1.93 2.14.46.88.68 1.87.68 2.97 0 2.03-.63 3.87-1.88 5.54-1.49 1.97-3.36 2.95-5.62 2.95-1.73 0-3.19-.6-4.36-1.81-1.17-1.22-1.89-3.01-2.16-5.36Z'
      />
      <path
        id='24'
        data-name='24n'
        className='cls-8 '
        d='m147.69 70.26-4.06-.74c.35-2.46 1.13-4.31 2.34-5.54 1.21-1.24 2.69-1.86 4.45-1.86 1.9 0 3.43.63 4.59 1.88s1.74 2.94 1.74 5.04c0 1.64-.43 3.24-1.28 4.8q-.945 1.725-3.87 5.04c-1.77 1.99-2.98 3.49-3.62 4.5h7.33l-.79 4.61h-12.83c.21-1.91.82-3.73 1.83-5.47.68-1.17 2.27-3.21 4.76-6.1 1.82-2.11 2.98-3.57 3.48-4.39.66-1.1.98-2.11.98-3.02s-.22-1.63-.65-2.14c-.42-.53-.94-.79-1.57-.79-1.44 0-2.39 1.39-2.83 4.18m17.73 12.48h-8.31l.72-4.22 11.44-16.29h3.55l-2.76 16.22h2.55l-.74 4.29h-2.55l-.91 5.26h-3.9zm.74-4.29 1.37-8.03-5.57 8.03z'
      />
      <path
        id='23'
        data-name='23n'
        className='cls-8 '
        d='m43.49 300.26-4.06-.74c.35-2.46 1.13-4.31 2.34-5.54 1.21-1.24 2.69-1.86 4.45-1.86 1.9 0 3.43.63 4.59 1.88s1.74 2.93 1.74 5.04c0 1.64-.43 3.24-1.28 4.8q-.945 1.725-3.87 5.04c-1.77 1.99-2.98 3.49-3.62 4.5h7.33l-.79 4.61H37.49c.21-1.91.82-3.73 1.83-5.47.68-1.17 2.27-3.2 4.76-6.1 1.82-2.11 2.98-3.57 3.48-4.39.66-1.1.98-2.11.98-3.02s-.22-1.63-.65-2.14c-.42-.53-.94-.79-1.56-.79-1.44 0-2.38 1.4-2.83 4.18zm10.12 11.01 3.96-.58q.24 2.055.9 2.85c.43.53 1.01.79 1.72.79.9 0 1.67-.38 2.3-1.12.63-.76.95-1.76.95-2.99 0-1.11-.26-1.96-.77-2.55-.52-.6-1.18-.9-2-.9-.2 0-.42.02-.65.07l.72-4.2.42.04c1.08 0 1.91-.33 2.5-.98.59-.67.88-1.61.88-2.83 0-.86-.2-1.52-.6-1.99s-.9-.7-1.51-.7c-1.3 0-2.21 1.18-2.72 3.53l-3.78-.91c.52-2.04 1.26-3.58 2.23-4.62 1.27-1.36 2.79-2.04 4.57-2.04s3.18.61 4.27 1.83q1.65 1.815 1.65 4.89c0 1.38-.33 2.61-1 3.67-.66 1.07-1.53 1.84-2.62 2.32.83.54 1.48 1.25 1.93 2.14q.69 1.32.69 2.97c0 2.03-.63 3.87-1.88 5.54-1.49 1.97-3.36 2.95-5.62 2.95-1.73 0-3.19-.6-4.36-1.81-1.17-1.22-1.89-3.01-2.16-5.36h-.02Z'
      />
      <path
        id='30'
        data-name='30n'
        className='cls-8 '
        d='m146.38 451.93 3.96-.58q.24 2.055.9 2.85c.43.53 1.01.79 1.72.79.9 0 1.67-.38 2.3-1.12.63-.76.95-1.76.95-2.99 0-1.11-.26-1.96-.77-2.55-.52-.6-1.18-.9-2-.9-.2 0-.42.02-.65.07l.72-4.2.42.04c1.08 0 1.91-.33 2.5-.98.59-.67.88-1.61.88-2.83 0-.86-.2-1.52-.6-1.99s-.9-.7-1.51-.7c-1.3 0-2.21 1.18-2.72 3.53l-3.78-.91c.52-2.04 1.26-3.58 2.23-4.62 1.27-1.36 2.79-2.04 4.57-2.04s3.18.61 4.27 1.83q1.65 1.815 1.65 4.89c0 1.38-.33 2.61-1 3.67-.66 1.07-1.53 1.84-2.62 2.32.83.54 1.48 1.25 1.93 2.14q.69 1.32.69 2.97c0 2.03-.63 3.87-1.88 5.54-1.49 1.97-3.36 2.95-5.62 2.95-1.74 0-3.19-.6-4.36-1.81-1.17-1.22-1.89-3.01-2.16-5.36h-.02Zm16.8-1.65c0-3.33.53-6.51 1.58-9.55 1.07-3.05 2.33-5.21 3.78-6.49 1.1-.97 2.34-1.46 3.73-1.46 1.7 0 3.1.76 4.2 2.27 1.11 1.5 1.67 3.7 1.67 6.61 0 3.33-.53 6.51-1.58 9.55-1.04 3.04-2.29 5.19-3.74 6.47-1.09.95-2.34 1.42-3.74 1.42-1.68 0-3.08-.74-4.2-2.23s-1.69-3.69-1.69-6.59Zm4.06 1.02c0 1.38.18 2.36.55 2.92.38.56.85.84 1.42.84.73 0 1.39-.4 1.99-1.21q1.185-1.635 2.04-6.24c.58-3.08.88-5.46.88-7.14 0-1.29-.19-2.21-.56-2.78-.38-.57-.85-.86-1.42-.86-.69 0-1.32.4-1.88 1.2-.89 1.25-1.65 3.56-2.27 6.91-.49 2.62-.74 4.75-.74 6.36Z'
      />
      <path
        id='36'
        data-name='36n'
        className='cls-8 '
        d='m323.86 483.7 3.96-.58q.24 2.055.9 2.85c.43.53 1.01.79 1.72.79.9 0 1.67-.38 2.3-1.12.63-.76.95-1.76.95-2.99 0-1.11-.26-1.96-.77-2.55-.51-.6-1.18-.9-2-.9-.2 0-.42.02-.65.07l.72-4.2.42.04c1.08 0 1.91-.33 2.5-.99.59-.67.88-1.61.88-2.83 0-.86-.2-1.52-.6-1.99s-.9-.7-1.51-.7c-1.3 0-2.21 1.18-2.72 3.53l-3.78-.91c.52-2.04 1.26-3.58 2.23-4.62 1.27-1.36 2.79-2.04 4.57-2.04s3.18.61 4.27 1.83q1.65 1.815 1.65 4.89c0 1.38-.33 2.61-1 3.67-.66 1.07-1.53 1.84-2.62 2.32.83.54 1.48 1.25 1.93 2.14q.69 1.32.69 2.97c0 2.03-.63 3.87-1.88 5.54-1.49 1.97-3.36 2.95-5.62 2.95-1.73 0-3.19-.6-4.36-1.81-1.17-1.22-1.89-3.01-2.16-5.36zm31.89-12.74-3.87.39c-.08-1.03-.29-1.75-.61-2.14-.32-.41-.74-.62-1.28-.62-.83 0-1.57.48-2.2 1.44-.62.95-1.14 2.38-1.55 4.29.89-.71 1.83-1.07 2.83-1.07 1.61 0 2.94.67 4.01 2 1.08 1.34 1.62 3.2 1.62 5.59 0 2.71-.7 5.06-2.09 7.07-1.38 1.99-3.14 2.99-5.27 2.99q-1.74 0-3.15-1.02c-.93-.69-1.66-1.73-2.21-3.11-.54-1.39-.81-3.29-.81-5.68 0-4.85.87-8.82 2.62-11.9s3.92-4.62 6.52-4.62c1.54 0 2.78.52 3.73 1.56s1.52 2.65 1.72 4.83zm-10.6 11.9c0 1.37.22 2.38.65 3.02.45.63 1 .95 1.65.95.81 0 1.5-.46 2.07-1.39.77-1.23 1.16-2.87 1.16-4.92 0-1.35-.22-2.34-.65-2.97-.43-.64-.96-.97-1.58-.97-.49 0-1 .22-1.51.65-.5.42-.93 1.14-1.28 2.16-.34 1.02-.51 2.17-.51 3.46Z'
      />
      <path
        id='27'
        data-name='27n'
        className='cls-8 '
        d='m490.11 473.12-4.06-.74c.35-2.46 1.13-4.31 2.34-5.54 1.21-1.24 2.69-1.86 4.45-1.86 1.9 0 3.43.63 4.59 1.88s1.74 2.93 1.74 5.04c0 1.64-.43 3.24-1.28 4.8q-.945 1.725-3.87 5.04c-1.77 1.99-2.98 3.49-3.62 4.5h7.33l-.79 4.61h-12.83c.21-1.91.82-3.73 1.83-5.47.68-1.17 2.27-3.2 4.76-6.1 1.82-2.11 2.98-3.57 3.48-4.39.66-1.1.98-2.11.98-3.02s-.22-1.63-.65-2.14c-.42-.53-.94-.79-1.56-.79-1.44 0-2.39 1.4-2.83 4.18zm11.67-3.02.79-4.66h13.94l-.61 3.59c-.73.71-1.49 1.69-2.3 2.92a39 39 0 0 0-2.97 5.4c-1.14 2.55-2.03 4.89-2.67 7-.89 2.94-1.45 5.11-1.67 6.52h-3.94c.24-1.71.78-3.93 1.64-6.64.86-2.72 1.85-5.23 2.97-7.54a45.7 45.7 0 0 1 3.87-6.57h-9.04v-.02Z'
      />
      <path
        id='34'
        data-name='34n'
        className='cls-8 '
        d='m645.09 483.91 3.96-.58q.24 2.055.9 2.85c.43.53 1.01.79 1.72.79.9 0 1.67-.38 2.3-1.12.63-.76.95-1.76.95-2.99 0-1.11-.26-1.96-.77-2.55-.52-.6-1.18-.9-2-.9-.2 0-.42.02-.65.07l.72-4.2.42.04c1.08 0 1.91-.33 2.5-.99.59-.67.88-1.61.88-2.83 0-.86-.2-1.52-.6-1.99s-.9-.7-1.51-.7c-1.3 0-2.21 1.18-2.72 3.53l-3.78-.91c.52-2.04 1.26-3.58 2.23-4.62 1.27-1.36 2.79-2.04 4.57-2.04s3.18.61 4.27 1.83q1.65 1.815 1.65 4.89c0 1.38-.33 2.61-1 3.67-.66 1.07-1.53 1.84-2.62 2.32.83.54 1.48 1.25 1.93 2.14.46.88.68 1.87.68 2.97 0 2.03-.63 3.87-1.88 5.54-1.49 1.97-3.36 2.95-5.62 2.95-1.73 0-3.19-.6-4.36-1.81-1.17-1.22-1.89-3.01-2.16-5.36zm24.03 1.47h-8.31l.72-4.22 11.44-16.3h3.55l-2.76 16.23h2.55l-.74 4.29h-2.55l-.91 5.26h-3.9zm.74-4.29 1.37-8.03-5.57 8.03z'
      />
      <path
        id='25'
        data-name='25n'
        className='cls-8 '
        d='m811.47 472.9-4.06-.74c.35-2.46 1.13-4.31 2.34-5.54 1.21-1.24 2.69-1.86 4.45-1.86 1.9 0 3.43.63 4.59 1.88q1.74 1.875 1.74 5.04c0 1.64-.43 3.24-1.28 4.8q-.945 1.725-3.87 5.04c-1.77 1.99-2.98 3.49-3.62 4.5h7.33l-.79 4.61h-12.83c.21-1.91.82-3.73 1.83-5.47.68-1.17 2.27-3.2 4.76-6.1 1.82-2.11 2.98-3.57 3.48-4.39.66-1.1.98-2.11.98-3.02s-.22-1.63-.65-2.14c-.42-.53-.94-.79-1.57-.79-1.44 0-2.39 1.4-2.83 4.18m10.5 10.36 4.03-.44-.02.53c0 1.21.22 2.11.67 2.71.45.59 1 .88 1.67.88.98 0 1.88-.57 2.69-1.7.81-1.14 1.21-2.72 1.21-4.75 0-1.22-.23-2.13-.68-2.73-.46-.6-1.04-.9-1.76-.9-.55 0-1.08.15-1.6.46-.5.3-.99.77-1.46 1.39l-3.41-.32 3.22-13.17h10.63l-.79 4.61h-7.01l-.97 3.97c.4-.22.8-.39 1.2-.49.41-.12.81-.18 1.21-.18 1.57 0 2.88.65 3.94 1.95s1.6 3.09 1.6 5.36c0 1.97-.41 3.81-1.22 5.52-.79 1.71-1.81 2.99-3.06 3.85-1.24.84-2.54 1.26-3.89 1.26-1.76 0-3.23-.7-4.41-2.09s-1.78-3.3-1.79-5.73h-.01Z'
      />
      <path
        id='21'
        data-name='21n'
        className='cls-8 '
        d='m972.98 473.15-4.06-.74c.35-2.46 1.13-4.31 2.34-5.54 1.21-1.24 2.69-1.86 4.45-1.86 1.9 0 3.43.63 4.59 1.88q1.74 1.875 1.74 5.04c0 1.64-.43 3.24-1.28 4.8q-.945 1.725-3.87 5.04c-1.77 1.99-2.98 3.49-3.62 4.5h7.33l-.79 4.61h-12.83c.21-1.91.82-3.73 1.83-5.47.68-1.17 2.27-3.2 4.76-6.1 1.82-2.11 2.98-3.57 3.48-4.39.66-1.1.99-2.11.99-3.02s-.22-1.63-.65-2.14c-.42-.53-.94-.79-1.56-.79-1.44 0-2.39 1.4-2.83 4.18zm12.11 2.93.77-4.5q5.355-2.85 8.28-6.63h2.51l-4.45 25.93h-4.17l3.08-17.86c-.84.67-1.83 1.28-2.97 1.85-1.12.56-2.15.97-3.06 1.21Z'
      />
      <path
        id='19'
        data-name='19n'
        className='cls-8 '
        d='m1127.61 475.69.77-4.5q5.355-2.85 8.28-6.63h2.51l-4.45 25.93h-4.16l3.08-17.86c-.85.67-1.84 1.28-2.98 1.85s-2.14.97-3.05 1.21m14.8 8.86 3.86-.39c.09 1.02.29 1.73.6 2.14q.495.6 1.32.6c.83 0 1.56-.48 2.18-1.42q.945-1.44 1.56-4.29c-.9.7-1.84 1.05-2.83 1.05-1.6 0-2.94-.66-4.02-1.99-1.07-1.33-1.6-3.2-1.6-5.59 0-2.67.68-5.02 2.06-7.03 1.37-2.01 3.15-3.02 5.36-3.02q1.725 0 3.09 1.02c.91.67 1.64 1.71 2.18 3.11.55 1.39.83 3.28.83 5.66 0 4.88-.88 8.85-2.62 11.92-1.75 3.07-3.92 4.61-6.52 4.61-1.55 0-2.82-.55-3.8-1.65-.98-1.1-1.54-2.68-1.65-4.73m10.61-11.92c0-1.36-.22-2.36-.66-2.99-.44-.64-.98-.97-1.64-.97-.81 0-1.5.46-2.09 1.39-.76 1.23-1.14 2.88-1.14 4.94q0 2.01.63 2.97c.43.63.96.95 1.58.95.5 0 1.01-.22 1.51-.65.52-.43.94-1.16 1.28-2.18.35-1.02.53-2.17.53-3.46'
      />
      <path
        id='32'
        data-name='32n'
        className='cls-8 '
        d='m1310.48 451.93 3.96-.58q.24 2.055.9 2.85c.43.53 1 .79 1.72.79.9 0 1.67-.38 2.3-1.12.63-.76.95-1.76.95-2.99 0-1.11-.26-1.96-.77-2.55q-.78-.9-2.01-.9c-.2 0-.41.02-.65.07l.72-4.2.42.04c1.08 0 1.92-.33 2.5-.98.59-.67.88-1.61.88-2.83 0-.86-.2-1.52-.6-1.99s-.9-.7-1.51-.7c-1.3 0-2.21 1.18-2.72 3.53l-3.78-.91c.51-2.04 1.26-3.58 2.23-4.62 1.26-1.36 2.79-2.04 4.57-2.04s3.18.61 4.27 1.83q1.65 1.815 1.65 4.89c0 1.38-.33 2.61-1 3.67-.65 1.07-1.53 1.84-2.62 2.32.83.54 1.48 1.25 1.94 2.14.45.88.68 1.87.68 2.97 0 2.03-.63 3.87-1.88 5.54-1.49 1.97-3.36 2.95-5.62 2.95-1.74 0-3.19-.6-4.36-1.81-1.17-1.22-1.89-3.01-2.16-5.36Zm22.71-11-4.06-.74c.36-2.46 1.14-4.31 2.34-5.54 1.21-1.24 2.69-1.86 4.45-1.86 1.9 0 3.43.63 4.59 1.88s1.74 2.94 1.74 5.05q0 2.46-1.29 4.8c-.63 1.15-1.92 2.83-3.86 5.05-1.77 1.99-2.98 3.49-3.62 4.5h7.33l-.79 4.61h-12.84c.21-1.91.82-3.73 1.83-5.47.68-1.17 2.27-3.21 4.76-6.1 1.82-2.11 2.98-3.57 3.48-4.39.66-1.1.98-2.11.98-3.02s-.22-1.63-.65-2.14c-.42-.53-.94-.79-1.57-.79-1.44 0-2.38 1.39-2.83 4.18z'
      />
      <path
        id='3'
        data-name='3n'
        className='cls-8 '
        d='m1408.12 171.35 3.95-.58q.24 2.055.9 2.85c.43.53 1 .79 1.72.79.9 0 1.67-.38 2.3-1.12.64-.76.95-1.76.95-2.99 0-1.11-.26-1.96-.77-2.55q-.78-.9-2.01-.9c-.2 0-.41.02-.65.07l.72-4.2.43.04c1.07 0 1.91-.33 2.49-.98.59-.67.88-1.61.88-2.83 0-.86-.2-1.52-.6-1.99-.39-.47-.9-.7-1.51-.7-1.3 0-2.21 1.18-2.72 3.53l-3.78-.91c.51-2.04 1.26-3.58 2.23-4.62 1.27-1.36 2.79-2.04 4.57-2.04s3.18.61 4.27 1.83q1.65 1.815 1.65 4.89c0 1.38-.33 2.61-1 3.67-.65 1.07-1.53 1.84-2.62 2.32a5.5 5.5 0 0 1 1.94 2.14c.45.88.68 1.87.68 2.97 0 2.03-.62 3.87-1.88 5.54-1.49 1.97-3.36 2.95-5.62 2.95-1.74 0-3.19-.6-4.36-1.81-1.17-1.22-1.9-3.01-2.16-5.36Z'
      />
      <path
        id='12'
        data-name='12n'
        className='cls-8 '
        d='m1250.21 44.11.77-4.5q5.37-2.85 8.28-6.63h2.52l-4.45 25.93h-4.17l3.08-17.86c-.84.67-1.83 1.28-2.97 1.85-1.13.56-2.15.97-3.06 1.21m20.73-2.93-4.07-.74c.36-2.46 1.14-4.31 2.34-5.54 1.21-1.24 2.69-1.86 4.45-1.86 1.9 0 3.43.63 4.59 1.88s1.74 2.94 1.74 5.04q0 2.46-1.29 4.8c-.63 1.15-1.92 2.83-3.86 5.04-1.77 1.99-2.98 3.49-3.62 4.5h7.33l-.79 4.61h-12.84c.21-1.91.82-3.73 1.83-5.47.68-1.17 2.27-3.21 4.77-6.1 1.81-2.11 2.97-3.57 3.48-4.39.65-1.1.98-2.11.98-3.02s-.22-1.63-.65-2.14c-.42-.53-.94-.79-1.56-.79-1.45 0-2.39 1.39-2.83 4.18'
      />
      <path
        id='7'
        data-name='7n'
        className='cls-8 '
        d='m1054.69 35.38.79-4.66h13.94l-.62 3.59c-.72.71-1.49 1.69-2.3 2.92a39 39 0 0 0-2.97 5.4c-1.14 2.55-2.03 4.89-2.67 7-.89 2.94-1.45 5.12-1.67 6.52h-3.94c.23-1.71.78-3.93 1.63-6.64.86-2.72 1.85-5.23 2.97-7.54q1.695-3.48 3.87-6.57h-9.03z'
      />
      <path
        id='18'
        data-name='18n'
        className='cls-8 '
        d='m886.73 41.37.77-4.5q5.355-2.85 8.28-6.63h2.51l-4.45 25.93h-4.17l3.08-17.86c-.84.67-1.83 1.28-2.97 1.85-1.12.56-2.14.97-3.06 1.21Zm19.27.61c-.74-.6-1.29-1.28-1.67-2.06-.38-.77-.56-1.64-.56-2.6 0-1.28.3-2.47.9-3.59.61-1.12 1.41-1.98 2.39-2.57 1-.59 2.11-.88 3.34-.88 1.79 0 3.24.57 4.34 1.72 1.1 1.14 1.65 2.66 1.65 4.57 0 1.3-.3 2.44-.9 3.41q-.885 1.44-2.67 2.37c.87.64 1.51 1.43 1.93 2.36.43.91.65 1.96.65 3.15 0 2.13-.6 4.03-1.79 5.68-1.48 2.05-3.41 3.08-5.8 3.08-1.8 0-3.29-.65-4.46-1.95q-1.74-1.95-1.74-5.19 0-2.655 1.2-4.68c.81-1.35 1.88-2.29 3.2-2.83h-.01Zm1.57-4.32c0 .83.21 1.49.62 1.99q.63.72 1.56.72c.71 0 1.36-.33 1.93-1s.86-1.48.86-2.43q0-1.29-.63-2.04c-.41-.5-.93-.76-1.56-.76-.74 0-1.39.33-1.95 1-.55.66-.83 1.49-.83 2.51Zm-1.85 11.92c0 1 .22 1.76.67 2.3.45.53 1.01.79 1.69.79 1.01 0 1.81-.55 2.41-1.64.61-1.1.91-2.38.91-3.83q0-1.35-.69-2.16c-.69-.81-1.02-.81-1.69-.81-.86 0-1.62.48-2.3 1.44-.67.96-1 2.26-1 3.9Z'
      />
      <path
        id='9'
        data-name='9n'
        className='cls-8 '
        d='m733.76 50.21 3.87-.39c.08 1.02.28 1.73.6 2.14q.495.6 1.32.6c.83 0 1.56-.47 2.18-1.42.63-.96 1.16-2.39 1.57-4.29-.9.7-1.85 1.05-2.83 1.05-1.61 0-2.95-.66-4.03-1.99-1.07-1.34-1.6-3.2-1.6-5.59 0-2.67.68-5.02 2.06-7.03 1.37-2.02 3.16-3.02 5.36-3.02q1.725 0 3.09 1.02c.91.67 1.64 1.71 2.18 3.11.55 1.39.83 3.28.83 5.66 0 4.88-.87 8.85-2.61 11.92-1.75 3.07-3.92 4.61-6.52 4.61-1.55 0-2.81-.55-3.8-1.65-.98-1.1-1.54-2.68-1.65-4.73m10.62-11.92c0-1.36-.22-2.36-.67-2.99-.43-.64-.98-.97-1.64-.97-.81 0-1.51.46-2.09 1.39-.76 1.23-1.14 2.88-1.14 4.94q0 2.01.63 2.97c.43.63.96.95 1.58.95.5 0 1.01-.22 1.51-.65.52-.43.94-1.16 1.28-2.18.35-1.02.53-2.17.53-3.46Z'
      />
      <path
        id='14'
        data-name='14n'
        className='cls-8 '
        d='m565.84 41.6.77-4.5c3.58-1.9 6.33-4.11 8.28-6.63h2.51l-4.45 25.93h-4.17l3.08-17.86c-.84.67-1.83 1.28-2.97 1.85-1.12.56-2.15.97-3.06 1.21Zm22.04 9.54h-8.32l.72-4.22 11.44-16.29h3.55l-2.76 16.22h2.55l-.74 4.29h-2.55l-.91 5.26h-3.9l.91-5.26zm.74-4.29 1.37-8.03-5.57 8.03z'
      />
      <path
        id='1'
        data-name='1n'
        className='cls-8 '
        d='m414.27 41.6.77-4.5q5.355-2.85 8.28-6.63h2.51l-4.45 25.93h-4.17l3.08-17.86c-.84.67-1.83 1.28-2.97 1.85-1.12.56-2.14.97-3.06 1.21z'
      />
      <path
        id='16'
        data-name='16n'
        className='cls-8 '
        d='m240.23 43.2.77-4.5q5.355-2.85 8.28-6.63h2.51L247.34 58h-4.17l3.08-17.86c-.84.67-1.83 1.28-2.97 1.85-1.12.56-2.14.97-3.06 1.21Zm29.9-4.68-3.87.39c-.08-1.03-.29-1.75-.61-2.14-.32-.41-.74-.62-1.28-.62-.83 0-1.57.48-2.2 1.44-.62.95-1.14 2.38-1.55 4.29.89-.71 1.83-1.07 2.83-1.07 1.6 0 2.94.67 4.01 2 1.08 1.34 1.62 3.2 1.62 5.59 0 2.71-.7 5.06-2.09 7.07-1.38 1.99-3.14 2.99-5.27 2.99q-1.74 0-3.15-1.02c-.93-.69-1.66-1.73-2.22-3.11-.54-1.39-.81-3.29-.81-5.68 0-4.85.87-8.82 2.62-11.9s3.92-4.62 6.52-4.62c1.53 0 2.78.52 3.73 1.56s1.52 2.65 1.72 4.83m-10.6 11.9c0 1.37.22 2.38.65 3.02.45.63 1 .95 1.65.95.81 0 1.5-.46 2.07-1.39.77-1.23 1.16-2.87 1.16-4.92 0-1.35-.22-2.34-.65-2.97-.43-.64-.96-.97-1.58-.97-.49 0-1 .22-1.51.65-.5.42-.93 1.14-1.28 2.16-.34 1.02-.51 2.17-.51 3.46Z'
      />
      <path
        id='5'
        data-name='5n'
        className='cls-8 '
        d='m81.44 143.62 4.03-.44-.02.53c0 1.21.22 2.11.67 2.71.45.59 1 .88 1.67.88.98 0 1.88-.57 2.69-1.71s1.21-2.72 1.21-4.75c0-1.22-.23-2.13-.69-2.72-.46-.6-1.04-.9-1.76-.9-.55 0-1.08.15-1.6.46-.5.3-.99.77-1.46 1.39l-3.41-.32 3.22-13.17h10.63l-.79 4.61h-7.01l-.97 3.97c.4-.22.8-.39 1.2-.49.41-.12.81-.18 1.21-.18 1.57 0 2.88.65 3.94 1.95s1.6 3.09 1.6 5.36c0 1.97-.4 3.81-1.21 5.52-.8 1.71-1.82 2.99-3.06 3.85-1.24.84-2.54 1.26-3.89 1.26-1.76 0-3.23-.7-4.41-2.09s-1.78-3.3-1.79-5.73h-.01Z'
      />
      <path
        d='M538.59 436c-62.78 0-197.35.75-234.89-.57C263.2 434 219.23 418.5 189.23 402c-31.5-18-89.29-65.04-93.05-145.86C98.6 196.07 145.13 99.6 298.73 86l80.5-.5'
        style={{
          fill: "none",
          strokeWidth: 4,
          stroke: "#c40707",
        }}
      />
      <path
        d='M1181.23 436.99c52.5-1.5 96.99-18.76 135.99-52.26 32.77-25.65 65.65-77.53 62.79-129.15-.73-75.23-64.24-122.15-97.07-143.1-23.35-12.88-39.78-21.67-74.28-25.17'
        style={{
          fill: "none",
          strokeWidth: 4,
          stroke: "#0cae12",
        }}
      />
      <path className='cls-5' d='m780.23 85-399 .5' />
      <path className='cls-5' d='m780.23 85-399 .5' />
      <path className='cls-2' d='M1208.66 87.32 782.24 85' />
      <path className='cls-2' d='m1181.23 436.99-399-.99' />
      <path className='cls-5' d='M780.23 436h-240' />
      <path className='cls-5' d='M780.23 436h-240' />
    </svg>
  )
}
export default SpecialsTable
