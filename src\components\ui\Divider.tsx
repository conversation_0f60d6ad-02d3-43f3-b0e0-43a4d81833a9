import React from 'react'

interface DividerProps {
  className?: string
}

/**
 * Reusable gradient divider component
 */
const Divider = ({ className = '' }: DividerProps) => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="3" viewBox="0 0 325 3" fill="none" className={className}>
      <path d="M0 1.33331H324.5" stroke="url(#paint0_linear_1463_5028)" strokeWidth="2"/>
      <defs>
        <linearGradient id="paint0_linear_1463_5028" x1="0" y1="1.83331" x2="324.5" y2="1.83331" gradientUnits="userSpaceOnUse">
          <stop stopColor="white" stopOpacity="0"/>
          <stop offset="0.45" stopColor="white"/>
          <stop offset="1" stopColor="white" stopOpacity="0"/>
        </linearGradient>
      </defs>
    </svg>
  )
}

export default Divider
