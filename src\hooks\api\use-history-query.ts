import { useApiQuery } from "@/middleware"
import { History } from "./use-api"

// Constants
const ENDPOINTS = {
  UPCOMING_ROUNDS: import.meta.env.VITE_APP_UPCOMING_ROUNDS,
}

/**
 * Custom hook to fetch game history using React Query
 */
export const useHistoryQuery = (lookBehind: number = 10) => {
  return useApiQuery<History[]>(
    ["history", JSON.stringify(lookBehind)],
    `${ENDPOINTS.UPCOMING_ROUNDS}?lookAheadMinutes=-${lookBehind}`,
    null,
    undefined,
    {
      staleTime: 1000 * 60, // 1 minute
      refetchInterval: 1000 * 60 * 5, // 5 minutes
      select: (data) => {
        if (!data) return []

        return data.map((round) => ({
          rouletteNumber: round.rouletteNumber,
          bonusNumber: round.bonusNumber,
        }))
      },
      showErrorToast: true,
      errorToastTitle: "Failed to Load History",
    }
  )
}
