import React from "react"
import { formatChipValue } from "@/lib/formatting"
import { cn } from "@/lib/utils"
import { EnhancedErrorBoundary } from "@/middleware/ui/errorBoundary"
import { useBettingStore } from "@/stores/betting-store"
import { useMobile } from "@/hooks/use-mobile"

// Inner component that will be wrapped with error boundary
const ChipStackInner: React.FC<{
  cellId: string | number | undefined
}> = ({ cellId }) => {
  const placedChips = useBettingStore((state) => state.placedChips)
  const isMobile = useMobile()
  const isDesktop = !isMobile

  // Get chips for the given cellId
  const cell = placedChips.find((cell) => cell.cell_id === cellId)
  if (
    !cell ||
    !Array.isArray(cell.chips_placed) ||
    cell.chips_placed.length === 0
  )
    return null

  const chip = cell.chips_placed[0] // We now know there's only one consolidated chip

  return (
    <div
      className={cn(
        "absolute inset-0 left-1/2 top-1/2 aspect-square lg:w-12 w-9 lg:max-w-none",
        "-translate-x-1/2 -translate-y-1/2",
        chip.value.toString().length < 3 && isDesktop && "text-lg"
      )}
    >
      <img
        src={chip.src}
        alt={chip.alt}
        className={cn(
          "absolute left-1/2 top-1/2 h-auto w-auto -translate-x-1/2 -translate-y-1/2 rounded-full",
          isMobile && "h-auto max-h-10 w-auto"
        )}
      />
      <p className='absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 font-bold'>
        {formatChipValue(chip.value)}
      </p>
    </div>
  )
}

// Main component with error boundary
export const ChipStack: React.FC<{
  cellId: string | number | undefined
}> = (props) => {
  return (
    <EnhancedErrorBoundary context='ChipStack'>
      <ChipStackInner {...props} />
    </EnhancedErrorBoundary>
  )
}
