import type React from "react"
import { cn } from "@/lib/utils"
import { bonusSelectors } from "@/config/bonus-config"
import BonusSymbol from "../bonus-symbol"
import { BONUS_PANEL_STYLES } from "../constants/bonus-panel-styles"

interface BonusSymbolsGridProps {
  isMobile: boolean
  showDragon?: boolean
}

/**
 * Component for displaying the bonus symbols grid layout
 */
export const BonusSymbolsGrid: React.FC<BonusSymbolsGridProps> = ({
  isMobile,
  showDragon = true,
}) => (
  <div className={cn(BONUS_PANEL_STYLES.contentContainer)}>
    {/* Bonus symbols container with auto-sized rows */}
    <div className={cn(BONUS_PANEL_STYLES.symbolsContainer)}>
      {/* Top row - 4 symbols */}
      <div
        className={cn(
          BONUS_PANEL_STYLES.topRow,
          isMobile && showDragon && "gap-2" // Smaller gap for mobile dragon view
        )}
      >
        {/* 200-1 */}
        <BonusSymbol
          bonus={bonusSelectors[0]} // lucky-pot with 200-1 odds
          className={cn(
            BONUS_PANEL_STYLES.symbolSize,
            "justify-self-center" // Position at start of grid cell
          )}
        />
        {/* 20-1 */}
        <BonusSymbol
          bonus={bonusSelectors[1]} // ying-yang with 20-1 odds
          className={cn(
            BONUS_PANEL_STYLES.symbolSize,
            "justify-self-center" // Keep middle item centered
          )}
        />
        {/* 20-1 */}
        <BonusSymbol
          bonus={bonusSelectors[5]} // chinese-coin with 20-1 odds
          className={cn(
            BONUS_PANEL_STYLES.symbolSize,
            "justify-self-center" // Keep middle item centered
          )}
        />
        {/* 200-1 */}
        <BonusSymbol
          bonus={bonusSelectors[6]} // gold-bag with 200-1 odds
          className={cn(
            BONUS_PANEL_STYLES.symbolSize,
            "justify-self-center" // Position at end of grid cell
          )}
        />
      </div>

      {/* Bottom row - 3 symbols (repeating the vertical ones) */}
      <div
        className={cn(
          BONUS_PANEL_STYLES.bottomRow,
          isMobile && showDragon && "gap-2" // Smaller gap for mobile dragon view
        )}
      >
        {/* 3-1 */}
        <BonusSymbol
          bonus={bonusSelectors[2]} // treasure-chest with 3-1 odds
          className={cn(
            BONUS_PANEL_STYLES.symbolSize,
            isMobile && showDragon ? "justify-self-center" : "justify-self-end" // Adjust positioning for mobile
          )}
        />
        {/* 1-1 */}
        <BonusSymbol
          bonus={bonusSelectors[3]} // chinese-fan with 1-1 odds
          className={cn(
            BONUS_PANEL_STYLES.symbolSize,
            "justify-self-center" // Keep middle item centered
          )}
        />
        {/* 3-1 */}
        <BonusSymbol
          bonus={bonusSelectors[4]} // lantern with 3-1 odds
          className={cn(
            BONUS_PANEL_STYLES.symbolSize,
            isMobile && showDragon
              ? "justify-self-center"
              : "justify-self-start" // Adjust positioning for mobile
          )}
        />
      </div>
    </div>
  </div>
)
