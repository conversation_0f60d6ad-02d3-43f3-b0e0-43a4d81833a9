import { useEffect, useReducer, useRef, useState } from "react"
import { useGameStateStore } from "@/stores/game-state-store"

export const getTimestamp = (dateString: string | undefined) => {
  if (!dateString) return 0
  try {
    return new Date(dateString).getTime()
  } catch {
    return 0
  }
}

const phaseReducer = (state, action) => {
  switch (action.type) {
    case "UPDATE_PHASES":
      return {
        ...state,
        isBetting: action.isBetting,
        isSpinning: action.isSpinning,
        isResulting: action.isResulting,
      }
    default:
      return state
  }
}

export const useRoundPhase = () => {
  // Use performance monitoring for phase calculations

  const roundData = useGameStateStore((state) => state.roundData)
  const [phaseState, dispatch] = useReducer(phaseReducer, {
    isBetting: false,
    isSpinning: false,
    isResulting: false,
  })

  const currentTimeRef = useRef(new Date().getTime())
  const [currentTime, setCurrentTime] = useState(currentTimeRef.current)

  useEffect(() => {
    const checkPhases = () => {
      // Use performance monitoring for phase calculations
      const now = new Date().getTime()
      currentTimeRef.current = now

      const closeTime = getTimestamp(roundData?.closeTime)
      const endTime = getTimestamp(roundData?.endTime)
      const resultTime = getTimestamp(roundData?.resultingTime)

      const newIsResulting = now > endTime && now < resultTime
      const newIsSpinning = now > closeTime && now < endTime
      const newIsBetting = !(newIsSpinning || newIsResulting)

      // Only dispatch if the phase has changed
      if (
        newIsResulting !== phaseState.isResulting ||
        newIsSpinning !== phaseState.isSpinning ||
        newIsBetting !== phaseState.isBetting
      ) {
        dispatch({
          type: "UPDATE_PHASES",
          isBetting: newIsBetting,
          isSpinning: newIsSpinning,
          isResulting: newIsResulting,
        })
        setCurrentTime(now)
      }
    }

    checkPhases()
    const interval = setInterval(checkPhases, 100) // Check every 100ms
    return () => clearInterval(interval)
  }, [roundData, phaseState]) // Add phaseState as a dependency

  return {
    Betting: phaseState.isBetting,
    Spinning: phaseState.isSpinning,
    Resulting: phaseState.isResulting,
    startTime: getTimestamp(roundData?.startTime),
    closeTime: getTimestamp(roundData?.closeTime),
    endTime: getTimestamp(roundData?.endTime),
    currentTime,
    roundData,
  }
}
