{"root": ["./src/app.tsx", "./src/main.tsx", "./src/vite-env.d.ts", "./src/components/common/betting-overlay.tsx", "./src/components/common/chip-stack.tsx", "./src/components/common/error-fallback.tsx", "./src/components/common/loading-fallback.tsx", "./src/components/online/control-panels-section.tsx", "./src/components/online/desktop-layout-sections.tsx", "./src/components/online/game-history-display.tsx", "./src/components/online/layout-constants.ts", "./src/components/online/mobile-layout-sections.tsx", "./src/components/online/player-balance-display.tsx", "./src/components/ui/divider.tsx", "./src/components/ui/alert.tsx", "./src/components/ui/badge.tsx", "./src/components/ui/button.tsx", "./src/components/ui/card.tsx", "./src/components/ui/carousel.tsx", "./src/components/ui/checkbox.tsx", "./src/components/ui/custom-slider.tsx", "./src/components/ui/dialog.tsx", "./src/components/ui/game-section.tsx", "./src/components/ui/input.tsx", "./src/components/ui/label.tsx", "./src/components/ui/messagepopover.tsx", "./src/components/ui/popover.tsx", "./src/components/ui/progress.tsx", "./src/components/ui/radio-group.tsx", "./src/components/ui/scroll-area.tsx", "./src/components/ui/scroll-fade.tsx", "./src/components/ui/sheet.tsx", "./src/components/ui/skeleton.tsx", "./src/components/ui/slider.tsx", "./src/components/ui/switch.tsx", "./src/components/ui/table.tsx", "./src/components/ui/tabs.tsx", "./src/components/ui/toast.tsx", "./src/components/ui/toaster.tsx", "./src/components/ui/tooltip.tsx", "./src/components/ui/layout/grid.tsx", "./src/features/bonus-bets/bonus-bets-panel.tsx", "./src/features/bonus-bets/bonus-symbol.tsx", "./src/features/bonus-bets/index.ts", "./src/features/bonus-bets/slot-wheel.tsx", "./src/features/bottom-navigation/bet-history-popover.tsx", "./src/features/bottom-navigation/control-button-skeleton.tsx", "./src/features/bottom-navigation/control-button.tsx", "./src/features/bottom-navigation/control-panel.tsx", "./src/features/bottom-navigation/favorites-popover.tsx", "./src/features/bottom-navigation/index.ts", "./src/features/bottom-navigation/responsive-control-panel.tsx", "./src/features/bottom-navigation/theme-context.tsx", "./src/features/bottom-navigation/visually-hidden.tsx", "./src/features/bottom-navigation/with-error-boundary.tsx", "./src/features/past-results/game-timer.tsx", "./src/features/past-results/past-results-panel.tsx", "./src/features/past-results/roulette-history.tsx", "./src/features/resulting/result-overlay.tsx", "./src/features/roulette-boards/diamond-svg.tsx", "./src/features/roulette-boards/function-layer.tsx", "./src/features/roulette-boards/mobile-specials-table.tsx", "./src/features/roulette-boards/neighbours.tsx", "./src/features/roulette-boards/render-layer.tsx", "./src/features/roulette-boards/roulette-table.tsx", "./src/features/roulette-boards/specials-table.tsx", "./src/features/roulette-boards/table-constants.ts", "./src/features/roulette-boards/table-styles.ts", "./src/features/side-menu/bet-history.tsx", "./src/features/side-menu/help.tsx", "./src/features/side-menu/menu-button.tsx", "./src/features/side-menu/minmax-display.tsx", "./src/features/side-menu/selected-menu-header.tsx", "./src/features/side-menu/settings.tsx", "./src/features/side-menu/sheet-header.tsx", "./src/features/side-menu/side-menu.tsx", "./src/features/side-menu/user-bet-history.tsx", "./src/features/statistics/statistics-panel.tsx", "./src/features/streaming/video-player.tsx", "./src/features/winners/winners-panel.tsx", "./src/hooks/index.ts", "./src/hooks/use-game-data-management.ts", "./src/hooks/use-image-loader.ts", "./src/hooks/use-mobile.ts", "./src/hooks/use-reduced-motion.ts", "./src/hooks/use-responsive-size.ts", "./src/hooks/use-table-text-size.ts", "./src/hooks/use-touch-gestures.ts", "./src/hooks/use-winnings-calculation.ts", "./src/hooks/api/index.ts", "./src/hooks/api/use-api.ts", "./src/hooks/api/use-bet-history-query.ts", "./src/hooks/api/use-bet-types-query.ts", "./src/hooks/api/use-history-query.ts", "./src/hooks/api/use-player-history-query.ts", "./src/hooks/api/use-winnings-query.ts", "./src/hooks/auth/index.ts", "./src/hooks/auth/use-auth.ts", "./src/hooks/game/index.ts", "./src/hooks/game/use-audio-controller.ts", "./src/hooks/game/use-bet-groups.ts", "./src/hooks/game/use-control-panels.tsx", "./src/hooks/game/use-game-notifications.ts", "./src/hooks/game/use-game-phases.ts", "./src/hooks/streaming/index.ts", "./src/hooks/streaming/types.ts", "./src/hooks/streaming/use-hls-player.ts", "./src/hooks/streaming/use-round-data.ts", "./src/hooks/streaming/use-signalr-connection.ts", "./src/hooks/streaming/use-stream-connection.ts", "./src/hooks/streaming/use-video-source.ts", "./src/hooks/ui/index.ts", "./src/hooks/ui/svg-icon.tsx", "./src/hooks/ui/use-carousel.ts", "./src/hooks/ui/use-dimensions.ts", "./src/hooks/ui/use-toast.ts", "./src/hooks/ui/use-virtualized-list.ts", "./src/hooks/utility/index.ts", "./src/hooks/utility/use-backoff.ts", "./src/hooks/utility/use-debounce.ts", "./src/layouts/errorpage.tsx", "./src/layouts/online.tsx", "./src/layouts/retail.tsx", "./src/lib/backoff.ts", "./src/lib/dynamic-import.ts", "./src/lib/formatting.ts", "./src/lib/initialization.ts", "./src/lib/lazy-load.tsx", "./src/lib/logger.ts", "./src/lib/performance.ts", "./src/lib/prefetch.ts", "./src/lib/secure-store.ts", "./src/lib/utils.ts", "./src/middleware/errorhandling.ts", "./src/middleware/errorreporting.ts", "./src/middleware/index.ts", "./src/middleware/api/apiclient.ts", "./src/middleware/api/authmiddleware.ts", "./src/middleware/api/queryprovider.tsx", "./src/middleware/api/useapiquery.ts", "./src/middleware/game/chipmiddleware.ts", "./src/middleware/game/gameeventtypes.ts", "./src/middleware/game/gameeventsmiddleware.tsx", "./src/middleware/storage/persistencemiddleware.ts", "./src/middleware/storage/statemiddleware.ts", "./src/middleware/ui/errorboundary.tsx", "./src/middleware/ui/responsivelayoutmiddleware.tsx", "./src/middleware/ui/toastmiddleware.ts", "./src/middleware/ui/virtuallistmiddleware.tsx", "./src/stores/auth-store.ts", "./src/stores/betting-store.ts", "./src/stores/game-state-store.ts", "./src/stores/settings-store.ts", "./config/audio-config.ts", "./config/bonus-config.ts", "./config/chip-config.ts", "./config/selector-config.ts", "./config/specials-config.ts", "./config/wheel-config.ts"], "errors": true, "version": "5.8.3"}