import type React from "react"
import { EnhancedErrorBoundary } from "@/middleware"
import { BonusPanelInner } from "./components/bonus-panel-inner"
import { BonusSymbolsGrid } from "./components/bonus-symbols-grid"

// Export BonusSymbolsGrid for backward compatibility
export { BonusSymbolsGrid }

/**
 * Component for displaying the Bonus Bets panel with error boundary
 */
const BonusBetsPanel: React.FC = () => {
  return (
    <EnhancedErrorBoundary context='BonusBetsPanel'>
      <BonusPanelInner />
    </EnhancedErrorBoundary>
  )
}

export default BonusBetsPanel
