# Task 06: Configuration Management Enhancement

## Task Overview

**Priority**: Medium | **Risk**: Low | **Impact**: Medium  
**Estimated Duration**: 1 week | **Dependencies**: None (can run in parallel)

### Description
Enhance the existing configuration system with runtime validation, composition utilities, and development-time hot-reloading. This addresses configuration type safety, validation gaps, and opportunities for better developer experience during configuration changes.

### Expected Impact
- Improve configuration type safety and validation
- Enable hot-reloading for faster development iteration
- Better configuration composition and reusability
- Enhanced error handling for configuration issues

## Current State Analysis

### Configuration Structure Assessment

**Well-Structured Configurations**:
- `config/chip-config.ts` - Clean, typed chip definitions
- `config/audio-config.ts` - Comprehensive audio configuration
- `config/bonus-config.ts` - Well-organized bonus selectors
- `config/wheel-config.ts` - Proper wheel configuration

**Areas for Enhancement**:

**Missing Runtime Validation**:
```typescript
// chip-config.ts - No validation for chip values
export const rouletteChips: Chip[] = [
  { value: 2, alt: "Black Chip", src: "/assets/images/chips/black-chip.webp" },
  // No validation that value > 0, src exists, etc.
]
```

**Configuration Composition Opportunities**:
```typescript
// Multiple configs could be composed
// audio-config.ts + settings-store.ts
// chip-config.ts + betting-store.ts
// bonus-config.ts + game-state-store.ts
```

**Development Experience Gaps**:
- No hot-reloading for configuration changes
- Limited configuration debugging utilities
- No configuration validation in development

### Configuration Dependencies
- Audio config used in settings store and audio controller
- Chip config used in betting store and control panels
- Bonus config used across multiple game components
- Layout constants scattered across components

## Target Architecture

### Enhanced Configuration Structure
```
config/
├── core/
│   ├── BaseConfig.ts           (base configuration types)
│   ├── ConfigValidator.ts      (runtime validation)
│   ├── ConfigComposer.ts       (composition utilities)
│   └── ConfigTypes.ts          (shared type definitions)
├── game/
│   ├── chip-config.ts          (enhanced with validation)
│   ├── audio-config.ts         (enhanced with validation)
│   ├── bonus-config.ts         (enhanced with validation)
│   └── wheel-config.ts         (enhanced with validation)
├── ui/
│   ├── layout-config.ts        (consolidated layout config)
│   ├── theme-config.ts         (theme configuration)
│   └── responsive-config.ts    (responsive breakpoints)
├── development/
│   ├── DevConfigManager.ts     (hot-reloading utilities)
│   ├── ConfigDebugger.ts       (debugging tools)
│   └── ConfigValidator.dev.ts  (development validation)
└── index.ts                    (configuration orchestration)
```

### Configuration Validation System
```typescript
// ConfigValidator.ts
import { z } from 'zod'

const ChipSchema = z.object({
  value: z.number().positive('Chip value must be positive'),
  alt: z.string().min(1, 'Alt text is required'),
  src: z.string().url('Invalid image URL').or(z.string().startsWith('/assets/'))
})

const ChipConfigSchema = z.array(ChipSchema).min(1, 'At least one chip required')

export const validateChipConfig = (config: unknown): Chip[] => {
  try {
    return ChipConfigSchema.parse(config)
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new ConfigValidationError('Chip configuration invalid', error.errors)
    }
    throw error
  }
}

// Enhanced chip config with validation
export const rouletteChips: Chip[] = validateChipConfig([
  { value: 2, alt: "Black Chip", src: "/assets/images/chips/black-chip.webp" },
  { value: 5, alt: "Blue Chip", src: "/assets/images/chips/blue-chip.webp" },
  // ... rest of chips
])
```

### Configuration Composition System
```typescript
// ConfigComposer.ts
interface ConfigComposition<T> {
  base: T
  overrides?: Partial<T>
  environment?: 'development' | 'production' | 'test'
  validation?: (config: T) => T
}

export class ConfigComposer {
  static compose<T>(composition: ConfigComposition<T>): T {
    const { base, overrides = {}, environment, validation } = composition
    
    // Apply environment-specific overrides
    const envOverrides = this.getEnvironmentOverrides<T>(environment)
    
    // Merge configurations
    const composed = {
      ...base,
      ...envOverrides,
      ...overrides
    }
    
    // Validate if validator provided
    return validation ? validation(composed) : composed
  }
  
  private static getEnvironmentOverrides<T>(env?: string): Partial<T> {
    if (!env) return {}
    
    try {
      return require(`../environments/${env}.config.js`).default || {}
    } catch {
      return {}
    }
  }
}

// Usage example
export const audioConfig = ConfigComposer.compose({
  base: baseAudioConfig,
  overrides: {
    masterVolume: import.meta.env.DEV ? 0.3 : 0.7
  },
  environment: import.meta.env.MODE,
  validation: validateAudioConfig
})
```

### Hot-Reloading Development System
```typescript
// DevConfigManager.ts
class DevConfigManager {
  private watchers = new Map<string, () => void>()
  private configs = new Map<string, any>()
  
  register<T>(
    configName: string, 
    configPath: string, 
    validator?: (config: unknown) => T
  ): T {
    const config = this.loadConfig(configPath, validator)
    this.configs.set(configName, config)
    
    if (import.meta.env.DEV) {
      this.setupHotReload(configName, configPath, validator)
    }
    
    return config
  }
  
  private setupHotReload<T>(
    configName: string,
    configPath: string,
    validator?: (config: unknown) => T
  ) {
    if (import.meta.hot) {
      import.meta.hot.accept(configPath, (newModule) => {
        try {
          const newConfig = validator 
            ? validator(newModule.default) 
            : newModule.default
            
          this.configs.set(configName, newConfig)
          this.notifyConfigChange(configName, newConfig)
          
          console.log(`🔄 Hot-reloaded config: ${configName}`)
        } catch (error) {
          console.error(`❌ Failed to hot-reload config ${configName}:`, error)
        }
      })
    }
  }
  
  private notifyConfigChange(configName: string, newConfig: any) {
    const watcher = this.watchers.get(configName)
    if (watcher) {
      watcher()
    }
  }
  
  onConfigChange(configName: string, callback: () => void) {
    this.watchers.set(configName, callback)
  }
}

export const devConfigManager = new DevConfigManager()
```

## Step-by-Step Implementation Plan

### Phase 1: Validation System (Days 1-2)

#### Day 1: Create Validation Infrastructure
- [ ] Create `ConfigValidator.ts` with Zod schemas
- [ ] Define validation schemas for all existing configs
- [ ] Create `ConfigValidationError` class
- [ ] Add validation utilities and helpers

#### Day 2: Enhance Existing Configurations
- [ ] Add validation to `chip-config.ts`
- [ ] Add validation to `audio-config.ts`
- [ ] Add validation to `bonus-config.ts`
- [ ] Add validation to `wheel-config.ts`

### Phase 2: Composition System (Days 3-4)

#### Day 3: Create Configuration Composer
- [ ] Create `ConfigComposer.ts` with composition utilities
- [ ] Implement environment-specific overrides
- [ ] Add configuration merging logic
- [ ] Create composition validation

#### Day 4: Implement Configuration Orchestration
- [ ] Create main configuration index
- [ ] Implement configuration dependency management
- [ ] Add configuration caching
- [ ] Create configuration debugging utilities

### Phase 3: Development Tools (Days 5-6)

#### Day 5: Create Hot-Reloading System
- [ ] Create `DevConfigManager.ts` for development
- [ ] Implement configuration watching
- [ ] Add hot-reload notifications
- [ ] Create development configuration utilities

#### Day 6: Create Configuration Debugging Tools
- [ ] Create `ConfigDebugger.ts` with debugging utilities
- [ ] Add configuration inspection tools
- [ ] Implement configuration diff utilities
- [ ] Create configuration validation reporting

### Phase 4: Integration and Testing (Day 7)

#### Day 7: Integration and Documentation
- [ ] Integrate enhanced configurations with existing code
- [ ] Update configuration imports across codebase
- [ ] Create configuration documentation
- [ ] Add comprehensive testing

## Code Examples

### Before: Basic Configuration
```typescript
// chip-config.ts
export type Chip = {
  value: number
  alt: string
  src: string
}

export const rouletteChips: Chip[] = [
  { value: 2, alt: "Black Chip", src: "/assets/images/chips/black-chip.webp" },
  // No validation, no composition, no hot-reloading
]
```

### After: Enhanced Configuration
```typescript
// chip-config.ts
import { z } from 'zod'
import { ConfigComposer, devConfigManager } from '../core'

const ChipSchema = z.object({
  value: z.number().positive('Chip value must be positive'),
  alt: z.string().min(1, 'Alt text is required'),
  src: z.string().refine(
    (src) => src.startsWith('/assets/') || src.startsWith('http'),
    'Invalid image source'
  )
})

const ChipConfigSchema = z.array(ChipSchema).min(1, 'At least one chip required')

const baseChipConfig: Chip[] = [
  { value: 2, alt: "Black Chip", src: "/assets/images/chips/black-chip.webp" },
  { value: 5, alt: "Blue Chip", src: "/assets/images/chips/blue-chip.webp" },
  // ... rest of chips
]

export const rouletteChips: Chip[] = ConfigComposer.compose({
  base: baseChipConfig,
  environment: import.meta.env.MODE,
  validation: (config) => ChipConfigSchema.parse(config)
})

// Development hot-reloading
if (import.meta.env.DEV) {
  devConfigManager.register('chips', './chip-config.ts', ChipConfigSchema.parse)
}
```

### Configuration Composition Example
```typescript
// Composed audio configuration
export const audioConfig = ConfigComposer.compose({
  base: {
    masterVolume: 0.7,
    sfxVolume: 0.8,
    musicVolume: 0.5,
    sounds: soundMap
  },
  overrides: {
    // Development overrides
    ...(import.meta.env.DEV && {
      masterVolume: 0.3, // Quieter in development
      enableDebugLogging: true
    }),
    
    // Production overrides
    ...(import.meta.env.PROD && {
      enableAnalytics: true,
      compressionLevel: 'high'
    })
  },
  validation: validateAudioConfig
})
```

### Hot-Reloading Integration
```typescript
// Component using hot-reloadable config
const ChipSelector = () => {
  const [chips, setChips] = useState(rouletteChips)
  
  useEffect(() => {
    if (import.meta.env.DEV) {
      devConfigManager.onConfigChange('chips', () => {
        setChips(devConfigManager.get('chips'))
      })
    }
  }, [])
  
  return (
    <div>
      {chips.map(chip => (
        <ChipButton key={chip.value} chip={chip} />
      ))}
    </div>
  )
}
```

## Testing Strategy

### Unit Tests
```typescript
// ConfigValidator.test.ts
describe('ConfigValidator', () => {
  it('validates chip configuration correctly', () => {
    const validConfig = [
      { value: 5, alt: "Test Chip", src: "/assets/test.webp" }
    ]
    
    expect(() => validateChipConfig(validConfig)).not.toThrow()
  })
  
  it('throws error for invalid chip configuration', () => {
    const invalidConfig = [
      { value: -5, alt: "", src: "invalid-url" }
    ]
    
    expect(() => validateChipConfig(invalidConfig)).toThrow(ConfigValidationError)
  })
})

// ConfigComposer.test.ts
describe('ConfigComposer', () => {
  it('composes configuration correctly', () => {
    const base = { a: 1, b: 2 }
    const overrides = { b: 3, c: 4 }
    
    const result = ConfigComposer.compose({ base, overrides })
    
    expect(result).toEqual({ a: 1, b: 3, c: 4 })
  })
})
```

### Integration Tests
```typescript
// configuration.integration.test.ts
describe('Configuration Integration', () => {
  it('loads all configurations without errors', () => {
    expect(() => {
      require('../config/chip-config')
      require('../config/audio-config')
      require('../config/bonus-config')
    }).not.toThrow()
  })
  
  it('validates all configurations', () => {
    const configs = [
      rouletteChips,
      audioConfig,
      bonusSelectors
    ]
    
    configs.forEach(config => {
      expect(config).toBeDefined()
      expect(Array.isArray(config) || typeof config === 'object').toBe(true)
    })
  })
})
```

### Development Tests
```typescript
// hot-reload.test.ts (development only)
describe('Hot Reload System', () => {
  it('reloads configuration on file change', async () => {
    const mockCallback = jest.fn()
    
    devConfigManager.onConfigChange('test-config', mockCallback)
    
    // Simulate file change
    await simulateConfigChange('test-config')
    
    expect(mockCallback).toHaveBeenCalled()
  })
})
```

## Risk Mitigation

### Potential Issues
1. **Runtime Performance**: Validation overhead in production
2. **Type Safety**: Dynamic configuration loading
3. **Hot-Reload Stability**: Development environment issues
4. **Configuration Complexity**: Over-engineering simple configs

### Mitigation Strategies
1. **Conditional Validation**: Only validate in development/test
2. **Type Guards**: Comprehensive TypeScript validation
3. **Error Boundaries**: Graceful configuration error handling
4. **Gradual Enhancement**: Start with critical configurations

### Rollback Procedure
```typescript
// Feature flag for enhanced configuration
const USE_ENHANCED_CONFIG = import.meta.env.VITE_USE_ENHANCED_CONFIG !== 'false'

export const rouletteChips = USE_ENHANCED_CONFIG 
  ? enhancedChipConfig 
  : legacyChipConfig
```

## Success Criteria

### Quantitative Metrics
- [ ] 100% of configurations have runtime validation
- [ ] Configuration hot-reloading works in development
- [ ] Zero configuration-related runtime errors
- [ ] Configuration loading time <10ms
- [ ] Test coverage >90% for configuration system

### Qualitative Metrics
- [ ] Improved developer experience with hot-reloading
- [ ] Better configuration debugging capabilities
- [ ] Enhanced type safety for configurations
- [ ] Cleaner configuration composition
- [ ] Better error messages for configuration issues

### Acceptance Criteria
- [ ] All existing configurations enhanced with validation
- [ ] Hot-reloading works for all configurations
- [ ] Configuration composition system functional
- [ ] Development debugging tools working
- [ ] No performance regression in production
- [ ] Configuration errors handled gracefully
- [ ] Documentation complete and accurate
- [ ] Code review approval
- [ ] QA testing passed
