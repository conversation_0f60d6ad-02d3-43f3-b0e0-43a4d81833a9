# Audio System Testing Guide

## Quick Verification Steps

### 1. Check Console Logs
Open browser DevTools (F12) and look for:

**Expected on startup:**
```
[Roulette] [Component: SettingsStore] Preloading sounds with resource management
[Roulette] [Component: SettingsStore] Sound preloading completed
```

**Should NOT see:**
```
Audio instance limit reached (10), skipping sound: [sound-name]
```

### 2. Test Background Music
- Background music and ambience should start automatically
- These should NOT count against the 10-instance limit
- Check console for: `Playing sound: bg-music` and `Playing sound: ambience`

### 3. Test Chip Sounds (Pooled)
- Click rapidly on different roulette table cells
- Should hear chip sounds without any limit warnings
- Check console for: `Playing sound: chip-1[chip-1:0]` or `Playing sound: chip-2[chip-2:1]`
- The `[chip-1:0]` indicates pooled sound with instance index

### 4. Test Voice Cues
- Wait for betting phase to start
- Should hear "bets open" without limit warnings
- Check console for: `Playing sound: rsa-open[rsa-open:0]` (if RSA voice selected)

### 5. Monitor Active Sound Count
Look for debug logs showing:
```
Playing sound: [sound-name] { activeCount: X, isPooled: true/false, isBackground: true/false }
```

## Expected Behavior Changes

### Before Fix:
- Frequent "Audio instance limit reached" warnings
- Background music counted against 10-instance limit
- Chip sounds could be blocked during rapid clicking
- Same audio element reused causing tracking issues

### After Fix:
- No instance limit warnings under normal usage
- Background music/ambience excluded from limits
- Chip sounds have 5 instances each for rapid-fire usage
- Proper cleanup and tracking of audio instances
- Better logging with pool information

## Stress Testing

### Rapid Chip Placement:
1. Select a chip value
2. Click rapidly (10+ times per second) on different table cells
3. Should hear all chip sounds without warnings

### Voice Cue Overlap:
1. Wait for betting phase countdown
2. Should hear "bets open", "10 seconds", "5 seconds", "bets closed" in sequence
3. No sounds should be skipped due to limits

### Background Music Persistence:
1. Background music should continue playing throughout session
2. Should not interfere with sound effects
3. Should not cause limit warnings

## Debugging

### Check Audio Pools:
In browser console, run:
```javascript
// Check current audio state
const store = window.__ZUSTAND_STORES__?.settings || useSettingsStore.getState()
console.log('Regular sounds:', Object.keys(store.sounds))
console.log('Pooled sounds:', Object.keys(store.soundPools))
console.log('Background sounds:', Object.keys(store.backgroundSounds))
console.log('Active sounds count:', store.activeSounds.size)
```

### Monitor Instance Usage:
```javascript
// Monitor active sounds in real-time
setInterval(() => {
  const store = useSettingsStore.getState()
  console.log(`Active: ${store.activeSounds.size}/${store.maxAudioInstances}`)
}, 1000)
```

## Common Issues

### If you still see limit warnings:
1. Check if new sounds were added to audio-config.ts but not categorized
2. Verify POOLED_SOUNDS and BACKGROUND_SOUNDS sets include the right keys
3. Check for memory leaks in activeSounds Set

### If sounds don't play:
1. Check browser audio permissions
2. Verify audio files are accessible from server
3. Check for JavaScript errors in console

### If background music stops:
1. Check if it's being counted against instance limits
2. Verify it's in BACKGROUND_SOUNDS set
3. Check for audio context suspension (user interaction required)
