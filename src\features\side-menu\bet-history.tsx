import { useEffect, useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { getPlayerBetHistory, PlayerBetHistory } from "@/hooks"
import { formatCurrency } from "@/lib/formatting"
import { useAuthStore } from "@/stores/auth-store"
import { useGameStateStore } from "@/stores/game-state-store"

export const UserBetHistory = () => {
  const [loading, setLoading] = useState(true)
  const { tokenData, token } = useAuthStore()
  const [playerHistory, setPlayerHistory] = useState<PlayerBetHistory[]>([])
  const roundData = useGameStateStore((state) => state.roundData)

  useEffect(() => {
    const fetchInitialHistory = async () => {
      if (!tokenData) return
      const playerBetHistory = await getPlayerBetHistory(
        tokenData?.PlayerID,
        token
      )
      if (playerBetHistory) {
        setLoading(false)
        setPlayerHistory(playerBetHistory)
      }
    }
    setLoading(true)
    fetchInitialHistory()
  }, [roundData, token, tokenData?.PlayerID])

  return (
    <div className='flex max-h-[90dvh] flex-col gap-2 overflow-y-auto'>
      {loading
        ? Array.from({ length: 10 }).map((_, index) => (
            <Skeleton key={index} className='h-32 w-full bg-zinc-800' />
          ))
        : playerHistory &&
          Object.entries(playerHistory).map(([drawId, bets]) => (
            <Card
              key={drawId}
              className='w-full rounded-none border-t-2 border-amber-400 bg-zinc-900/70'
            >
              <CardHeader className='p-3 pb-2'>
                <CardTitle>Draw ID: {drawId}</CardTitle>
                <p className='mb-2'>
                  Total Stake: ${formatCurrency(bets.stake)}
                </p>
              </CardHeader>
              <CardContent className='p-2 pt-0'>
                <Table>
                  <TableHeader>
                    <TableRow className='border-amber-400 bg-amber-500 text-base'>
                      <TableHead className='px-4 py-2 font-semibold text-white'>
                        Bet Type
                      </TableHead>
                      <TableHead className='px-4 py-2 font-semibold text-white'>
                        Stake
                      </TableHead>
                      <TableHead className='px-4 py-2 font-semibold text-white'>
                        Numbers
                      </TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    <TableRow>
                      <TableCell>{bets.betTypeName}</TableCell>
                      <TableCell>${bets.stake.toFixed(2)}</TableCell>
                      <TableCell>{bets.betNumbers}</TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          ))}
    </div>
  )
}
