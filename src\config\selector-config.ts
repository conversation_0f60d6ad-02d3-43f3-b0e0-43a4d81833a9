import { Chip } from './chip-config'

export interface SelectableCell {
  // Unique identifier for the cell, can be a number or string eg. "2:1-1" or 26
  cell_id: number | string
  // Array of chips placed on this cell
  chips_placed: Chip[]
  // Optional total value of chips placed on this cell
  total_chip_value?: number
  // Optional ID representing the type of bet associated with this cell
  bet_type_id?: number | null
  // Array of selector strings that determine which numbers this cell covers
  cell_selectors: string[]
  // Boolean indicating whether this cell can be selected
  isSelectable: boolean
  // Optional source of bonus for this cell
  bonusSource?: string
  // Optional string representing bonus odds
  bonusOdds?: string
  // Optional numeric value of bonus odds
  bonusOddValue?: number
}

export const twoToOneCells: SelectableCell[] = [
  {
    cell_id: '2:1-1',
    chips_placed: [],
    bet_type_id: 3,
    cell_selectors: ['3', '6', '9', '12', '15', '18', '21', '24', '27', '30', '33', '36'],
    isSelectable: true,
  },
  {
    cell_id: '2:1-2',
    chips_placed: [],
    bet_type_id: 3,
    cell_selectors: ['2', '5', '8', '11', '14', '17', '20', '23', '26', '29', '32', '35'],
    isSelectable: true,
  },
  {
    cell_id: '2:1-3',
    chips_placed: [],
    bet_type_id: 3,
    cell_selectors: ['1', '4', '7', '10', '13', '16', '19', '22', '25', '28', '31', '34'],
    isSelectable: true,
  },
]

export const firstTwelveCell: SelectableCell = {
  cell_id: '1st 12',
  chips_placed: [],
  bet_type_id: 7,
  cell_selectors: ['3', '6', '9', '2', '5', '8', '1', '4', '7', '12', '11', '10'],
  isSelectable: true,
}

export const secondTwelveCell: SelectableCell = {
  cell_id: '2nd 12',
  chips_placed: [],
  bet_type_id: 7,
  cell_selectors: ['15', '14', '13', '18', '17', '16', '21', '20', '19', '24', '23', '22'],
  isSelectable: true,
}

export const thirdTwelveCell: SelectableCell = {
  cell_id: '3rd 12',
  chips_placed: [],
  bet_type_id: 7,
  cell_selectors: ['27', '26', '25', '30', '29', '28', '33', '32', '31', '36', '35', '34'],
  isSelectable: true,
}

export const redCell: SelectableCell = {
  cell_id: 'Red',
  chips_placed: [],
  bet_type_id: 8,
  cell_selectors: [
    '3',
    '5',
    '1',
    '9',
    '7',
    '12',
    '14',
    '18',
    '16',
    '21',
    '19',
    '23',
    '27',
    '25',
    '30',
    '32',
    '36',
    '34',
  ],
  isSelectable: true,
}

export const blackCell: SelectableCell = {
  cell_id: 'Black',
  chips_placed: [],
  bet_type_id: 9,
  cell_selectors: [
    '2',
    '4',
    '6',
    '8',
    '10',
    '11',
    '13',
    '15',
    '17',
    '20',
    '22',
    '24',
    '26',
    '29',
    '28',
    '31',
    '33',
    '35',
  ],
  isSelectable: true,
}

export const oddCell: SelectableCell = {
  cell_id: 'Odd',
  chips_placed: [],
  bet_type_id: 11,
  cell_selectors: [
    '1',
    '3',
    '5',
    '7',
    '9',
    '11',
    '13',
    '15',
    '17',
    '19',
    '21',
    '23',
    '25',
    '27',
    '29',
    '31',
    '33',
    '35',
  ],
  isSelectable: true,
}

export const evenCell: SelectableCell = {
  cell_id: 'Even',
  chips_placed: [],
  bet_type_id: 10,
  cell_selectors: [
    '2',
    '4',
    '6',
    '8',
    '10',
    '12',
    '14',
    '16',
    '18',
    '20',
    '22',
    '24',
    '26',
    '28',
    '30',
    '32',
    '34',
    '36',
  ],
  isSelectable: true,
}

export const nineteenToThirtySix = {
  cell_id: '19-36',
  chips_placed: [],
  bet_type_id: 19,
  cell_selectors: [
    '19',
    '20',
    '21',
    '22',
    '23',
    '24',
    '25',
    '26',
    '27',
    '28',
    '29',
    '30',
    '31',
    '32',
    '33',
    '34',
    '35',
    '36',
  ],
  isSelectable: true,
}

export const oneToEighteen = {
  cell_id: '1-18',
  chips_placed: [],
  bet_type_id: 18,
  cell_selectors: [
    '1',
    '2',
    '3',
    '4',
    '5',
    '6',
    '7',
    '8',
    '9',
    '10',
    '11',
    '12',
    '13',
    '14',
    '15',
    '16',
    '17',
    '18',
  ],
  isSelectable: true,
}

const createCell = (
  id: number,
  selectors: string[],
  isSelectable: boolean,
  bet_type_id?: number
): SelectableCell => ({
  cell_id: id,
  chips_placed: [],
  cell_selectors: selectors,
  isSelectable,
  bet_type_id,
})

export const cellSelectorsConfig: {
  range: number[]
  selectorsPattern: string[][]
}[] = [
  {
    range: [1, 25],
    selectorsPattern: [
      ['0', '1', '2', '3'],
      ['1', '2', '3'],
      ['1', '2', '3', '4', '5', '6'],
      ['4', '5', '6'],
      ['4', '5', '6', '7', '8', '9'],
      ['7', '8', '9'],
      ['7', '8', '9', '10', '11', '12'],
      ['10', '11', '12'],
      ['10', '11', '12', '13', '14', '15'],
      ['13', '14', '15'],
      ['13', '14', '15', '16', '17', '18'],
      ['16', '17', '18'],
      ['16', '17', '18', '19', '20', '21'],
      ['19', '20', '21'],
      ['19', '20', '21', '22', '23', '24'],
      ['22', '23', '24'],
      ['22', '23', '24', '25', '26', '27'],
      ['25', '26', '27'],
      ['25', '26', '27', '28', '29', '30'],
      ['28', '29', '30'],
      ['28', '29', '30', '31', '32', '33'],
      ['31', '32', '33'],
      ['31', '32', '33', '34', '35', '36'],
      ['34', '35', '36'],
      [],
    ],
  },
  {
    range: [26, 50],
    selectorsPattern: [
      ['0', '3'],
      ['3'],
      ['3', '6'],
      ['6'],
      ['6', '9'],
      ['9'],
      ['9', '12'],
      ['12'],
      ['12', '15'],
      ['15'],
      ['15', '18'],
      ['18'],
      ['18', '21'],
      ['21'],
      ['21', '24'],
      ['24'],
      ['24', '27'],
      ['27'],
      ['27', '30'],
      ['30'],
      ['30', '33'],
      ['33'],
      ['33', '36'],
      ['36'],
      [],
    ],
  },
  {
    range: [51, 75],
    selectorsPattern: [
      ['0', '3', '2'],
      ['3', '2'],
      ['3', '6', '2', '5'],
      ['6', '5'],
      ['6', '9', '5', '8'],
      ['9', '8'],
      ['9', '12', '8', '11'],
      ['12', '11'],
      ['12', '15', '11', '14'],
      ['15', '14'],
      ['15', '18', '14', '17'],
      ['18', '17'],
      ['18', '21', '17', '20'],
      ['21', '20'],
      ['21', '24', '20', '23'],
      ['24', '23'],
      ['24', '27', '23', '26'],
      ['27', '26'],
      ['27', '30', '26', '29'],
      ['30', '29'],
      ['30', '33', '29', '32'],
      ['33', '32'],
      ['33', '36', '32', '35'],
      ['36', '35'],
      [],
    ],
  },
  {
    range: [76, 100],
    selectorsPattern: [
      ['0', '2'],
      ['2'],
      ['2', '5'],
      ['5'],
      ['5', '8'],
      ['8'],
      ['8', '11'],
      ['11'],
      ['11', '14'],
      ['14'],
      ['14', '17'],
      ['17'],
      ['17', '20'],
      ['20'],
      ['20', '23'],
      ['23'],
      ['23', '26'],
      ['26'],
      ['26', '29'],
      ['29'],
      ['29', '32'],
      ['32'],
      ['32', '35'],
      ['35'],
      [],
    ],
  },
  {
    range: [101, 125],
    selectorsPattern: [
      ['0', '2', '1'],
      ['2', '1'],
      ['2', '5', '1', '4'],
      ['5', '4'],
      ['5', '8', '4', '7'],
      ['8', '7'],
      ['8', '11', '7', '10'],
      ['11', '10'],
      ['11', '14', '10', '13'],
      ['14', '13'],
      ['14', '17', '13', '16'],
      ['17', '16'],
      ['17', '20', '16', '19'],
      ['20', '19'],
      ['20', '23', '19', '22'],
      ['23', '22'],
      ['23', '26', '22', '25'],
      ['26', '25'],
      ['26', '29', '25', '28'],
      ['29', '28'],
      ['29', '32', '28', '31'],
      ['32', '31'],
      ['32', '35', '31', '34'],
      ['35', '34'],
      [],
    ],
  },
  {
    range: [126, 150],
    selectorsPattern: [
      ['0', '1'],
      ['1'],
      ['1', '4'],
      ['4'],
      ['4', '7'],
      ['7'],
      ['7', '10'],
      ['10'],
      ['10', '13'],
      ['13'],
      ['13', '16'],
      ['16'],
      ['16', '19'],
      ['19'],
      ['19', '22'],
      ['22'],
      ['22', '25'],
      ['25'],
      ['25', '28'],
      ['28'],
      ['28', '31'],
      ['31'],
      ['31', '34'],
      ['34'],
      [],
    ],
  },
  {
    range: [151, 175],
    selectorsPattern: [
      ['0', '1', '2', '3'],
      ['1', '2', '3'],
      ['1', '4', '2', '5', '3', '6'],
      ['4', '5', '6'],
      ['4', '7', '5', '8', '6', '9'],
      ['7', '8', '9'],
      ['7', '10', '8', '11', '9', '12'],
      ['10', '11', '12'],
      ['10', '13', '11', '14', '12', '15'],
      ['13', '14', '15'],
      ['13', '16', '14', '17', '15', '18'],
      ['16', '17', '18'],
      ['16', '19', '17', '20', '18', '21'],
      ['19', '20', '21'],
      ['19', '22', '20', '23', '21', '24'],
      ['22', '23', '24'],
      ['22', '25', '23', '26', '24', '27'],
      ['25', '26', '27'],
      ['25', '28', '26', '29', '27', '30'],
      ['28', '29', '30'],
      ['28', '31', '29', '32', '30', '33'],
      ['31', '32', '33'],
      ['31', '34', '32', '35', '33', '36'],
      ['34', '35', '36'],
      [],
    ],
  },
]

const SelectableCells: SelectableCell[] = []

const CreateSelectableCells = async () => {
  const nonSelectableIds = [25, 50, 75, 100, 125, 150, 175]
  const promises = cellSelectorsConfig.map(async ({ range, selectorsPattern }) => {
    const [start, end] = range
    for (let i = start; i <= end; i++) {
      const selectors = selectorsPattern[i - start] || []
      const isSelectable = !nonSelectableIds.includes(i)
      SelectableCells.push(createCell(i, selectors, isSelectable))
    }
  })
  await Promise.all(promises)
}

await CreateSelectableCells()

export const rouletteSelectors = SelectableCells.sort(
  (a, b) => parseInt(a.cell_id?.toString() ?? '') - parseInt(b.cell_id?.toString() ?? '')
)
