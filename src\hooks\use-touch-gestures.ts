import { useRef, useEffect, useState } from "react"

interface TouchGestureOptions {
  onSwipeLeft?: () => void
  onSwipeRight?: () => void
  onSwipeUp?: () => void
  onSwipeDown?: () => void
  threshold?: number
}

/**
 * Custom hook to handle touch gestures for scrolling
 * @param options Configuration options for gestures
 * @returns Ref to attach to the element and current gesture state
 */
export function useTouchGestures<T extends HTMLElement>({
  onSwipeLeft,
  onSwipeRight,
  onSwipeUp,
  onSwipeDown,
  threshold = 50,
}: TouchGestureOptions = {}) {
  const elementRef = useRef<T>(null)
  const [isSwiping, setIsSwiping] = useState(false)
  const touchStartXRef = useRef<number | null>(null)
  const touchStartYRef = useRef<number | null>(null)

  useEffect(() => {
    const element = elementRef.current
    if (!element) return

    const handleTouchStart = (e: TouchEvent) => {
      touchStartXRef.current = e.touches[0].clientX
      touchStartYRef.current = e.touches[0].clientY
      setIsSwiping(true)
    }

    const handleTouchMove = (e: TouchEvent) => {
      if (touchStartXRef.current === null || touchStartYRef.current === null)
        return

      const touchCurrentX = e.touches[0].clientX
      const touchCurrentY = e.touches[0].clientY
      const diffX = touchStartXRef.current - touchCurrentX
      const diffY = touchStartYRef.current - touchCurrentY

      // Determine if this is primarily a horizontal or vertical swipe
      const isHorizontalSwipe = Math.abs(diffX) > Math.abs(diffY)

      // Prevent default to avoid page scrolling while swiping the component
      // Only if we have a handler for the detected direction
      if (
        (isHorizontalSwipe &&
          Math.abs(diffX) > 10 &&
          (onSwipeLeft || onSwipeRight)) ||
        (!isHorizontalSwipe &&
          Math.abs(diffY) > 10 &&
          (onSwipeUp || onSwipeDown))
      ) {
        e.preventDefault()
      }
    }

    const handleTouchEnd = (e: TouchEvent) => {
      if (touchStartXRef.current === null || touchStartYRef.current === null)
        return

      const touchEndX = e.changedTouches[0].clientX
      const touchEndY = e.changedTouches[0].clientY
      const diffX = touchStartXRef.current - touchEndX
      const diffY = touchStartYRef.current - touchEndY

      // Determine if this is primarily a horizontal or vertical swipe
      const isHorizontalSwipe = Math.abs(diffX) > Math.abs(diffY)

      if (isHorizontalSwipe) {
        // Handle horizontal swipes
        if (diffX > threshold && onSwipeLeft) {
          onSwipeLeft()
        } else if (diffX < -threshold && onSwipeRight) {
          onSwipeRight()
        }
      } else {
        // Handle vertical swipes
        if (diffY > threshold && onSwipeUp) {
          onSwipeUp()
        } else if (diffY < -threshold && onSwipeDown) {
          onSwipeDown()
        }
      }

      touchStartXRef.current = null
      touchStartYRef.current = null
      setIsSwiping(false)
    }

    element.addEventListener("touchstart", handleTouchStart, { passive: false })
    element.addEventListener("touchmove", handleTouchMove, { passive: false })
    element.addEventListener("touchend", handleTouchEnd)

    return () => {
      element.removeEventListener("touchstart", handleTouchStart)
      element.removeEventListener("touchmove", handleTouchMove)
      element.removeEventListener("touchend", handleTouchEnd)
    }
  }, [onSwipeLeft, onSwipeRight, onSwipeUp, onSwipeDown, threshold])

  return { ref: elementRef, isSwiping }
}
