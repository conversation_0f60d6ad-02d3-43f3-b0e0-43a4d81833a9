"use client"

import { useState, useEffect } from "react"
import { useMobile } from "@/hooks/use-mobile"

type Size = "sm" | "md" | "lg"

/**
 * Custom hook to determine the appropriate size based on viewport width
 * @param defaultSize Default size to use
 * @returns The responsive size
 */
export function useResponsiveSize(defaultSize: Size = "lg"): Size {
  const [size, setSize] = useState<Size>(defaultSize)
  const isMobile = useMobile()

  useEffect(() => {
    // Default to the provided size if window is not available (SSR)
    if (typeof window === "undefined") return

    const handleResize = () => {
      if (window.innerWidth < 480) { // Reduced threshold for small size
        setSize("sm")
      } else if (window.innerWidth < 768) { // Reduced threshold for medium size
        setSize("md")
      } else {
        setSize("lg") // Default to large for most screen sizes
      }
    }

    window.addEventListener("resize", handleResize)
    handleResize() // Initial call

    return () => window.removeEventListener("resize", handleResize)
  }, [])

  // Override with sm on mobile devices
  return isMobile ? "sm" : size
}
