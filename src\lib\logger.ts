import * as fs from "fs"
import path from "path"

/**
 * Advanced Logging Utility
 *
 * Features:
 * - Multiple output destinations: console, file, URL (API), and JSX stringified output
 * - Configurable log levels (debug, info, warn, error)
 * - Component/event/render tracing for debugging
 * - Timestamp and metadata support
 * - Customizable formatting
 */

type LogLevel = {
  value: number
  label: string
}

type LogMetadata = {
  timestamp?: Date
  source?: string
  component?: string
  event?: string
  render?: string
  [key: string]: any
}

type LoggerConfig = {
  minLevel: LogLevel
  outputs: Array<"console" | "file" | "api" | "jsx">
  filePath?: string
  apiUrl?: string | null
  includeTimestamp: boolean
  format: (level: LogLevel, message: string, metadata: LogMetadata) => string
  jsxFormat: (level: LogLevel, message: string, metadata: LogMetadata) => any
  context: Record<string, any>
  batchSize: number
  flushInterval: number
}

type LogEntry = {
  level: string
  message: string
  [key: string]: any
}

/**
 * Type definition matching the C# ClientErrorLog model
 * Expected by the ASP.NET Core API controller
 */
type ClientErrorLog = {
  Message: string
  Url: string
  Stack: string | null
  Level: string
  UserAgent: string
  AdditionalInfo: Record<string, any>
}

class Logger {
  // Log levels with numeric values for comparison
  static LOG_LEVELS = {
    DEBUG: { value: 0, label: "DEBUG" } as LogLevel,
    INFO: { value: 1, label: "INFO" } as LogLevel,
    LOG: { value: 2, label: "LOG" } as LogLevel,
    WARN: { value: 3, label: "WARN" } as LogLevel,
    ERROR: { value: 4, label: "ERROR" } as LogLevel,
  }

  /**
   * Map TypeScript log levels to C# API expected format
   */
  private static mapLogLevelForApi(level: LogLevel): string {
    switch (level) {
      case Logger.LOG_LEVELS.DEBUG:
        return "debug"
      case Logger.LOG_LEVELS.INFO:
      case Logger.LOG_LEVELS.LOG:
        return "info"
      case Logger.LOG_LEVELS.WARN:
        return "warning"
      case Logger.LOG_LEVELS.ERROR:
        return "error"
      default:
        return "info"
    }
  }

  /**
   * Collect browser/client information for API logging
   */
  private static getBrowserInfo(): { url: string; userAgent: string } {
    if (typeof window !== "undefined") {
      return {
        url: window.location.href,
        userAgent: navigator.userAgent,
      }
    }
    return {
      url: "N/A (Server-side)",
      userAgent: "N/A (Server-side)",
    }
  }

  /**
   * Extract stack trace from Error object or metadata
   */
  private static extractStackTrace(metadata: LogMetadata): string | null {
    // Check if there's an error object with stack trace
    if (
      metadata.error &&
      metadata.error instanceof Error &&
      metadata.error.stack
    ) {
      return metadata.error.stack
    }

    // Check if stack trace is provided directly
    if (metadata.stack && typeof metadata.stack === "string") {
      return metadata.stack
    }

    // Try to generate a stack trace
    try {
      throw new Error()
    } catch (e) {
      if (e instanceof Error && e.stack) {
        // Remove the first few lines that are from this logger
        const lines = e.stack.split("\n")
        return lines.slice(3).join("\n")
      }
    }

    return null
  }

  private config: LoggerConfig
  private logQueue: LogEntry[]
  private flushInterval?: NodeJS.Timeout

  constructor(options: Partial<LoggerConfig> = {}) {
    // Default configuration
    this.config = {
      minLevel: options.minLevel || Logger.LOG_LEVELS.DEBUG,
      outputs: options.outputs || ["console"],
      filePath: options.filePath || "./logs/app.log.txt",
      apiUrl: options.apiUrl || null,
      includeTimestamp: options.includeTimestamp !== false,
      format: options.format || this.defaultFormat,
      jsxFormat: options.jsxFormat || this.defaultJsxFormat,
      context: options.context || {},
      batchSize: options.batchSize || 10, // For API logging batching
      flushInterval: options.flushInterval || 5000, // 5 seconds
    }

    // Queue for batched API logs
    this.logQueue = []

    // Set up flush interval for API logs if API output is enabled
    if (this.config.outputs.includes("api") && this.config.apiUrl) {
      this.flushInterval = setInterval(
        () => this.flushLogs(),
        this.config.flushInterval
      )
    }
  }

  // Default formatter for regular logs
  defaultFormat(
    level: LogLevel,
    message: string,
    metadata: LogMetadata
  ): string {
    const timestamp = metadata.timestamp
      ? `[${metadata.timestamp.toISOString()}] `
      : ""
    const source = metadata.source ? `[${metadata.source}] ` : ""
    const component = metadata.component
      ? `[Component: ${metadata.component}] `
      : ""
    const event = metadata.event ? `[Event: ${metadata.event}] ` : ""
    const render = metadata.render ? `[Render: ${metadata.render}] ` : ""

    return `${timestamp}${level.label} ${source}${component}${event}${render}${message}`
  }

  // Default formatter for JSX logs
  defaultJsxFormat(
    level: LogLevel,
    message: string,
    metadata: LogMetadata
  ): any {
    return {
      level: level.label,
      message,
      timestamp: metadata.timestamp ? metadata.timestamp.toISOString() : null,
      ...metadata,
    }
  }

  /**
   * Internal logging method that handles all log processing
   */
  private _log(
    level: LogLevel,
    message: string,
    metadata: LogMetadata = {}
  ): void {
    // Check if this log should be processed based on minimum level
    if (level.value < this.config.minLevel.value) {
      return
    }

    // Merge context with provided metadata
    const mergedMetadata = {
      ...this.config.context,
      ...metadata,
    }

    // Add timestamp if configured
    if (this.config.includeTimestamp) {
      mergedMetadata.timestamp = new Date()
    }

    // Format the log message
    const formattedLog = this.config.format(level, message, mergedMetadata)

    // Process each configured output
    this.config.outputs.forEach((output) => {
      switch (output) {
        case "console":
          this.logToConsole(level, formattedLog)
          break
        case "file":
          this.logToFile(formattedLog)
          break
        case "api":
          this.logToApi(level, message, mergedMetadata)
          break
        case "jsx":
          this.logToJsx(level, message, mergedMetadata)
          break
      }
    })
  }

  /**
   * Log to console with appropriate console method
   */
  private logToConsole(level: LogLevel, formattedLog: string): void {
    switch (level) {
      case Logger.LOG_LEVELS.DEBUG:
        console.debug(formattedLog)
        break
      case Logger.LOG_LEVELS.INFO:
      case Logger.LOG_LEVELS.LOG:
        console.log(formattedLog)
        break
      case Logger.LOG_LEVELS.WARN:
        console.warn(formattedLog)
        break
      case Logger.LOG_LEVELS.ERROR:
        console.error(formattedLog)
        break
    }
  }

  /**
   * Log to file with quota management and recursive logging prevention
   */
  private logToFile(formattedLog: string): void {
    try {
      // In browser environment, we can't directly write to filesystem
      // Use localStorage as a simple simulation with quota management
      if (typeof window !== "undefined") {
        this.writeToLocalStorageWithQuotaManagement(formattedLog)
        return
      }

      // Ensure directory exists
      const dir = path.dirname(this.config.filePath!)
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true })
      }

      // Append to file
      if (this.config.filePath) {
        fs.appendFileSync(this.config.filePath, formattedLog + "\n")
      }
    } catch (error) {
      // Use direct console.error to avoid potential recursive logging
      // Only log file write errors, not the recursive logging issue
      if (error instanceof Error && !error.message.includes("recursive")) {
        console.error("Failed to write log to file:", error)
      }
    }
  }

  /**
   * Write to localStorage with quota management and recursive logging prevention
   */
  private writeToLocalStorageWithQuotaManagement(formattedLog: string): void {
    const LOG_KEY = "app_logs"
    const MAX_LOG_SIZE = 500 * 1024 // 500KB limit for logs
    const CLEANUP_THRESHOLD = 0.8 // Clean up when 80% of limit is reached

    try {
      // Get existing logs
      const existingLogs = localStorage.getItem(LOG_KEY) || ""
      const newLogEntry = formattedLog + "\n"
      const totalSize = existingLogs.length + newLogEntry.length

      // Check if we need to clean up logs
      if (totalSize > MAX_LOG_SIZE * CLEANUP_THRESHOLD) {
        this.cleanupOldLogs(existingLogs, MAX_LOG_SIZE)
      }

      // Try to append the new log
      const currentLogs = localStorage.getItem(LOG_KEY) || ""
      localStorage.setItem(LOG_KEY, currentLogs + newLogEntry)
    } catch (error) {
      if (error instanceof Error && error.name === "QuotaExceededError") {
        // Handle quota exceeded by cleaning up and retrying once
        try {
          this.handleQuotaExceeded()
          // Retry with just the current log entry
          localStorage.setItem(LOG_KEY, formattedLog + "\n")
        } catch (retryError) {
          // If still failing, fall back to console only and disable file logging temporarily
          console.warn(
            "localStorage quota exceeded, disabling file logging temporarily"
          )
          this.temporarilyDisableFileLogging()
        }
      } else {
        // For other errors, use console.error but avoid recursive logging
        console.error("Failed to write to localStorage:", error)
      }
    }
  }

  /**
   * Clean up old logs to make space
   */
  private cleanupOldLogs(existingLogs: string, maxSize: number): void {
    try {
      const lines = existingLogs.split("\n")
      const targetSize = maxSize * 0.5 // Keep only 50% of max size

      // Keep the most recent logs that fit within target size
      let currentSize = 0
      const keptLines: string[] = []

      for (let i = lines.length - 1; i >= 0; i--) {
        const lineSize = lines[i].length + 1 // +1 for newline
        if (currentSize + lineSize <= targetSize) {
          keptLines.unshift(lines[i])
          currentSize += lineSize
        } else {
          break
        }
      }

      localStorage.setItem("app_logs", keptLines.join("\n"))
    } catch (error) {
      // If cleanup fails, clear all logs
      try {
        localStorage.removeItem("app_logs")
      } catch (clearError) {
        console.error("Failed to clear logs during cleanup:", clearError)
      }
    }
  }

  /**
   * Handle quota exceeded error by aggressive cleanup
   */
  private handleQuotaExceeded(): void {
    try {
      // Clear old logs completely
      localStorage.removeItem("app_logs")

      // Also try to clean up other potential large items
      const keysToCheck = [
        "enhanced-settings-storage",
        "enhanced-betting-storage",
      ]
      keysToCheck.forEach((key) => {
        try {
          const item = localStorage.getItem(key)
          if (item && item.length > 10000) {
            // If item is larger than 10KB
            console.warn(
              `Large localStorage item detected: ${key} (${item.length} chars)`
            )
          }
        } catch (error) {
          // Ignore errors when checking items
        }
      })
    } catch (error) {
      console.error("Failed to handle quota exceeded:", error)
    }
  }

  /**
   * Temporarily disable file logging to prevent recursive errors
   */
  private temporarilyDisableFileLogging(): void {
    // Remove "file" from outputs temporarily
    this.config.outputs = this.config.outputs.filter(
      (output) => output !== "file"
    )

    // Re-enable after 30 seconds
    setTimeout(() => {
      if (!this.config.outputs.includes("file")) {
        this.config.outputs.push("file")
        console.info("File logging re-enabled")
      }
    }, 30000)
  }

  /**
   * Log to API endpoint - sends logs in C# ClientErrorLog format
   */
  private logToApi(
    level: LogLevel,
    message: string,
    metadata: LogMetadata
  ): void {
    if (!this.config.apiUrl) {
      return
    }

    // Get browser information
    const browserInfo = Logger.getBrowserInfo()

    // Extract stack trace
    const stack = Logger.extractStackTrace(metadata)

    // Create additional info object (exclude special properties)
    const additionalInfo: Record<string, any> = {}
    Object.keys(metadata).forEach((key) => {
      if (!["error", "stack", "timestamp"].includes(key)) {
        additionalInfo[key] = metadata[key]
      }
    })

    // Create ClientErrorLog object matching C# model
    const clientErrorLog: ClientErrorLog = {
      Message: message,
      Url: browserInfo.url,
      Stack: stack,
      Level: Logger.mapLogLevelForApi(level),
      UserAgent: browserInfo.userAgent,
      AdditionalInfo: additionalInfo,
    }

    // For API logging, send immediately instead of batching
    // This ensures critical errors are sent right away
    this.sendLogToApi(clientErrorLog)
  }

  /**
   * Send individual log to API endpoint
   */
  private async sendLogToApi(clientErrorLog: ClientErrorLog): Promise<void> {
    if (!this.config.apiUrl) {
      return
    }

    try {
      const response = await fetch(this.config.apiUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(clientErrorLog),
      })

      if (!response.ok) {
        // Avoid console.error to prevent potential recursive logging
        // console.error(
        //   `Failed to send log to API: ${response.status} ${response.statusText}`
        // )
      }
    } catch (_error) {
      // Avoid console.error to prevent potential recursive logging
      // console.error("Failed to send log to API:", error)
    }
  }

  /**
   * Flush queued logs to API (legacy method - now unused for API logging)
   * Kept for backward compatibility
   */
  flushLogs(): void {
    // This method is now primarily used for cleanup
    // API logs are sent immediately via sendLogToApi
    if (this.logQueue.length === 0) return

    const logs = [...this.logQueue]
    this.logQueue = []

    // Make API request (legacy format)
    fetch(this.config.apiUrl!, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ logs }),
    }).catch((_error) => {
      // Avoid console.error to prevent potential recursive logging
      // console.error("Failed to send logs to API:", error)
      // Put logs back in queue
      this.logQueue = [...logs, ...this.logQueue]
    })
  }

  /**
   * Format log for JSX output and return it
   */
  private logToJsx(
    level: LogLevel,
    message: string,
    metadata: LogMetadata
  ): string {
    const jsxLog = this.config.jsxFormat(level, message, metadata)

    // Just return the JSX log object as stringified JSON
    // In a real app, you might dispatch this to a store or context
    const stringified = JSON.stringify(jsxLog, null, 2)
    // Avoid console.info to prevent potential recursive logging
    // console.info("JSX Log Output:", stringified)

    return stringified
  }

  /**
   * Convenience methods for different log levels
   */
  debug(message: string, metadata: LogMetadata = {}): void {
    this._log(Logger.LOG_LEVELS.DEBUG, message, metadata)
  }

  info(message: string, metadata: LogMetadata = {}): void {
    this._log(Logger.LOG_LEVELS.INFO, message, metadata)
  }

  log(message: string, metadata: LogMetadata = {}): void {
    this._log(Logger.LOG_LEVELS.LOG, message, metadata)
  }

  warn(message: string, metadata: LogMetadata = {}): void {
    this._log(Logger.LOG_LEVELS.WARN, message, metadata)
  }

  error(message: string, metadata: LogMetadata = {}): void {
    this._log(Logger.LOG_LEVELS.ERROR, message, metadata)
  }

  /**
   * Log JavaScript errors with automatic stack trace extraction
   */
  logError(
    error: Error,
    additionalMessage?: string,
    metadata: LogMetadata = {}
  ): void {
    const message = additionalMessage
      ? `${additionalMessage}: ${error.message}`
      : error.message

    this._log(Logger.LOG_LEVELS.ERROR, message, {
      ...metadata,
      error,
      stack: error.stack,
    })
  }

  /**
   * Create a logger for a specific component
   */
  forComponent(componentName: string) {
    return {
      debug: (message: string, metadata = {}) =>
        this.debug(message, { ...metadata, component: componentName }),
      info: (message: string, metadata = {}) =>
        this.info(message, { ...metadata, component: componentName }),
      log: (message: string, metadata = {}) =>
        this.log(message, { ...metadata, component: componentName }),
      warn: (message: string, metadata = {}) =>
        this.warn(message, { ...metadata, component: componentName }),
      error: (message: string, metadata = {}) =>
        this.error(message, { ...metadata, component: componentName }),
      logError: (error: Error, additionalMessage?: string, metadata = {}) =>
        this.logError(error, additionalMessage, {
          ...metadata,
          component: componentName,
        }),

      // Additional helpers for event and render logging
      logEvent: (eventName: string, message: string, metadata = {}) => {
        this.info(message, {
          ...metadata,
          component: componentName,
          event: eventName,
        })
      },

      logRender: (renderInfo: string, metadata = {}) => {
        this.debug(`Component rendered`, {
          ...metadata,
          component: componentName,
          render: renderInfo,
        })
      },
    }
  }

  /**
   * Create a logger for a specific source (module, service, etc.)
   */
  forSource(sourceName: string) {
    return {
      debug: (message: string, metadata = {}) =>
        this.debug(message, { ...metadata, source: sourceName }),
      info: (message: string, metadata = {}) =>
        this.info(message, { ...metadata, source: sourceName }),
      log: (message: string, metadata = {}) =>
        this.log(message, { ...metadata, source: sourceName }),
      warn: (message: string, metadata = {}) =>
        this.warn(message, { ...metadata, source: sourceName }),
      error: (message: string, metadata = {}) =>
        this.error(message, { ...metadata, source: sourceName }),
      logError: (error: Error, additionalMessage?: string, metadata = {}) =>
        this.logError(error, additionalMessage, {
          ...metadata,
          source: sourceName,
        }),
    }
  }

  /**
   * Clean up resources (e.g., flush logs and clear intervals)
   */
  dispose() {
    if (this.flushInterval) {
      clearInterval(this.flushInterval)
    }

    // Flush any remaining logs
    this.flushLogs()
  }
}

export default Logger

/**
 * Compatibility layer for the middleware logger
 * This provides the same interface as the middleware logger
 * but uses the new Logger class internally
 */

/**
 * Performance-optimized logger that prevents React re-renders
 * by deferring all logging operations to the next tick
 */
class PerformantLogger {
  private loggerInstance: any
  private logQueue: Array<() => void> = []
  private isProcessing = false

  constructor(loggerInstance: any) {
    this.loggerInstance = loggerInstance
  }

  private queueLog(logFn: () => void) {
    this.logQueue.push(logFn)

    if (!this.isProcessing) {
      this.isProcessing = true
      // Use setTimeout to defer logging to next tick, preventing re-renders
      setTimeout(() => {
        this.processQueue()
      }, 0)
    }
  }

  private processQueue() {
    while (this.logQueue.length > 0) {
      const logFn = this.logQueue.shift()
      if (logFn) {
        try {
          logFn()
        } catch (error) {
          // Fallback to console to avoid recursive issues
          console.error("Logger queue processing error:", error)
        }
      }
    }
    this.isProcessing = false
  }

  debug(message: string, metadata = {}) {
    this.queueLog(() => this.loggerInstance.debug(message, metadata))
  }

  info(message: string, metadata = {}) {
    this.queueLog(() => this.loggerInstance.info(message, metadata))
  }

  log(message: string, metadata = {}) {
    this.queueLog(() => this.loggerInstance.log(message, metadata))
  }

  warn(message: string, metadata = {}) {
    this.queueLog(() => this.loggerInstance.warn(message, metadata))
  }

  error(message: string, metadata = {}) {
    this.queueLog(() => this.loggerInstance.error(message, metadata))
  }

  logError(error: Error, additionalMessage?: string, metadata = {}) {
    this.queueLog(() =>
      this.loggerInstance.logError(error, additionalMessage, metadata)
    )
  }
}

/**
 * Utility function to check localStorage usage and health
 */
export const checkLocalStorageHealth = (): {
  totalSize: number
  itemCount: number
  largeItems: Array<{ key: string; size: number }>
  quotaExceeded: boolean
} => {
  let totalSize = 0
  let itemCount = 0
  const largeItems: Array<{ key: string; size: number }> = []
  let quotaExceeded = false

  try {
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key) {
        const value = localStorage.getItem(key) || ""
        const size = value.length
        totalSize += size
        itemCount++

        // Track items larger than 50KB
        if (size > 50000) {
          largeItems.push({ key, size })
        }
      }
    }

    // Test if we can still write to localStorage
    try {
      localStorage.setItem("__test__", "test")
      localStorage.removeItem("__test__")
    } catch (error) {
      if (error instanceof Error && error.name === "QuotaExceededError") {
        quotaExceeded = true
      }
    }
  } catch (error) {
    console.error("Error checking localStorage health:", error)
  }

  return {
    totalSize,
    itemCount,
    largeItems: largeItems.sort((a, b) => b.size - a.size),
    quotaExceeded,
  }
}

// Create a default logger instance with improved configuration
const defaultLogger = new Logger({
  outputs: ["console", "file"], // Include console output as fallback
  filePath: "./logs/app.log.txt",
  includeTimestamp: true,
  // apiUrl: `${import.meta.env.VITE_APP_API_URL}/api/v1/logging/client-error`,
  // batchSize: 10, // For API logging batching (legacy)
  minLevel: Logger.LOG_LEVELS.INFO, // Reduce verbosity to prevent quota issues
})

// Interface to match the middleware logger
interface LogOptions {
  context?: string
  data?: unknown
}

// Create a compatibility layer with performance optimization
export const createLogger = (appName = "Roulette") => {
  // Create a logger with the app name in the context
  const baseLogger = defaultLogger.forSource(appName)

  // Wrap with performant logger to prevent re-renders
  const performantLogger = new PerformantLogger(baseLogger)

  return {
    debug: (message: string, options?: LogOptions) => {
      performantLogger.debug(message, {
        component: options?.context,
        ...(options?.data as object),
      })
    },

    info: (message: string, options?: LogOptions) => {
      performantLogger.info(message, {
        component: options?.context,
        ...(options?.data as object),
      })
    },

    warn: (message: string, options?: LogOptions) => {
      performantLogger.warn(message, {
        component: options?.context,
        ...(options?.data as object),
      })
    },

    error: (message: string, error?: unknown, options?: LogOptions) => {
      performantLogger.error(message, {
        component: options?.context,
        error,
        ...(options?.data as object),
      })
    },

    logError: (
      error: Error,
      additionalMessage?: string,
      options?: LogOptions
    ) => {
      performantLogger.logError(error, additionalMessage, {
        component: options?.context,
        ...(options?.data as object),
      })
    },
  }
}

/**
 * Utility function to clear localStorage safely with user confirmation
 */
export const clearLocalStorageWithConfirmation = (): boolean => {
  try {
    const health = checkLocalStorageHealth()

    if (health.quotaExceeded || health.totalSize > 1000000) {
      // > 1MB
      const message = `localStorage is using ${Math.round(
        health.totalSize / 1024
      )}KB of space. Clear it to fix quota issues?`

      if (confirm(message)) {
        // Keep only essential data
        const essentialKeys = [
          "enhanced-settings-storage",
          "enhanced-betting-storage",
        ]
        const backupData: Record<string, string | null> = {}

        // Backup essential data
        essentialKeys.forEach((key) => {
          backupData[key] = localStorage.getItem(key)
        })

        // Clear everything
        localStorage.clear()

        // Restore essential data
        essentialKeys.forEach((key) => {
          if (backupData[key]) {
            try {
              localStorage.setItem(key, backupData[key]!)
            } catch (error) {
              console.warn(`Could not restore ${key}:`, error)
            }
          }
        })

        console.info("localStorage cleared and essential data restored")
        return true
      }
    }

    return false
  } catch (error) {
    console.error("Error clearing localStorage:", error)
    return false
  }
}

/**
 * Initialize storage health monitoring
 * Call this early in your app to start monitoring localStorage health
 */
export const initializeStorageHealthMonitoring = () => {
  // Check health immediately
  const health = checkLocalStorageHealth()

  if (health.quotaExceeded) {
    console.warn("localStorage quota exceeded detected on startup")

    // Try automatic cleanup of logs
    try {
      localStorage.removeItem("app_logs")
      console.info("Cleared app logs to free up space")
    } catch (error) {
      console.error("Failed to clear logs on startup:", error)
    }
  }

  // Set up periodic health checks
  setInterval(() => {
    const currentHealth = checkLocalStorageHealth()

    if (currentHealth.quotaExceeded) {
      console.warn("localStorage quota exceeded - automatic cleanup triggered")

      // Try to clear logs first
      try {
        localStorage.removeItem("app_logs")
      } catch (error) {
        console.error("Failed to clear logs during health check:", error)
      }
    }
  }, 60000) // Check every minute
}

// Create a default logger instance for compatibility
export const logger = createLogger()
