// Shared types for streaming hooks
export interface RouletteRound {
  rouletteDrawId: number
  rouletteNumber: number
  bonusNumber: number
  videoUrl: string
  nextVideoUrl: string
  startTime: string
  endTime: string
  closeTime: string
  resultingTime: string
  drawStatusId: number
}

export interface QualityConfig {
  auto: -1
  "1080p": 1080
  "720p": 720
  "480p": 480
  "360p": 360
}

export const QUALITY_HEIGHT_MAP: QualityConfig = {
  auto: -1,
  "1080p": 1080,
  "720p": 720,
  "480p": 480,
  "360p": 360,
}

export interface VideoSourceInfo {
  url: string
  startPosition: number
  isSideView: boolean
}

export interface StreamSyncData {
  position: number
  roundId: number
}

export interface RoundChangeData {
  round: RouletteRound
  position: number
  serverTime: number
  roundStartTime: number
  previousNumber: number
}
