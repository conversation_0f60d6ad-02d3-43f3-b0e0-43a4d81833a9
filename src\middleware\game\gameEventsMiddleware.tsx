import {
  createContext,
  useContext,
  useEffect,
  useState,
  ReactNode,
} from "react"
import { logger } from "@/lib/logger"
import { toastService } from "../ui/toastMiddleware"
import { GameEventType, GameEvent } from "./gameEventTypes"

// Re-export the types
export type { GameEventType, GameEvent }

// Game events context
interface GameEventsContextType {
  events: GameEvent[]
  lastEvent: GameEvent | null
  dispatchEvent: (type: GameEventType, message: string, data?: unknown) => void
  clearEvents: () => void
  subscribeToEvent: (
    type: GameEventType,
    callback: (event: GameEvent) => void
  ) => () => void
}

const GameEventsContext = createContext<GameEventsContextType | undefined>(
  undefined
)

// Provider component
interface GameEventsProviderProps {
  children: ReactNode
  maxEvents?: number
  showToasts?: boolean
}

export const GameEventsProvider = ({
  children,
  maxEvents = 100,
  showToasts = true,
}: GameEventsProviderProps) => {
  // State
  const [events, setEvents] = useState<GameEvent[]>([])
  const [lastEvent, setLastEvent] = useState<GameEvent | null>(null)
  const [subscribers, setSubscribers] = useState<
    Map<GameEventType, Set<(event: GameEvent) => void>>
  >(new Map())

  // Generate unique ID
  const generateId = () => {
    return Date.now().toString(36) + Math.random().toString(36).substring(2)
  }

  // Dispatch event
  const dispatchEvent = (
    type: GameEventType,
    message: string,
    data?: unknown
  ) => {
    const event: GameEvent = {
      type,
      message,
      data,
      timestamp: Date.now(),
      id: generateId(),
    }

    // Update state
    setEvents((prevEvents) => {
      const newEvents = [event, ...prevEvents].slice(0, maxEvents)
      return newEvents
    })

    setLastEvent(event)

    // Log event
    logger.debug(`Game event: ${type}`, {
      context: "GameEvents",
      data: {
        type,
        message,
        data,
      },
    })

    // Show toast if enabled
    if (showToasts) {
      switch (type) {
        case "round_start":
          toastService.info("Round Started", message)
          break
        case "round_end":
          toastService.info("Round Ended", message)
          break
        case "betting_open":
          toastService.info("Betting Open", message)
          break
        case "betting_closed":
          toastService.warning("Betting Closed", message)
          break
        case "result":
          toastService.info("Result", message)
          break
        case "win":
          toastService.success("Win", message)
          break
        case "loss":
          toastService.info("Loss", message)
          break
        case "balance_update":
          toastService.info("Balance Updated", message)
          break
        case "error":
          toastService.error("Error", message)
          break
        case "connection":
          toastService.success("Connected", message)
          break
        case "disconnection":
          toastService.error("Disconnected", message)
          break
      }
    }

    // Notify subscribers
    const typeSubscribers = subscribers.get(type)
    if (typeSubscribers) {
      typeSubscribers.forEach((callback) => {
        try {
          callback(event)
        } catch (error) {
          logger.error("Error in game event subscriber", error, {
            context: "GameEvents",
            data: { type, message },
          })
        }
      })
    }

    return event
  }

  // Clear events
  const clearEvents = () => {
    setEvents([])
    setLastEvent(null)
  }

  // Subscribe to event
  const subscribeToEvent = (
    type: GameEventType,
    callback: (event: GameEvent) => void
  ) => {
    setSubscribers((prevSubscribers) => {
      const newSubscribers = new Map(prevSubscribers)

      if (!newSubscribers.has(type)) {
        newSubscribers.set(type, new Set())
      }

      newSubscribers.get(type)!.add(callback)

      return newSubscribers
    })

    // Return unsubscribe function
    return () => {
      setSubscribers((prevSubscribers) => {
        const newSubscribers = new Map(prevSubscribers)

        if (newSubscribers.has(type)) {
          const typeSubscribers = newSubscribers.get(type)!
          typeSubscribers.delete(callback)

          if (typeSubscribers.size === 0) {
            newSubscribers.delete(type)
          }
        }

        return newSubscribers
      })
    }
  }

  return (
    <GameEventsContext.Provider
      value={{
        events,
        lastEvent,
        dispatchEvent,
        clearEvents,
        subscribeToEvent,
      }}
    >
      {children}
    </GameEventsContext.Provider>
  )
}

// Hook to use game events
export const useGameEvents = () => {
  const context = useContext(GameEventsContext)

  if (context === undefined) {
    throw new Error("useGameEvents must be used within a GameEventsProvider")
  }

  return context
}

// Hook to subscribe to specific event type
export const useGameEventSubscription = (
  type: GameEventType,
  callback: (event: GameEvent) => void
) => {
  const { subscribeToEvent } = useGameEvents()

  useEffect(() => {
    return subscribeToEvent(type, callback)
  }, [type, callback, subscribeToEvent])
}
