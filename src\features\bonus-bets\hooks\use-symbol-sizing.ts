import { useEffect, useRef } from "react"

interface UseSymbolSizingProps {
  isMobile: boolean
  showDragon: boolean
  betButtonsLength: number
}

/**
 * Hook to manage responsive symbol sizing based on container dimensions
 */
export const useSymbolSizing = ({ 
  isMobile, 
  showDragon, 
  betButtonsLength 
}: UseSymbolSizingProps) => {
  const containerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const updateSymbolSize = () => {
      if (!containerRef.current) return

      const containerHeight = containerRef.current.clientHeight

      let symbolSize: number

      if (isMobile) {
        if (showDragon) {
          const availableHeight = containerHeight * 0.3

          const calculatedSize = availableHeight / 2.5

          symbolSize = Math.max(Math.min(calculatedSize, 48), 24)
        } else {
          const availableHeight = containerHeight - 50
          const itemCount = betButtonsLength
          const spacing = 8

          const calculatedSize =
            (availableHeight - spacing * (itemCount - 1)) / itemCount

          symbolSize = Math.max(Math.min(calculatedSize, 44), 24)
        }
      } else {
        const bottomSectionHeight = containerHeight * 0.3

        const calculatedSize = bottomSectionHeight / 2.5

        symbolSize = Math.max(Math.min(calculatedSize, 64), 32)
      }

      containerRef.current.style.setProperty("--symbol-size", `${symbolSize}px`)
    }

    updateSymbolSize()

    const resizeObserver = new ResizeObserver(updateSymbolSize)
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current)
    }

    const handleWindowResize = () => {
      requestAnimationFrame(updateSymbolSize)
    }
    window.addEventListener("resize", handleWindowResize)

    return () => {
      resizeObserver.disconnect()
      window.removeEventListener("resize", handleWindowResize)
    }
  }, [isMobile, showDragon, betButtonsLength])

  return { containerRef }
}
