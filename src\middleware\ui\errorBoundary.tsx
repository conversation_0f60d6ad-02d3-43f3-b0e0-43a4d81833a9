import React from "react"
import { ErrorBoundary, FallbackProps } from "react-error-boundary"
import { handleError } from "../errorHandling"
import { errorReporting } from "../errorReporting"
import { <PERSON><PERSON> } from "@/components/ui/button"

interface EnhancedErrorBoundaryProps {
  children: React.ReactNode
  fallback?: React.ComponentType<FallbackProps>
  onError?: (error: Error, info: { componentStack: string }) => void
  context?: string
}

const DefaultFallback = ({ error, resetErrorBoundary }: FallbackProps) => (
  <div className='p-2 bg-global-green border'>
    <h2 className=' font-semibold leading-tight'>Something went wrong</h2>
    <p className='text-sm'>{error.message}</p>
    <Button onClick={resetErrorBoundary}>Try again</Button>
  </div>
)

export const EnhancedErrorBoundary: React.FC<EnhancedErrorBoundaryProps> = ({
  children,
  fallback: Fallback = DefaultFallback,
  onError,
  context = "UI",
}) => {
  const handleComponentError = (
    error: Error,
    info: { componentStack: string }
  ) => {
    // Use the standardized error handler
    handleError(error, {
      context,
      showToast: false, // Don't show a toast for component errors
      logError: true,
    })

    // Report the error to the error reporting service
    errorReporting.reportError(error, context, {
      componentStack: info.componentStack,
    })

    // Call custom error handler if provided
    if (onError) {
      onError(error, info)
    }
  }

  return (
    <ErrorBoundary
      FallbackComponent={Fallback}
      onError={(error, info) =>
        handleComponentError(error, {
          componentStack: info.componentStack || "",
        })
      }
    >
      {children}
    </ErrorBoundary>
  )
}

export default EnhancedErrorBoundary
