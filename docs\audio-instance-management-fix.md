# Audio Instance Management Fix

## Problem Analysis

The audio system was experiencing instance limit warnings because:

1. **Single Audio Element Per Sound**: Each sound key had only one `HTMLAudioElement` instance, but the system tried to track "active sounds" as if there could be multiple instances.

2. **Flawed Active Sound Tracking**: The `activeSounds` Set tracked the same audio element multiple times when the same sound was played repeatedly, but since it's the same object reference, the Set didn't actually grow properly.

3. **Incorrect Cleanup Logic**: The `ended` event listener tried to remove the audio element from `activeSounds`, but this didn't work properly when the same element was reused.

4. **Missing Audio Pooling**: Despite having an `audioPool` property, it was never actually used for creating reusable audio instances.

5. **Background Music vs SFX Handling**: Background music and ambience should loop continuously and not count against the instance limit, but they were treated the same as sound effects.

## Solution Implementation

### 1. Audio Classification System

```typescript
// Sounds that should have multiple instances for rapid-fire usage
const POOLED_SOUNDS = new Set([
  'chip-1',
  'chip-2', 
  'rsa-open',
  'eu-open',
  'zulu-open', 
  'xhosa-open'
])

// Background sounds that loop and don't count against instance limits
const BACKGROUND_SOUNDS = new Set([
  'bg-music',
  'ambience'
])
```

### 2. Three-Tier Audio Management

**Background Sounds**: Single dedicated instances that don't count against limits
- `bg-music` and `ambience` get dedicated instances
- Can loop continuously without affecting other sounds
- Don't count against the 10-instance limit

**Pooled Sounds**: Multiple instances for rapid-fire usage
- Chip sounds and frequently used voice cues get 5 instances each
- Automatically selects available instance or reuses busy ones
- Proper cleanup when each instance finishes

**Regular Sounds**: Single instances with proper cleanup
- Most voice cues and sound effects
- Proper tracking and cleanup after playback

### 3. Improved Instance Selection

```typescript
const getAudioInstance = (): HTMLAudioElement | null => {
  // Check background sounds first
  if (backgroundSounds[key]) {
    return backgroundSounds[key]
  }

  // Check pooled sounds - find available instance
  if (soundPools[key]) {
    const pool = soundPools[key]
    for (const audio of pool) {
      if (audio.paused || audio.ended) {
        return audio
      }
    }
    // If all busy, reuse first one
    return pool[0]
  }

  // Regular sounds
  if (sounds[key]) {
    return sounds[key]
  }

  return null
}
```

### 4. Enhanced Cleanup and Tracking

- Proper event listeners for each audio instance
- Automatic removal from `activeSounds` when playback ends
- Background sounds excluded from instance counting
- Better logging with pool information

## Benefits

1. **Eliminates Instance Limit Warnings**: Background music doesn't count against limits
2. **Supports Rapid-Fire Sounds**: Chip placement sounds can play simultaneously
3. **Better Resource Management**: Proper cleanup and reuse of audio instances
4. **Improved Performance**: Reduced audio element creation/destruction
5. **Enhanced Debugging**: Better logging with pool and instance information

## Testing Recommendations

1. **Rapid Chip Placement**: Click multiple chips quickly to test pooling
2. **Background Music**: Ensure music/ambience doesn't interfere with sound effects
3. **Voice Cue Timing**: Test dealer voice cues during betting phases
4. **Instance Limit**: Monitor console for any remaining limit warnings
5. **Memory Usage**: Check for audio element leaks over extended sessions

## Configuration

The system is configurable via constants:

```typescript
const AUDIO_LIMITS = {
  maxInstances: 10, // Total non-background instances
  poolSize: 5, // Instances per pooled sound
  maxPooledSounds: 15, // Maximum total pooled instances
}
```

Sounds can be easily moved between categories by updating the Sets:
- Add to `POOLED_SOUNDS` for multiple instances
- Add to `BACKGROUND_SOUNDS` to exclude from limits
