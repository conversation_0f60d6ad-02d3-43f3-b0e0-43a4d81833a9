import { useApiQuery } from "@/middleware"

// Constants
const ENDPOINTS = {
  GET_AMOUNT_WON: import.meta.env.VITE_APP_GET_AMOUNT_WON,
}

/**
 * Custom hook to fetch winnings using React Query
 */
export const useWinningsQuery = (betSlipId: number) => {
  return useApiQuery<number>(
    ["winnings", JSON.stringify(betSlipId)],
    `${ENDPOINTS.GET_AMOUNT_WON}?betSlipId=${betSlipId}`,
    null,
    undefined,
    {
      enabled: betSlipId > 0,
      staleTime: 1000 * 60, // 1 minute
      showErrorToast: false,
      errorToastTitle: "Failed to Load Winnings",
    }
  )
}
