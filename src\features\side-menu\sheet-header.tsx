import { SheetClose } from "@/components/ui/sheet"
import { SVGIcon } from "@/hooks"

interface SheetHeaderProps {
  buttonClasses: string
  isMuted: boolean
  setIsMuted: (value: boolean) => void
}

export const SheetHeader = ({
  buttonClasses,
  isMuted,
  setIsMuted,
}: SheetHeaderProps) => {
  return (
    <div className='flex items-center justify-between space-x-4 p-2'>
      <SheetClose className={buttonClasses}>
        <SVGIcon url='/assets/svgs/x.svg' className='h-5 w-5' />
        <span className='sr-only'>Close</span>
      </SheetClose>
      <button className={buttonClasses} onClick={() => setIsMuted(!isMuted)}>
        {isMuted ? (
          <SVGIcon url='/assets/svgs/volume-off.svg' className='h-5 w-5' />
        ) : (
          <SVGIcon url='/assets/svgs/volume-2.svg' className='h-5 w-5' />
        )}
      </button>
    </div>
  )
}
