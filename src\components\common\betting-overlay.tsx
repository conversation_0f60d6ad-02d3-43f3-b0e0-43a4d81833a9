import { motion } from "motion/react"
import { useEffect, useState } from "react"
import { useRoundPhase } from "@/hooks"
import { EnhancedErrorBoundary } from "@/middleware"

/**
 * BettingOverlay component
 * Displays a green gradient animation when betting is active
 */
const BettingOverlayInner = () => {
  const { Betting } = useRoundPhase()
  const [visible, setVisible] = useState(false)

  // Control visibility with a slight delay for smoother transitions
  useEffect(() => {
    if (Betting) {
      const timer = setTimeout(() => setVisible(true), 100)
      return () => clearTimeout(timer)
    } else {
      setVisible(false)
    }
  }, [Betting])

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{
        opacity: visible ? 1 : 0,
      }}
      transition={{
        duration: 1.2,
        ease: "easeInOut",
      }}
      id='green-gradient'
      className='pointer-events-none fixed inset-0 z-20 overflow-hidden'
      style={{ height: "100vh" }}
    >
      {/* Vertical green gradient overlay - more pronounced at bottom */}
      <motion.div
        className='h-full w-full'
        style={{
          background:
            "linear-gradient(to top, #167a15 0%, rgba(12, 174, 18, 1) 75%, rgba(12, 174, 18, 0.3) 90%, transparent 100%)",
        }}
        animate={{
          y: visible ? [3, 0, 3] : 20,
        }}
        transition={{
          y: {
            duration: 6,
            repeat: Infinity,
            repeatType: "reverse",
            ease: "easeInOut",
          },
        }}
      />
    </motion.div>
  )
}

/**
 * BettingOverlay with error boundary
 */
export const BettingOverlay = () => {
  return (
    <EnhancedErrorBoundary context='BettingOverlay'>
      <BettingOverlayInner />
    </EnhancedErrorBoundary>
  )
}

export default BettingOverlay
