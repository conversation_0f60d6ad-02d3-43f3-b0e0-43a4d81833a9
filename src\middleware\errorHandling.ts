import { tryCatch } from "@maxmorozoff/try-catch-tuple"
import { logger } from "@/lib/logger"
import { toastService } from "./ui/toastMiddleware"

/**
 * Standard error interface for the application
 */
export interface AppError {
  message: string
  code?: number | string
  status?: number
  details?: unknown
  context?: string
  stack?: string
}

/**
 * Options for error handling
 */
export interface ErrorHandlingOptions {
  showToast?: boolean
  toastTitle?: string
  logError?: boolean
  context?: string
  rethrow?: boolean
}

/**
 * Default error handling options
 */
const DEFAULT_OPTIONS: ErrorHandlingOptions = {
  showToast: true,
  toastTitle: "Error",
  logError: true,
  context: "Application",
  rethrow: false,
}

/**
 * Format an error into a standardized AppError
 *
 * @param error - Error to format
 * @param context - Error context
 * @returns Formatted AppError
 */
export const formatError = (
  error: unknown,
  context = "Application"
): AppError => {
  // Handle Error objects
  if (error instanceof Error) {
    return {
      message: error.message,
      stack: error.stack,
      context,
    }
  }

  // Handle string errors
  if (typeof error === "string") {
    return {
      message: error,
      context,
    }
  }

  // Handle object errors
  if (error && typeof error === "object") {
    const objError = error as Record<string, unknown>

    return {
      message: (objError.message as string) || "An unknown error occurred",
      code: objError.code as number | string,
      status: objError.status as number,
      details: error,
      context,
    }
  }

  // Default case
  return {
    message: "An unknown error occurred",
    details: error,
    context,
  }
}

/**
 * Handle an error with standardized logging and UI feedback
 *
 * @param error - Error to handle
 * @param options - Error handling options
 * @returns Formatted AppError
 */
export const handleError = (
  error: unknown,
  options: ErrorHandlingOptions = {}
): AppError => {
  const mergedOptions = { ...DEFAULT_OPTIONS, ...options }
  const { showToast, toastTitle, logError, context, rethrow } = mergedOptions

  // Format the error
  const formattedError = formatError(error, context)

  // Log the error
  if (logError) {
    logger.error(`Error in ${context}`, error, {
      context,
      data: formattedError,
    })
  }

  // Show toast notification
  if (showToast) {
    toastService.error(toastTitle || "Error", formattedError.message)
  }

  // Rethrow if requested
  if (rethrow) {
    throw error
  }

  return formattedError
}

/**
 * Wrap a function with error handling
 *
 * @param fn - Function to wrap
 * @param options - Error handling options
 * @returns Wrapped function
 */
export const withErrorHandling = <T extends (...args: any[]) => any>(
  fn: T,
  options: ErrorHandlingOptions = {}
): ((...args: Parameters<T>) => ReturnType<T> | null) => {
  return (...args: Parameters<T>) => {
    try {
      return fn(...args)
    } catch (error) {
      handleError(error, options)
      return null
    }
  }
}

/**
 * Wrap an async function with error handling
 *
 * @param fn - Async function to wrap
 * @param options - Error handling options
 * @returns Wrapped async function
 */
export const withAsyncErrorHandling = <
  T extends (...args: any[]) => Promise<any>
>(
  fn: T,
  options: ErrorHandlingOptions = {}
): ((...args: Parameters<T>) => Promise<Awaited<ReturnType<T>> | null>) => {
  return async (...args: Parameters<T>) => {
    try {
      return await fn(...args)
    } catch (error) {
      handleError(error, options)
      return null
    }
  }
}

/**
 * Execute a function and handle any errors
 *
 * @param fn - Function to execute
 * @param options - Error handling options
 * @returns Result of the function or null if an error occurred
 */
export const tryCatchWithHandling = <T>(
  fn: () => T,
  options: ErrorHandlingOptions = {}
): [T | null, AppError | null] => {
  // Use the tryCatch function but handle the result properly
  const result = tryCatch(fn, options.context || "Application")

  if (result[1]) {
    return [null, handleError(result[1], options)]
  }

  return [result[0], null]
}

/**
 * Execute an async function and handle any errors
 *
 * @param fn - Async function to execute
 * @param options - Error handling options
 * @returns Promise that resolves to the result of the function or null if an error occurred
 */
export const tryCatchAsyncWithHandling = async <T>(
  fn: () => Promise<T>,
  options: ErrorHandlingOptions = {}
): Promise<[T | null, AppError | null]> => {
  // Use the tryCatch function but handle the result properly
  const result = await tryCatch(fn, options.context || "Application")

  if (result[1]) {
    return [null, handleError(result[1], options)]
  }

  return [result[0], null]
}
