# Task 04: Zustand Store Optimization

## Task Overview

**Priority**: Medium | **Risk**: Medium | **Impact**: High  
**Estimated Duration**: 2 weeks | **Dependencies**: Task 02 (Control Panel Simplification)

### Description
Split the monolithic `betting-store.ts` (896 lines) into focused, single-responsibility stores while maintaining type safety and performance. This addresses overlapping concerns, complex interdependencies, and opportunities for derived state optimization.

### Expected Impact
- Reduce store complexity by 70% (896 lines → ~300 lines per focused store)
- Improve performance through better memoization and selective subscriptions
- Enhance maintainability and testability of state management
- Enable better code organization and feature isolation

## Current State Analysis

### Primary Issues in `src/stores/betting-store.ts`

**Lines 147-151**: Simple chip selection state (good extraction candidate)
```typescript
// Chip selection
selectedChip: DEFAULT_CHIP,
setSelectedChip: (chip) =>
  set((state) => {
    state.selectedChip = chip
  }),
```

**Lines 154-200**: Complex bet placement logic with calculations
```typescript
// Placed chips with complex state updates
placedChips: [],
setPlacedChips: (updater) =>
  set((state) => {
    const newPlacedChips = typeof updater === 'function' ? updater(state.placedChips) : updater
    state.placedChips = newPlacedChips
    state.totalBet = calculateTotalBet(newPlacedChips)
  }),

addChip: (cell, chip) =>
  set((state) => {
    // 30+ lines of complex chip placement logic
  }),
```

**Lines 89-95, 800-850**: Self-contained autoplay system
```typescript
// Autoplay configuration and state
autoplay: AutoplayConfig
updateAutoplayConfig: (config: Partial<AutoplayConfig>) => void
startAutoplay: () => void
stopAutoplay: () => void
processAutoplayRound: (roundResult: { win: boolean; amount: number }) => void
```

**Lines 140-145**: UI-specific favorites state
```typescript
// Favorites
favoritesPopoverOpen: true,
setFavoritesPopoverOpen: (open) =>
  set((state) => {
    state.favoritesPopoverOpen = open
  }),
```

**Lines 75-82**: Data management for bet history
```typescript
// Bet history
betHistory: [],
addToBetHistory: (bet) => set((state) => {
  state.betHistory.unshift(bet)
  if (state.betHistory.length > 50) {
    state.betHistory = state.betHistory.slice(0, 50)
  }
}),
```

### Cross-Store Dependencies
- Betting actions depend on game state (phases, round data)
- Autoplay needs access to betting and balance state
- UI state (favorites) mixed with business logic
- Some state could be derived rather than stored

## Target Architecture

### New Store Structure
```
src/stores/
├── betting/
│   ├── chip-selection-store.ts     (~50 lines)
│   ├── bet-placement-store.ts      (~300 lines)
│   ├── autoplay-store.ts           (~200 lines)
│   ├── favorites-store.ts          (~100 lines)
│   └── bet-history-store.ts        (~150 lines)
├── game-state-store.ts             (keep as-is, well-sized)
├── settings-store.ts               (keep as-is, well-sized)
└── derived/
    ├── betting-selectors.ts        (derived state)
    └── store-composition.ts        (store orchestration)
```

### Chip Selection Store (Focused)
```typescript
// chip-selection-store.ts
interface ChipSelectionState {
  selectedChip: Chip | null
  setSelectedChip: (chip: Chip | null) => void
  clearSelection: () => void
}

export const useChipSelectionStore = createEnhancedStore<ChipSelectionState>(
  (set) => ({
    selectedChip: DEFAULT_CHIP,
    
    setSelectedChip: (chip) =>
      set((state) => {
        state.selectedChip = chip
      }),
      
    clearSelection: () =>
      set((state) => {
        state.selectedChip = null
      })
  }),
  {
    name: 'chip-selection-storage',
    persist: true,
    secure: true,
    immer: true
  }
)
```

### Bet Placement Store (Core Logic)
```typescript
// bet-placement-store.ts
interface BetPlacementState {
  placedChips: PlacedChip[]
  balance: number
  betslipId: number | null
  winnings: number
  
  // Actions
  addChip: (cell: SelectableCell, chip: Chip) => void
  removeChip: (chipId: string) => void
  clearAllChips: () => void
  setBalance: (balance: number) => void
  setWinnings: (winnings: number) => void
  
  // Computed actions
  repeatLastBet: () => void
  doubleCurrentBet: () => void
  undoLastChip: () => void
}

export const useBetPlacementStore = createEnhancedStore<BetPlacementState>(
  (set, get) => ({
    placedChips: [],
    balance: 0,
    betslipId: null,
    winnings: 0,
    
    addChip: (cell, chip) =>
      set((state) => {
        // Focused chip placement logic
        const existingChipIndex = state.placedChips.findIndex(
          (placedChip) => placedChip.cell.id === cell.id
        )
        
        if (existingChipIndex !== -1) {
          state.placedChips[existingChipIndex].chips.push(chip)
        } else {
          state.placedChips.push({
            id: generateChipId(),
            cell,
            chips: [chip],
            timestamp: Date.now()
          })
        }
      }),
      
    // Other focused actions...
  }),
  {
    name: 'bet-placement-storage',
    persist: true,
    secure: true,
    immer: true,
    partialize: (state) => ({
      balance: state.balance,
      betslipId: state.betslipId,
      placedChips: state.placedChips
    })
  }
)
```

### Derived State Selectors
```typescript
// betting-selectors.ts
export const useTotalBetAmount = createSelector(
  useBetPlacementStore,
  (state) => calculateTotalBet(state.placedChips)
)

export const useCanPlaceBet = createSelector(
  [useBetPlacementStore, useChipSelectionStore, useGameStateStore],
  (betState, chipState, gameState) => 
    gameState.roundPhase === 'betting' && 
    chipState.selectedChip !== null &&
    betState.balance >= chipState.selectedChip.value
)

export const useBettingActions = () => {
  const addChip = useBetPlacementStore(state => state.addChip)
  const selectedChip = useChipSelectionStore(state => state.selectedChip)
  const canPlaceBet = useCanPlaceBet()
  
  return useMemo(() => ({
    placeBet: (cell: SelectableCell) => {
      if (canPlaceBet && selectedChip) {
        addChip(cell, selectedChip)
      }
    },
    // Other composed actions...
  }), [addChip, selectedChip, canPlaceBet])
}
```

## Step-by-Step Implementation Plan

### Phase 1: Store Extraction (Days 1-5)

#### Day 1: Create Chip Selection Store
- [ ] Create `chip-selection-store.ts` with focused interface
- [ ] Extract chip selection logic from betting store
- [ ] Add proper TypeScript types and validation
- [ ] Create unit tests for chip selection

#### Day 2: Create Favorites Store
- [ ] Create `favorites-store.ts` for UI state
- [ ] Extract favorites popover state
- [ ] Add favorites management actions
- [ ] Create favorites persistence logic

#### Day 3: Create Bet History Store
- [ ] Create `bet-history-store.ts` for data management
- [ ] Extract bet history logic and actions
- [ ] Implement history size management
- [ ] Add history querying utilities

#### Day 4: Create Autoplay Store
- [ ] Create `autoplay-store.ts` with self-contained logic
- [ ] Extract autoplay configuration and state
- [ ] Implement autoplay round processing
- [ ] Add autoplay validation and safety checks

#### Day 5: Create Core Bet Placement Store
- [ ] Create `bet-placement-store.ts` with core logic
- [ ] Extract chip placement and removal logic
- [ ] Implement balance and winnings management
- [ ] Add bet calculation utilities

### Phase 2: Derived State and Selectors (Days 6-8)

#### Day 6: Create Derived State Selectors
- [ ] Create `betting-selectors.ts` with computed state
- [ ] Implement total bet calculation selector
- [ ] Add betting validation selectors
- [ ] Create performance-optimized selectors

#### Day 7: Create Store Composition
- [ ] Create `store-composition.ts` for orchestration
- [ ] Implement composed actions and selectors
- [ ] Add cross-store communication patterns
- [ ] Create store synchronization utilities

#### Day 8: Create Betting Actions Hook
- [ ] Create `useBettingActions` composition hook
- [ ] Implement high-level betting operations
- [ ] Add action validation and error handling
- [ ] Create action testing utilities

### Phase 3: Migration and Integration (Days 9-12)

#### Day 9: Update Component Dependencies
- [ ] Update components to use new stores
- [ ] Replace betting store imports
- [ ] Update hook dependencies
- [ ] Verify functionality preservation

#### Day 10: Update Control Panel Integration
- [ ] Integrate with new control panel system
- [ ] Update button state selectors
- [ ] Verify control panel functionality
- [ ] Test cross-store interactions

#### Day 11: Performance Optimization
- [ ] Add proper memoization to selectors
- [ ] Optimize store subscriptions
- [ ] Implement selective re-rendering
- [ ] Add performance monitoring

#### Day 12: Integration Testing
- [ ] Test store interactions
- [ ] Verify state synchronization
- [ ] Test persistence and rehydration
- [ ] Validate performance improvements

### Phase 4: Testing and Documentation (Days 13-14)

#### Day 13: Comprehensive Testing
- [ ] Unit tests for all new stores
- [ ] Integration tests for store composition
- [ ] Performance benchmarking
- [ ] State management testing

#### Day 14: Documentation and Cleanup
- [ ] Create store documentation
- [ ] Add migration guides
- [ ] Clean up old betting store
- [ ] Final optimization and review

## Code Examples

### Before: Monolithic Betting Store
```typescript
// betting-store.ts (896 lines)
export const useBettingStore = createEnhancedStore<BettingStore>(
  (set, get) => ({
    // Chip selection (5 lines)
    selectedChip: DEFAULT_CHIP,
    setSelectedChip: (chip) => set((state) => { state.selectedChip = chip }),
    
    // Bet placement (100+ lines)
    placedChips: [],
    addChip: (cell, chip) => set((state) => {
      // Complex 50+ line implementation
    }),
    
    // Autoplay (200+ lines)
    autoplay: { /* complex config */ },
    startAutoplay: () => { /* complex logic */ },
    
    // Favorites (10 lines)
    favoritesPopoverOpen: true,
    setFavoritesPopoverOpen: (open) => set((state) => { state.favoritesPopoverOpen = open }),
    
    // Bet history (50+ lines)
    betHistory: [],
    addToBetHistory: (bet) => { /* history management */ },
    
    // 500+ more lines of mixed concerns...
  })
)
```

### After: Focused Stores with Composition
```typescript
// Focused stores
export const useChipSelectionStore = createEnhancedStore<ChipSelectionState>(...)
export const useBetPlacementStore = createEnhancedStore<BetPlacementState>(...)
export const useAutoplayStore = createEnhancedStore<AutoplayState>(...)
export const useFavoritesStore = createEnhancedStore<FavoritesState>(...)
export const useBetHistoryStore = createEnhancedStore<BetHistoryState>(...)

// Composed interface (maintains backward compatibility)
export const useBettingActions = () => {
  const chipActions = useChipSelectionStore()
  const betActions = useBetPlacementStore()
  const autoplayActions = useAutoplayStore()
  
  return useMemo(() => ({
    // Composed actions that work across stores
    placeBet: (cell: SelectableCell) => {
      if (chipActions.selectedChip) {
        betActions.addChip(cell, chipActions.selectedChip)
      }
    },
    
    startAutoplayWithBets: () => {
      if (betActions.placedChips.length > 0) {
        autoplayActions.startAutoplay()
      }
    }
    
    // Other composed actions...
  }), [chipActions, betActions, autoplayActions])
}
```

### Derived State Example
```typescript
// betting-selectors.ts
export const useTotalBetAmount = createSelector(
  useBetPlacementStore,
  (state) => calculateTotalBet(state.placedChips)
)

export const useBettingStatus = createSelector(
  [useBetPlacementStore, useChipSelectionStore, useGameStateStore],
  (betState, chipState, gameState) => ({
    canPlaceBet: gameState.roundPhase === 'betting' && 
                 chipState.selectedChip !== null &&
                 betState.balance >= chipState.selectedChip.value,
    totalBet: calculateTotalBet(betState.placedChips),
    hasActiveBets: betState.placedChips.length > 0,
    remainingBalance: betState.balance - calculateTotalBet(betState.placedChips)
  })
)
```

## Testing Strategy

### Unit Tests
```typescript
// chip-selection-store.test.ts
describe('ChipSelectionStore', () => {
  it('sets selected chip correctly', () => {
    const { result } = renderHook(() => useChipSelectionStore())
    
    act(() => {
      result.current.setSelectedChip(mockChip)
    })
    
    expect(result.current.selectedChip).toEqual(mockChip)
  })
  
  it('clears selection correctly', () => {
    const { result } = renderHook(() => useChipSelectionStore())
    
    act(() => {
      result.current.setSelectedChip(mockChip)
      result.current.clearSelection()
    })
    
    expect(result.current.selectedChip).toBeNull()
  })
})

// betting-selectors.test.ts
describe('BettingSelectors', () => {
  it('calculates total bet amount correctly', () => {
    const mockPlacedChips = [
      { chips: [{ value: 5 }, { value: 10 }] },
      { chips: [{ value: 20 }] }
    ]
    
    const { result } = renderHook(() => useTotalBetAmount(), {
      wrapper: ({ children }) => (
        <StoreProvider initialState={{ placedChips: mockPlacedChips }}>
          {children}
        </StoreProvider>
      )
    })
    
    expect(result.current).toBe(35)
  })
})
```

### Integration Tests
```typescript
// store-composition.test.ts
describe('Store Composition', () => {
  it('coordinates actions across stores', () => {
    const { result } = renderHook(() => useBettingActions())
    
    act(() => {
      result.current.placeBet(mockCell)
    })
    
    // Verify chip was added to bet placement store
    expect(useBetPlacementStore.getState().placedChips).toHaveLength(1)
    
    // Verify chip selection remains active
    expect(useChipSelectionStore.getState().selectedChip).toBeTruthy()
  })
})
```

### Performance Tests
```typescript
describe('Store Performance', () => {
  it('optimizes re-renders with selectors', () => {
    const renderCount = jest.fn()
    
    const TestComponent = () => {
      const totalBet = useTotalBetAmount()
      renderCount()
      return <div>{totalBet}</div>
    }
    
    render(<TestComponent />)
    
    // Change unrelated state
    act(() => {
      useChipSelectionStore.getState().setSelectedChip(mockChip)
    })
    
    // Should not cause re-render
    expect(renderCount).toHaveBeenCalledTimes(1)
  })
})
```

## Risk Mitigation

### Potential Issues
1. **State Synchronization**: Cross-store dependencies
2. **Performance Regression**: Over-subscription to stores
3. **Migration Complexity**: Large codebase changes
4. **Type Safety**: Maintaining TypeScript safety across stores

### Mitigation Strategies
1. **Gradual Migration**: Migrate one store at a time
2. **Backward Compatibility**: Maintain existing interfaces during transition
3. **Comprehensive Testing**: Focus on integration testing
4. **Performance Monitoring**: Benchmark before/after changes
5. **Feature Flags**: Enable/disable new stores

### Rollback Procedure
```typescript
// Feature flag for gradual migration
const USE_SPLIT_STORES = import.meta.env.VITE_USE_SPLIT_STORES === 'true'

export const useBettingActions = () => {
  return USE_SPLIT_STORES 
    ? useNewBettingActions() 
    : useLegacyBettingStore()
}
```

## Success Criteria

### Quantitative Metrics
- [ ] Betting store reduced from 896 to <300 lines per focused store
- [ ] 5 focused stores created with single responsibilities
- [ ] Performance improved by 25% (fewer unnecessary re-renders)
- [ ] Test coverage >85% for all new stores
- [ ] Memory usage optimized (reduce state duplication)

### Qualitative Metrics
- [ ] Better separation of concerns
- [ ] Improved maintainability
- [ ] Enhanced developer experience
- [ ] Cleaner state management patterns
- [ ] Better testability

### Acceptance Criteria
- [ ] All betting functionality preserved
- [ ] State persistence works correctly
- [ ] Cross-store communication functions properly
- [ ] Performance benchmarks met or exceeded
- [ ] Type safety maintained
- [ ] Migration completed without regressions
- [ ] Code review approval
- [ ] QA testing passed
