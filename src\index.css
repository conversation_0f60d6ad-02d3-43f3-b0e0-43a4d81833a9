@import "tailwindcss";

@plugin 'tailwindcss-animate';

@custom-variant dark (&:is(.dark *));

@theme {
  --z-index--1: -1;

  --shadow-3xl: 0 35px 60px -15px rgba(0, 0, 0);
  --text-xxs: 0.6rem;

  --grid-column-span-13: span 13 / span 13;
  --grid-column-span-14: span 14 / span 14;
  --grid-column-span-15: span 15 / span 15;
  --grid-column-span-16: span 16 / span 16;

  --radius-inherit: inherit;
  --radius-lg: var(--radius);
  --radius-md: calc(var(--radius) - 2px);
  --radius-sm: calc(var(--radius) - 4px);

  --color-rlt-green: hsl(var(--rlt-green));
  --color-rlt-red: hsl(var(--rlt-red));
  --color-rlt-black: hsl(var(--rlt-black));
  --color-global-green: hsl(var(--global-green));

  --color-background: hsl(var(--background));
  --color-foreground: hsl(var(--foreground));
  --color-smoke: hsl(36, 47%, 58%);

  --color-card: hsl(var(--card));
  --color-card-foreground: hsl(var(--card-foreground));

  --color-popover: hsl(var(--popover));
  --color-popover-foreground: hsl(var(--popover-foreground));

  --color-primary: hsl(var(--primary));
  --color-primary-foreground: hsl(var(--primary-foreground));

  --color-secondary: hsl(var(--secondary));
  --color-secondary-foreground: hsl(var(--secondary-foreground));

  --color-muted: hsl(var(--muted));
  --color-muted-foreground: hsl(var(--muted-foreground));

  --color-accent: hsl(var(--accent));
  --color-accent-foreground: hsl(var(--accent-foreground));

  --color-destructive: hsl(var(--destructive));
  --color-destructive-foreground: hsl(var(--destructive-foreground));

  --color-border: hsl(var(--border));
  --color-input: hsl(var(--input));
  --color-ring: hsl(var(--ring));

  --color-chart-1: hsl(var(--chart-1));
  --color-chart-2: hsl(var(--chart-2));
  --color-chart-3: hsl(var(--chart-3));
  --color-chart-4: hsl(var(--chart-4));
  --color-chart-5: hsl(var(--chart-5));

  --spacing-vw-1: 1svw;
  --spacing-vw-5: 5svw;
  --spacing-vw-10: 10svw;
  --spacing-vh-1: 1svh;
  --spacing-vh-5: 5svh;
  --spacing-vh-10: 10svh;

  --container-screen-safe: min(100%, 100svw);
  --container-container-safe: min(100%, 90svw);

  --width-screen-safe: min(100%, 100svw);
  --width-container-safe: min(100%, 90svw);

  --height-screen-safe: min(100%, 100svh);
  --height-container-safe: min(100%, 90svh);
}

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentColor);
  }
}

/* Radial gradient utility */
.bg-radial-gradient {
  background: radial-gradient(
    circle,
    var(--tw-gradient-from) 0%,
    var(--tw-gradient-to) 70%
  );
}

@keyframes seamlessVerticalScroll {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-50%);
  }
}

@utility scale-display {
  transform: scale(calc(100vw / 1920));
  transform-origin: top left;

  /* For high DPI displays */
  @media screen and (min-resolution: 120dpi) {
    transform: scale(0.8);
  }
}

/* Text stroke utility */
@utility text-stroke {
  -webkit-text-stroke: 1px;
  text-stroke: 1px;
  paint-order: stroke fill;
}

/* Text stroke color variants */
.text-stroke-white {
  -webkit-text-stroke-color: white;
  text-stroke-color: white;
}

.text-stroke-black {
  -webkit-text-stroke-color: black;
  text-stroke-color: black;
}

.text-stroke-amber-200 {
  -webkit-text-stroke-color: hsl(43, 100%, 80%);
  text-stroke-color: hsl(43, 100%, 80%);
}

.text-stroke-amber-500 {
  -webkit-text-stroke-color: hsl(43, 100%, 50%);
  text-stroke-color: hsl(43, 100%, 50%);
}

/* Text stroke width variants */
.text-stroke-1 {
  -webkit-text-stroke-width: 1px;
  text-stroke-width: 1px;
}

.text-stroke-2 {
  -webkit-text-stroke-width: 2px;
  text-stroke-width: 2px;
}

.stats-tag-gradient {
  background-image: linear-gradient(
    to bottom,
    hsla(0, 0%, 20%) 0%,
    hsla(0, 0%, 10%) 40%,
    hsla(0, 0%, 2%) 100%
  );
}

:root {
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

*,
*::before,
*::after {
  box-sizing: border-box;
}

img,
picture,
svg,
video {
  display: block;
  max-width: 100%;
}

textarea {
  resize: none;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  text-wrap: balance;
}

p {
  max-width: 80ch;
  text-wrap: pretty;
}

body {
  font-family: Inter, system-ui, -apple-system, Avenir, Helvetica, Arial,
    sans-serif !important;
  font-size: 100%;

  background-color: hsl(119, 71%, 28%);
  color: hsl(240, 5%, 96%);
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, "Courier New",
    monospace !important;
}

html {
  hanging-punctuation: first last;
}

input[type="file"]::file-selector-button {
  display: hidden;
}

.invis-scroll {
  overflow-y: scroll;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.invis-scroll::-webkit-scrollbar {
  width: 0;
  height: 0;
}

/* Scroll fade utilities */
.scroll-fade-container {
  position: relative;
  overflow: hidden;
}

/* Vertical scroll fade */
.scroll-fade-y {
  --fade-size: 24px;
  --fade-color: rgba(0, 0, 0, 0.8);
  mask-image: linear-gradient(
    to bottom,
    var(--fade-color) 0,
    var(--fade-color) 5px,
    var(--fade-color) calc(100% - var(--fade-size)),
    transparent 100%
  );
  -webkit-mask-image: linear-gradient(
    to bottom,
    var(--fade-color) 0,
    var(--fade-color) 5px,
    var(--fade-color) calc(100% - var(--fade-size)),
    transparent 100%
  );
}

/* Horizontal scroll fade */
.scroll-fade-x {
  --fade-size: 24px;
  --fade-color: rgba(0, 0, 0, 0.8);
  mask-image: linear-gradient(
    to right,
    var(--fade-color) 0,
    var(--fade-color) 5px,
    var(--fade-color) calc(100% - var(--fade-size)),
    transparent 100%
  );
  -webkit-mask-image: linear-gradient(
    to right,
    var(--fade-color) 0,
    var(--fade-color) 5px,
    var(--fade-color) calc(100% - var(--fade-size)),
    transparent 100%
  );
}

/* Fade on both sides horizontally */
.scroll-fade-x-both {
  --fade-size: 24px;
  --fade-color: rgba(0, 0, 0, 0.8);
  mask-image: linear-gradient(
    to right,
    transparent 0,
    var(--fade-color) var(--fade-size),
    var(--fade-color) calc(100% - var(--fade-size)),
    transparent 100%
  );
  -webkit-mask-image: linear-gradient(
    to right,
    transparent 0,
    var(--fade-color) var(--fade-size),
    var(--fade-color) calc(100% - var(--fade-size)),
    transparent 100%
  );
}

/* Fade on both sides vertically */
.scroll-fade-y-both {
  --fade-size: 24px;
  --fade-color: rgba(0, 0, 0, 0.8);
  mask-image: linear-gradient(
    to bottom,
    transparent 0,
    var(--fade-color) var(--fade-size),
    var(--fade-color) calc(100% - var(--fade-size)),
    transparent 100%
  );
  -webkit-mask-image: linear-gradient(
    to bottom,
    transparent 0,
    var(--fade-color) var(--fade-size),
    var(--fade-color) calc(100% - var(--fade-size)),
    transparent 100%
  );
}

/* Fade size variants */
.scroll-fade-sm {
  --fade-size: 16px;
}

.scroll-fade-md {
  --fade-size: 24px;
}

.scroll-fade-lg {
  --fade-size: 32px;
}

* {
  user-select: none;
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus {
  transition: background-color 5000s ease-in-out 0s;
  -webkit-text-fill-color: currentColor !important;
}

@media (prefers-reduced-motion: no-preference) {
  :has(:target) {
    scroll-behavior: smooth;
    scroll-padding-top: 3rem;
  }
}

@layer base {
  :root {
    --rlt-green: 118 100% 11%;
    --rlt-red: 0 95% 24%;
    --rlt-black: 0 0% 15%;
    --global-green: 119 71% 28%;
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  .dark {
    --rlt-green: 118 100% 11%;
    --rlt-red: 0 95% 24%;
    --rlt-black: 0 0% 15%;
    --global-green: 119 71% 28%;
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
