# =============================================================================
# API CONFIGURATION
# =============================================================================

# Main API URL - Change to your local API server for development
VITE_APP_API_URL=https://grrouletteapi.easygamesdemo.co.za/

# Side/Media Base URL for assets and streaming
VITE_APP_SIDE_BASE_URL=https://videos.easygamesdemo.co.za/

# =============================================================================
# API ENDPOINTS
# =============================================================================

VITE_APP_BALANCE=api/Transaction/GetBalance
VITE_APP_SUBMIT_BET=api/Bet/submit-bet
VITE_APP_UPCOMING_ROUNDS=api/Round/GetUpcomingRounds
VITE_APP_TOP_BETS=api/Round/GetTopBetNumbersAndBonuses
VITE_APP_HOT_COLD=api/Round/GetHotOrColdNumbers
VITE_APP_GET_BET_TYPES=api/Bet/GetBetGroups
VITE_APP_GET_CURRENCY_SYMBOL=api/Transaction/GetCurrencySymbol
VITE_APP_GET_PLAYER_HISTORY=api/Bet/GetPlayerBetHistory
VITE_APP_GET_GRAPH_DATA=api/Round/GetDistributionWheelData
VITE_APP_GET_AMOUNT_WON=api/Transaction/GetAmountWon
VITE_APP_GET_PLAYER_HISTORY_NAV=api/Bet/GetPlayerBetNavHistory

# =============================================================================
# ENVIRONMENT CONFIGURATION
# =============================================================================

# Environment type: development | staging | production
VITE_APP_ENV=production

# Build type: online | retail | video
VITE_APP_BUILD=online

# =============================================================================
# DEVELOPMENT FEATURES
# =============================================================================

# Enable debug mode (shows additional logging and dev tools)
VITE_APP_DEBUG=false

# Enable mock API responses (for offline development)
VITE_APP_MOCK_API=false

# Enable React DevTools and other development tools
VITE_APP_ENABLE_DEVTOOLS=false

# Enable hot reload for configuration files
VITE_APP_HOT_RELOAD=true

# =============================================================================
# PERFORMANCE & ANALYTICS
# =============================================================================

# Enable analytics tracking
VITE_APP_ENABLE_ANALYTICS=true

# Logging level: debug | info | warn | error
VITE_APP_LOG_LEVEL=error

# Enable source maps in build
VITE_APP_ENABLE_SOURCE_MAPS=false

# =============================================================================
# BROWSER-SPECIFIC SETTINGS
# =============================================================================

# Default browser for development (chrome | firefox | edge | zen)
BROWSER=chrome

# =============================================================================
# ADDITIONAL CONFIGURATION
# =============================================================================

# Add any additional environment variables here
# VITE_APP_CUSTOM_FEATURE=true
# VITE_APP_API_TIMEOUT=5000
# VITE_APP_MAX_RETRIES=3

