// Configuration middleware has been removed as it was not being used

/**
 * Game Events Middleware
 *
 * Provides a centralized event system for game-related events.
 * Includes providers and hooks for publishing and subscribing to events.
 */
export {
  GameEventsProvider,
  useGameEvents,
  useGameEventSubscription,
} from "./game/gameEventsMiddleware"

/**
 * Game Event Types
 *
 * Type definitions for game events.
 */
export type { GameEventType, GameEvent } from "./game/gameEventTypes"

/**
 * Chip Middleware
 *
 * Provides utilities for chip management and display.
 * Includes functions for finding chips.
 */
export { useChips, findLiteralChip } from "./game/chipMiddleware"

/**
 * Chip Types
 *
 * Type definitions for chips.
 */
export type { Chip } from "./game/chipMiddleware"

/**
 * API Middleware
 *
 * Provides centralized API client with error handling, retries, and authentication.
 * Includes React Query integration for data fetching with caching and refetching.
 */
export { apiClient, createApiClient } from "./api/apiClient"
export { authMiddleware, createAuthMiddleware } from "./api/authMiddleware"
export { QueryProvider, queryClient } from "./api/queryProvider"
export { useApiQuery, useApiMutation } from "./api/useApiQuery"

/**
 * Logging Middleware
 *
 * Provides centralized logging and error reporting.
 * Includes structured logging with different levels and contexts.
 */
export { logger, createLogger } from "@/lib/logger"
export { errorReporting, createErrorReporting } from "./errorReporting"

/**
 * Error Handling Middleware
 *
 * Provides standardized error handling utilities.
 * Includes error formatting, handling, and try-catch wrappers.
 */
export {
  formatError,
  handleError,
  withErrorHandling,
  withAsyncErrorHandling,
  tryCatchWithHandling,
  tryCatchAsyncWithHandling,
} from "./errorHandling"
export type { AppError, ErrorHandlingOptions } from "./errorHandling"

/**
 * Storage Middleware
 *
 * Provides secure storage and enhanced state management.
 * Includes Zustand integration with Immer for immutable updates.
 */
export {
  secureStorage,
  createSecureStorage,
} from "./storage/persistenceMiddleware"
export { createEnhancedStore, createSelector } from "./storage/stateMiddleware"

/**
 * UI Middleware
 *
 * Provides reusable UI components and utilities.
 * Includes toast notifications, error boundaries, form validation, and more.
 */
export { toastService, createToastService } from "./ui/toastMiddleware"
export { default as EnhancedErrorBoundary } from "./ui/errorBoundary"
// Form middleware has been removed as it was not being used
export { VirtualizedList } from "./ui/virtualListMiddleware"
// Note: dateUtils and DATE_FORMATS have been moved to src/lib/formatting.ts
export {
  ResponsiveLayoutProvider,
  useResponsiveLayout,
  Responsive,
  Device,
  Orientation,
  BREAKPOINTS,
} from "./ui/responsiveLayoutMiddleware"
