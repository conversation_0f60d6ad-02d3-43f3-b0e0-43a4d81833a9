import {
  QueryClient,
  QueryClientProvider,
  MutationCache,
  QueryCache,
} from "@tanstack/react-query"
import React from "react"
import { logger } from "@/lib/logger"

// Create query and mutation caches with error handlers
const queryCache = new QueryCache({
  onError: (error) => {
    logger.error("Query error", error, { context: "ReactQuery" })
  },
})

const mutationCache = new MutationCache({
  onError: (error) => {
    logger.error("Mutation error", error, { context: "ReactQuery" })
  },
})

// Create a client
const queryClient = new QueryClient({
  queryCache,
  mutationCache,
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes
      gcTime: 1000 * 60 * 10, // 10 minutes
      retry: 3,
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    },
    mutations: {
      retry: 2,
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 15000),
    },
  },
})

interface QueryProviderProps {
  children: React.ReactNode
}

export const QueryProvider: React.FC<QueryProviderProps> = ({ children }) => {
  return (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  )
}

export { queryClient }
