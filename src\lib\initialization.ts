import { jwtDecode } from "jwt-decode"
import { useEffect, useState, useCallback, useRef } from "react"
import { useLocation } from "react-router-dom"

import {
  fetchBalance,
  RouletteBetRequest,
  submitBet,
} from "@/hooks/api/use-api"
import { useRoundPhase } from "@/hooks/game/use-game-phases"
import { useMobile } from "@/hooks/use-mobile"
import { TokenData } from "@/stores/auth-store"
import { useAuthStore } from "@/stores/auth-store"
import { useBettingStore } from "@/stores/betting-store"
import { useGameStateStore } from "@/stores/game-state-store"

const SUBMIT_TIMEOUT = 20 * 60 * 1000
const MIN_BET_AMOUNT = 2

export const useInitialization = () => {
  const { Betting, Resulting, Spinning } = useRoundPhase()
  const isMobile = useMobile()

  const { setTokenData, tokenData } = useAuthStore()
  const setToken = useAuthStore((state) => state.setToken)
  const setBalance = useBettingStore((state) => state.setBalance)
  const setPlacedChips = useBettingStore((state) => state.setPlacedChips)
  const placedChips = useBettingStore((state) => state.placedChips)
  const balance = useBettingStore((state) => state.balance)
  const setBetslipId = useBettingStore((state) => state.setBetslipId)
  const totalBet = useBettingStore((state) => state.totalBet)

  const roundData = useGameStateStore((state) => state.roundData)
  const setGameType = useGameStateStore((state) => state.setGameType)
  const setSessionActive = useGameStateStore((state) => state.setSessionActive)

  const [hasSubmitted, setHasSubmitted] = useState(false)
  const [showTimeoutModal, setShowTimeoutModal] = useState(false)
  const [validationError, setValidationError] = useState<string | null>(null)
  const submitTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  const location = useLocation()
  const token = new URLSearchParams(location.search).get("token")

  const validateBet = useCallback((): string | null => {
    if (!token) return "Authentication token is missing"
    if (!tokenData) return "Player data is not available"
    if (!roundData?.rouletteDrawId) return "Round data is not available"
    if (placedChips.length === 0) return "No bets have been placed"
    if (totalBet < MIN_BET_AMOUNT) return "Bet amount is too low"
    if (totalBet > balance) return "Insufficient balance"
    return null
  }, [token, tokenData, roundData, placedChips, totalBet, balance])

  const resetTimeoutWarning = useCallback(() => {
    if (submitTimeoutRef.current) {
      clearTimeout(submitTimeoutRef.current)
      submitTimeoutRef.current = null
    }
    setShowTimeoutModal(false)
  }, [])

  useEffect(() => {
    if (!token) return

    const decodedToken = jwtDecode(token) as TokenData
    setTokenData(decodedToken)
    setToken(token)
    fetchBalance(token, setBalance, setPlacedChips)
  }, [token])

  useEffect(() => {
    if (!token || !Resulting) return
    fetchBalance(token, setBalance, setPlacedChips)
  }, [Resulting])

  useEffect(() => {
    if (Betting && placedChips.length > 0 && !hasSubmitted) {
      resetTimeoutWarning()
      submitTimeoutRef.current = setTimeout(() => {
        setShowTimeoutModal(true)
        setSessionActive(false)
      }, SUBMIT_TIMEOUT)
    }

    return () => resetTimeoutWarning()
  }, [Betting, placedChips, hasSubmitted])

  useEffect(() => {
    if (!Spinning) {
      setHasSubmitted(false)
      setValidationError(null)
      return
    }

    if (hasSubmitted) return

    const error = validateBet()
    if (error) {
      setBetslipId(0)
      setValidationError(error)
      return
    }

    const betSubmission: RouletteBetRequest = {
      playerGuid: Number(tokenData?.PlayerID) || 0,
      currencyCode: tokenData?.CurrencyCode || "",
      rouletteDrawId: roundData?.rouletteDrawId || 0,
      bets: placedChips.map((chip) => ({
        bet_type_id: chip.bet_type_id ?? 1,
        total_chip_value: chip.total_chip_value,
        cell_id:
          typeof chip.cell_id === "string"
            ? chip.cell_id
            : chip.cell_id.toString(),
        cell_selectors: chip.cell_selectors,
      })),
    }

    submitBet(betSubmission, token!, setPlacedChips, setBetslipId, setBalance)
    setHasSubmitted(true)
    resetTimeoutWarning()
  }, [Spinning, token, placedChips, roundData, tokenData])

  return {
    validationError,
    showTimeoutModal,
    setShowTimeoutModal,
  }
}
