import { History } from "@/hooks"
import { bonusSelectors } from "@/config/bonus-config"
import { LAYOUT_CONSTANTS } from "./layout-constants"

interface GameHistoryDisplayProps {
  gameHistoryResults: History[]
}

interface HistoryItemProps {
  historyEntry: History | number
  entryIndex: number
}

const HistoryResultItem = ({ historyEntry, entryIndex }: HistoryItemProps) => {
  const isNumericEntry = typeof historyEntry === "number"

  if (isNumericEntry) {
    return <span>{historyEntry}</span>
  }

  const bonusImageSource =
    bonusSelectors[historyEntry.bonusNumber - 1]?.bonusSource

  return (
    <>
      {historyEntry.rouletteNumber}
      <img
        className='aspect-square w-4'
        src={bonusImageSource}
        alt='bonus symbol'
      />
    </>
  )
}

const GameHistoryDisplay = ({
  gameHistoryResults,
}: GameHistoryDisplayProps) => {
  const displayedHistoryResults = gameHistoryResults.slice(
    0,
    LAYOUT_CONSTANTS.HISTORY_DISPLAY_LIMIT
  )

  return (
    <section className='flex snap-x snap-mandatory gap-3 overflow-x-auto bg-black'>
      {displayedHistoryResults.map((historyEntry, entryIndex) => (
        <span
          key={entryIndex}
          className='flex snap-center flex-col items-center justify-center text-center align-middle text-xxs font-bold'
        >
          <HistoryResultItem
            historyEntry={historyEntry}
            entryIndex={entryIndex}
          />
        </span>
      ))}
    </section>
  )
}

export default GameHistoryDisplay
