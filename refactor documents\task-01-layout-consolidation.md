# Task 01: Layout Component Consolidation

## Task Overview

**Priority**: High | **Risk**: Low | **Impact**: High  
**Estimated Duration**: 2 weeks | **Dependencies**: None

### Description
Refactor the monolithic `Online.tsx` layout component (447 lines) by extracting mobile/desktop layout logic into focused, reusable components. This task addresses mixed concerns, duplicate constants, and complex conditional rendering that makes the main layout difficult to maintain.

### Expected Impact
- Reduce `Online.tsx` from 447 to <150 lines (66% reduction)
- Improve component testability and reusability
- Enable layout-specific optimizations
- Simplify future layout modifications

## Current State Analysis

### Primary Issues in `src/layouts/Online.tsx`

**Lines 45-70**: Hardcoded layout constants
```typescript
const MOBILE_LAYOUT_CONSTANTS = {
  TOP_SECTION_HEIGHT: "40vh",
  HISTORY_HEIGHT: "5vh",
  VIDEO_HEIGHT: "24vh",
  // ... more magic numbers
}
```

**Lines 145-178**: Inline `GameHistoryDisplay` component (33 lines)
```typescript
const GameHistoryDisplay = () => {
  // Complex inline component logic
  return (
    <section className='flex snap-x snap-mandatory gap-3 overflow-x-auto bg-black'>
      {/* 33 lines of rendering logic */}
    </section>
  )
}
```

**Lines 193-214**: Complex mobile layout with magic numbers
```typescript
<div className={`col-span-full w-full row-start-1 relative max-h-[${MOBILE_LAYOUT_CONSTANTS.TOP_SECTION_HEIGHT}] overflow-hidden`}>
```

**Lines 265-341**: Duplicate mobile betting area logic
**Lines 375-441**: Control panel configuration mixed with layout

### Related Files Requiring Updates
- `src/components/online/layout-constants.ts` (partial constants)
- `src/components/online/mobile-layout-sections.tsx` (incomplete mobile logic)
- `src/components/online/desktop-layout-sections.tsx` (incomplete desktop logic)

## Target Architecture

### New File Structure
```
src/layouts/
├── Online.tsx (orchestrator only, <150 lines)
├── components/
│   ├── DesktopLayout.tsx
│   ├── MobileLayout.tsx
│   ├── GameHistorySection.tsx
│   ├── ControlPanelSection.tsx
│   └── shared/
│       ├── LayoutConstants.ts
│       ├── LayoutTypes.ts
│       └── LayoutUtils.ts
```

### Target Online.tsx Structure
```typescript
function Online() {
  const layoutConfig = useLayoutConfiguration()
  const isMobile = useMobile()
  
  return (
    <main className={layoutConfig.containerClasses}>
      <ResultOverlay />
      <BettingOverlay />
      
      {isMobile ? (
        <MobileLayout {...layoutConfig.mobile} />
      ) : (
        <DesktopLayout {...layoutConfig.desktop} />
      )}
      
      <ControlPanelSection {...layoutConfig.controlPanel} />
    </main>
  )
}
```

### Layout Configuration Hook
```typescript
interface LayoutConfiguration {
  containerClasses: string
  mobile: MobileLayoutProps
  desktop: DesktopLayoutProps
  controlPanel: ControlPanelSectionProps
}

const useLayoutConfiguration = (): LayoutConfiguration => {
  const gameState = useGameState()
  const controlPanels = useControlPanels()
  
  return useMemo(() => ({
    containerClasses: cn(
      LAYOUT_CONSTANTS.CONTAINER.BASE,
      LAYOUT_CONSTANTS.CONTAINER.GRID_MOBILE,
      LAYOUT_CONSTANTS.CONTAINER.GRID_DESKTOP
    ),
    mobile: createMobileLayoutProps(gameState, controlPanels),
    desktop: createDesktopLayoutProps(gameState, controlPanels),
    controlPanel: createControlPanelProps(gameState, controlPanels)
  }), [gameState, controlPanels])
}
```

## Step-by-Step Implementation Plan

### Phase 1: Constants and Types (Days 1-2)

#### Day 1: Consolidate Layout Constants
- [ ] Create `src/layouts/components/shared/LayoutConstants.ts`
- [ ] Move all constants from `Online.tsx` and `layout-constants.ts`
- [ ] Add proper TypeScript types and JSDoc comments
- [ ] Create constant validation utilities

#### Day 2: Create Layout Types
- [ ] Create `src/layouts/components/shared/LayoutTypes.ts`
- [ ] Define interfaces for all layout components
- [ ] Add prop type definitions
- [ ] Create layout state type definitions

### Phase 2: Component Extraction (Days 3-7)

#### Day 3: Extract GameHistorySection
- [ ] Create `src/layouts/components/GameHistorySection.tsx`
- [ ] Extract inline component from lines 145-178
- [ ] Add proper props interface and TypeScript types
- [ ] Add basic unit tests

#### Day 4: Create ControlPanelSection
- [ ] Create `src/layouts/components/ControlPanelSection.tsx`
- [ ] Extract control panel logic from lines 375-441
- [ ] Implement responsive balance display
- [ ] Add comprehensive prop types

#### Day 5-6: Create MobileLayout Component
- [ ] Create `src/layouts/components/MobileLayout.tsx`
- [ ] Extract mobile rendering logic from lines 193-341
- [ ] Implement mobile betting area transformations
- [ ] Add mobile-specific game section configurations

#### Day 7: Create DesktopLayout Component
- [ ] Create `src/layouts/components/DesktopLayout.tsx`
- [ ] Extract desktop grid configuration from lines 216-262
- [ ] Implement desktop-specific game sections
- [ ] Add desktop layout optimizations

### Phase 3: Integration and Optimization (Days 8-14)

#### Day 8-10: Create Layout Configuration Hook
- [ ] Create `useLayoutConfiguration` hook
- [ ] Implement layout props factories
- [ ] Add memoization for performance
- [ ] Create layout utilities

#### Day 11-12: Refactor Online.tsx
- [ ] Update Online.tsx to use extracted components
- [ ] Remove extracted code and update imports
- [ ] Implement layout orchestration pattern
- [ ] Verify functionality preservation

#### Day 13-14: Testing and Optimization
- [ ] Add comprehensive integration tests
- [ ] Performance testing and optimization
- [ ] Accessibility testing
- [ ] Documentation updates

## Code Examples

### Before: Current Online.tsx (Simplified)
```typescript
function Online() {
  // 50+ lines of hooks and state
  const MOBILE_LAYOUT_CONSTANTS = { /* ... */ }
  
  const GameHistoryDisplay = () => {
    // 33 lines of inline component
  }
  
  return (
    <main className="h-[100vh] max-h-[100vh] grid gap-4...">
      {/* 400+ lines of complex conditional rendering */}
      {isMobileDevice && (
        <>
          <BettingOverlay />
          <div className={`col-span-full w-full...`}>
            {/* Complex mobile layout */}
          </div>
        </>
      )}
      
      {!isMobileDevice && (
        <>
          {/* Complex desktop layout */}
        </>
      )}
      
      {/* Control panel section */}
    </main>
  )
}
```

### After: Refactored Online.tsx
```typescript
function Online() {
  useAudioController()
  useInitialization()
  useGameNotifications()
  
  const layoutConfig = useLayoutConfiguration()
  const isMobile = useMobile()
  
  return (
    <main className={layoutConfig.containerClasses}>
      <ResultOverlay />
      
      {isMobile ? (
        <MobileLayout {...layoutConfig.mobile} />
      ) : (
        <DesktopLayout {...layoutConfig.desktop} />
      )}
      
      <ControlPanelSection {...layoutConfig.controlPanel} />
    </main>
  )
}
```

### New MobileLayout Component
```typescript
interface MobileLayoutProps {
  gameHistory: History[]
  gamePhases: GamePhases
  gameType: GameType
  bonusSymbolsProps: BonusSymbolsGridProps
}

export const MobileLayout: React.FC<MobileLayoutProps> = ({
  gameHistory,
  gamePhases,
  gameType,
  bonusSymbolsProps
}) => {
  return (
    <>
      <BettingOverlay />
      <div className={LAYOUT_CONSTANTS.MOBILE.TOP_SECTION}>
        <GameHistorySection history={gameHistory} />
        <VideoPlayer />
        <SideMenu />
      </div>
      
      <div className={cn(
        LAYOUT_CONSTANTS.MOBILE.BETTING_AREA,
        gamePhases.isBetting && LAYOUT_CONSTANTS.MOBILE.BETTING_ACTIVE
      )}>
        <BonusBetsSection {...bonusSymbolsProps} />
        <RouletteTableSection gameType={gameType} />
      </div>
    </>
  )
}
```

## Testing Strategy

### Unit Tests
```typescript
// GameHistorySection.test.tsx
describe('GameHistorySection', () => {
  it('renders history items correctly', () => {
    const mockHistory = createMockHistory(5)
    render(<GameHistorySection history={mockHistory} />)
    expect(screen.getAllByRole('listitem')).toHaveLength(5)
  })
  
  it('applies display limit correctly', () => {
    const mockHistory = createMockHistory(20)
    render(<GameHistorySection history={mockHistory} displayLimit={10} />)
    expect(screen.getAllByRole('listitem')).toHaveLength(10)
  })
})

// MobileLayout.test.tsx
describe('MobileLayout', () => {
  it('applies betting phase transformations', () => {
    const props = createMobileLayoutProps({ isBetting: true })
    render(<MobileLayout {...props} />)
    expect(screen.getByTestId('betting-area')).toHaveClass('betting-active')
  })
})
```

### Integration Tests
```typescript
// Online.integration.test.tsx
describe('Online Layout Integration', () => {
  it('switches between mobile and desktop layouts', () => {
    const { rerender } = render(<Online />)
    
    // Test mobile layout
    mockUseMobile.mockReturnValue(true)
    rerender(<Online />)
    expect(screen.getByTestId('mobile-layout')).toBeInTheDocument()
    
    // Test desktop layout
    mockUseMobile.mockReturnValue(false)
    rerender(<Online />)
    expect(screen.getByTestId('desktop-layout')).toBeInTheDocument()
  })
})
```

### Visual Regression Tests
- Screenshot testing for layout components
- Responsive behavior validation
- Game phase transition testing

## Risk Mitigation

### Potential Issues
1. **Layout Breaking**: Complex CSS grid dependencies
2. **State Management**: Props drilling in new components
3. **Performance**: Re-rendering of large layout components

### Mitigation Strategies
1. **Gradual Migration**: Use feature flags for new components
2. **CSS Isolation**: Maintain existing CSS classes during transition
3. **State Optimization**: Implement proper memoization
4. **Rollback Plan**: Keep original Online.tsx as backup

### Rollback Procedure
```typescript
// Feature flag approach
const USE_NEW_LAYOUT = import.meta.env.VITE_USE_NEW_LAYOUT === 'true'

function Online() {
  return USE_NEW_LAYOUT ? <NewOnlineLayout /> : <LegacyOnlineLayout />
}
```

## Success Criteria

### Quantitative Metrics
- [ ] Online.tsx reduced from 447 to <150 lines (66% reduction)
- [ ] 4+ new focused components created
- [ ] Layout constants centralized (100% consolidation)
- [ ] Test coverage >85% for new components
- [ ] No performance regression (maintain <16ms render time)

### Qualitative Metrics
- [ ] Zero functional regression
- [ ] Improved developer experience for layout modifications
- [ ] Better component reusability
- [ ] Cleaner separation of concerns
- [ ] Enhanced maintainability

### Acceptance Criteria
- [ ] All existing functionality preserved
- [ ] Mobile/desktop responsive behavior maintained
- [ ] Game phase transitions work correctly
- [ ] Control panel interactions function properly
- [ ] Performance benchmarks met or exceeded
- [ ] Code review approval from team
- [ ] QA testing passed
