/* Control panel scrolling styles */

/* Make the scrollbar more visible on desktop */
[data-radix-scroll-area-viewport] {
  scrollbar-width: thin;
  scrollbar-color: rgba(198, 158, 97, 0.5) transparent;
}

[data-radix-scroll-area-viewport]::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

[data-radix-scroll-area-viewport]::-webkit-scrollbar-track {
  background: transparent;
}

[data-radix-scroll-area-viewport]::-webkit-scrollbar-thumb {
  background-color: rgba(198, 158, 97, 0.5);
  border-radius: 20px;
  border: 2px solid transparent;
}

[data-radix-scroll-area-viewport]::-webkit-scrollbar-thumb:hover {
  background-color: rgba(198, 158, 97, 0.8);
}

/* Add a subtle gradient to indicate scrollable content */
.control-panel-scroll-container {
  position: relative;
}

.control-panel-scroll-container::after,
.control-panel-scroll-container::before {
  content: "";
  position: absolute;
  top: 0;
  bottom: 0;
  width: 20px;
  z-index: 1;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.control-panel-scroll-container::before {
  left: 0;
  background: linear-gradient(to right, rgba(0, 0, 0, 0.3), transparent);
}

.control-panel-scroll-container::after {
  right: 0;
  background: linear-gradient(to left, rgba(0, 0, 0, 0.3), transparent);
}

/* Vertical orientation gradient indicators */
.control-panel-scroll-container.vertical::after,
.control-panel-scroll-container.vertical::before {
  width: 100%;
  height: 20px;
  left: 0;
  right: 0;
}

.control-panel-scroll-container.vertical::before {
  top: 0;
  bottom: auto;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.3), transparent);
}

.control-panel-scroll-container.vertical::after {
  top: auto;
  bottom: 0;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.3), transparent);
}

.control-panel-scroll-container.can-scroll-left::before,
.control-panel-scroll-container.vertical.can-scroll-top::before {
  opacity: 1;
}

.control-panel-scroll-container.can-scroll-right::after,
.control-panel-scroll-container.vertical.can-scroll-bottom::after {
  opacity: 1;
}

/* Ensure the scroll area takes full width/height */
.control-panel-scroll-area {
  width: 100%;
  overflow-x: auto;
}

.control-panel-scroll-area.vertical {
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
}

/* Ensure buttons don't shrink */
.control-panel-button-container {
  flex-shrink: 0;
}

/* Improve scroll button visibility */
.control-panel-scroll-button {
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.5);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.control-panel-scroll-button:hover {
  transform: scale(1.1) translateY(-50%);
  box-shadow: 0 0 12px rgba(0, 0, 0, 0.7);
}

/* Vertical orientation scroll buttons */
.control-panel-scroll-button.vertical {
  transform-origin: center;
  left: 50%;
  right: auto;
  transform: translateX(-50%);
}

.control-panel-scroll-button.vertical.top {
  top: 10px;
  bottom: auto;
}

.control-panel-scroll-button.vertical.bottom {
  top: auto;
  bottom: 10px;
}

.control-panel-scroll-button.vertical:hover {
  transform: scale(1.1) translateX(-50%);
}

/* Add focus styles for keyboard navigation */
.control-panel-scroll-area:focus-visible {
  outline: 2px solid #c69e61;
  outline-offset: 2px;
}

/* Responsive control panel styles */
@media (max-width: 768px) {
  /* Adjust spacing for mobile */
  .control-panel-vertical {
    max-height: 300px;
  }
}

/* Vertical control panel specific styles */
.control-panel-vertical {
  height: 100%;
  min-height: 150px;
  display: flex;
  flex-direction: column;
}

.control-panel-vertical .control-panel-scroll-area {
  flex-grow: 1;
}
