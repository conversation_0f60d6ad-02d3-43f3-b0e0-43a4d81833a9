import React, { ReactNode } from "react"
import { cn } from "@/lib/utils"

export interface ScrollFadeProps {
  /**
   * The content to be scrolled with fade effect
   */
  children: ReactNode

  /**
   * Additional class names to apply to the container
   */
  className?: string

  /**
   * Direction of the fade effect
   * - 'y': Vertical fade at the bottom
   * - 'x': Horizontal fade at the right
   * - 'y-both': Vertical fade at top and bottom
   * - 'x-both': Horizontal fade at left and right
   */
  direction?: "y" | "x" | "y-both" | "x-both"

  /**
   * Size of the fade effect
   * - 'sm': Small fade (20px)
   * - 'md': Medium fade (40px)
   * - 'lg': Large fade (60px)
   */
  size?: "sm" | "md" | "lg"

  /**
   * Whether to hide the scrollbar
   */
  hideScrollbar?: boolean
}

/**
 * ScrollFade component adds a fade effect to scrollable content
 * It uses CSS mask-image to create a gradient fade effect in the scroll direction
 */
export function ScrollFade({
  children,
  className,
  direction = "y",
  size = "md",
  hideScrollbar = true,
}: ScrollFadeProps) {
  return (
    <div className={cn("scroll-fade-container", className)}>
      <div
        className={cn(
          `scroll-fade-${direction}`,
          `scroll-fade-${size}`,
          {
            "invis-scroll": hideScrollbar,
            "overflow-y-auto": direction.includes("y"),
            "overflow-x-auto": direction.includes("x"),
          },
          "h-full w-full"
        )}
      >
        {children}
      </div>
    </div>
  )
}

export default ScrollFade
