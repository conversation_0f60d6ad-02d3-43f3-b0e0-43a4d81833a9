"use client"

import { useState, useEffect } from "react"

/**
 * Simple in-memory cache for loaded images
 * Using a global cache to persist across component instances
 */
const imageCache = new Map<string, boolean>()

/**
 * Custom hook for optimized image loading with caching
 * @param src Image source URL
 * @param id Unique identifier for the image
 * @returns Loading state and error state
 */
export function useImageLoader(src: string, id: string) {
  const [loaded, setLoaded] = useState(() => {
    // Initialize with cached state if available
    if (!src) return true
    return imageCache.get(src) || false
  })
  const [error, setError] = useState(false)

  useEffect(() => {
    // Reset states when src changes
    setLoaded(imageCache.get(src) || false)
    setError(false)

    // If no source, mark as loaded immediately
    if (!src) {
      setLoaded(true)
      return
    }

    // Check if image is already cached in memory
    if (imageCache.get(src)) {
      setLoaded(true)
      return
    }

    // Create a new image element to load the image
    const img = new Image()

    const handleLoad = () => {
      imageCache.set(src, true)
      setLoaded(true)
    }

    const handleError = () => {
      // Log error but don't block UI
      console.warn(`Failed to load image: ${src} for button ${id}`)
      imageCache.set(src, true) // Cache the error state too
      setError(true)
      setLoaded(true) // Mark as loaded to avoid blocking
    }

    // Add event listeners before setting src
    img.addEventListener("load", handleLoad)
    img.addEventListener("error", handleError)

    // Set src to trigger loading
    img.src = src

    // Clean up event listeners on unmount
    return () => {
      img.removeEventListener("load", handleLoad)
      img.removeEventListener("error", handleError)
    }
  }, [src, id])

  return { loaded, error }
}
