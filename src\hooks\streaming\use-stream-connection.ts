import { useCallback, useEffect, useRef } from "react"
import { useGameStateStore } from "@/stores/game-state-store"
import { useRoundPhase } from "../game"
import { RouletteRound } from "./types"
import { useSignalRConnection } from "./use-signalr-connection"
import { useVideoSource } from "./use-video-source"

/**
 * Main connection hook that combines SignalR connection with video source management
 * This maintains backward compatibility with the original implementation
 */
export const useSignalR = (
  setupVideo?: (url: string, position: number) => void,
  cleanup?: () => void
) => {
  const { Betting, Spinning, Resulting } = useRoundPhase()
  const setRoundData = useGameStateStore((state) => state.setRoundData)
  const isSessionActive = useGameStateStore((state) => state.isSessionActive)

  // Use our new modular hooks
  const {
    hubConnectionRef,
    isConnected,
    setIsConnected,
    setupSignalR,
    disconnectSignalR,
    isMountedRef,
    registerEventHandlers,
  } = useSignalRConnection()

  const {
    updateVideoSource,
    updatePosition,
    isSideViewRef,
    videoStartPositionRef,
  } = useVideoSource()

  // Create refs that were previously in this hook
  const videoRef = useRef<HTMLVideoElement | null>(null)
  const isPlayingRef = useRef<boolean>(false)
  const currentRoundIdRef = useRef<number | null>(null)
  const roundStartTimeRef = useRef<number | null>(null)
  const roundPhasesRef = useRef({ Betting, Spinning, Resulting })
  const retryTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null)

  // Handle stream sync events from server
  const handleStreamSync = useCallback(
    ({ position, roundId }: { position: number; roundId: number }) => {
      if (!isMountedRef.current) return

      if (currentRoundIdRef.current === roundId) {
        updatePosition(position)
      }

      if (isSideViewRef.current) return

      const { Spinning, Resulting } = roundPhasesRef.current
      if (
        currentRoundIdRef.current === roundId &&
        videoRef.current &&
        isPlayingRef.current &&
        (Spinning || Resulting)
      ) {
        const relativePosition = isSideViewRef.current
          ? position
          : position - videoStartPositionRef.current

        const currentTime = videoRef.current.currentTime
        const drift = Math.abs(currentTime - relativePosition)

        if (drift > 0.5) {
          videoRef.current.currentTime = relativePosition
        }
      }
    },
    [updatePosition, isMountedRef, isSideViewRef, videoStartPositionRef]
  )

  // Handle video source updates
  const handleVideoSourceUpdate = useCallback(
    (data: {
      round: RouletteRound
      position: number
      previousNumber: number
    }) => {
      if (!isMountedRef.current) return

      const videoSourceInfo = updateVideoSource(data)

      if (videoRef.current) {
        videoRef.current.muted = isSideViewRef.current
      }

      if (videoSourceInfo && setupVideo && isMountedRef.current) {
        setupVideo(videoSourceInfo.url, videoSourceInfo.startPosition)
      }
    },
    [setupVideo, updateVideoSource, isMountedRef, isSideViewRef]
  )

  // Handle round change events from server
  const handleRoundChange = useCallback(
    (data: {
      round: RouletteRound
      position: number
      serverTime: number
      roundStartTime: number
      previousNumber: number
    }) => {
      if (!isMountedRef.current) return

      setRoundData(data.round)
      currentRoundIdRef.current = data.round.rouletteDrawId
      roundStartTimeRef.current = data.roundStartTime
      updatePosition(data.position)

      handleVideoSourceUpdate({
        round: data.round,
        position: data.position,
        previousNumber: data.previousNumber,
      })
    },
    [setRoundData, handleVideoSourceUpdate, updatePosition, isMountedRef]
  )

  // Setup connection and event handlers
  useEffect(() => {
    isMountedRef.current = true
    let isCleanedUp = false
    let unregisterEvents: (() => void) | undefined

    if (isSessionActive && !isCleanedUp) {
      ;(async () => {
        const hubConnection = await setupSignalR()

        // Register event handlers
        unregisterEvents = registerEventHandlers(
          handleRoundChange,
          handleStreamSync
        )

        if (hubConnection.state !== "Connected") {
          await hubConnection.start()
        }
        setIsConnected(true)
        await hubConnection.invoke("RequestCurrentRound")
      })()
    } else {
      disconnectSignalR()
      setIsConnected(false)
      cleanup?.()
    }

    return () => {
      isCleanedUp = true
      isMountedRef.current = false
      if (retryTimeoutRef.current) clearTimeout(retryTimeoutRef.current)
      unregisterEvents?.()
      disconnectSignalR()
      cleanup?.()
    }
  }, [
    isSessionActive,
    setupSignalR,
    disconnectSignalR,
    registerEventHandlers,
    handleRoundChange,
    handleStreamSync,
    setIsConnected,
    cleanup,
  ])

  // Update round phases ref when phases change
  useEffect(() => {
    roundPhasesRef.current = { Betting, Spinning, Resulting }
  }, [Betting, Spinning, Resulting])

  // Update video source when betting phase changes
  useEffect(() => {
    if (!isMountedRef.current || !currentRoundIdRef.current) return

    const currentRound = useGameStateStore.getState().roundData
    if (!currentRound) return

    handleVideoSourceUpdate({
      round: currentRound,
      position: videoStartPositionRef.current,
      previousNumber: currentRound.rouletteNumber,
    })
  }, [Betting, handleVideoSourceUpdate, videoStartPositionRef, isMountedRef])

  return {
    videoRef,
    isPlayingRef,
    hubConnectionRef,
    disconnectSignalR,
    isConnected,
  }
}
