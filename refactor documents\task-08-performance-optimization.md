# Task 08: Performance Optimization

## Task Overview

**Priority**: Low-Medium | **Risk**: Medium | **Impact**: Medium  
**Estimated Duration**: 1.5 weeks | **Dependencies**: Tasks 01-03 (Layout, Control Panel, Table)

### Description
Implement targeted performance optimizations including memoization, bundle splitting, image optimization, and virtual scrolling. This addresses identified performance bottlenecks and prepares the application for better scalability and user experience.

### Expected Impact
- Improve initial load time by 15%
- Reduce memory usage during gameplay
- Optimize re-render frequency in game components
- Better mobile performance and responsiveness

## Current State Analysis

### Performance Bottlenecks Identified

**Component Re-rendering Issues**:
- Complex components re-rendering unnecessarily
- Missing memoization in expensive calculations
- Prop drilling causing cascade re-renders
- Large component trees without optimization

**Bundle Size Concerns**:
- Monolithic bundle loading all features upfront
- No code splitting for feature components
- Large dependencies loaded synchronously
- Missing tree-shaking opportunities

**Memory Usage Patterns**:
- Audio resources not properly cleaned up
- Large state objects causing memory leaks
- Event listeners not properly removed
- Image resources not optimized

**Mobile Performance Issues**:
- Touch interactions causing lag
- Large DOM trees on mobile devices
- Unoptimized animations and transitions
- Heavy computation on main thread

### Specific Areas for Optimization

**Winners Panel Virtual Scrolling**:
```typescript
// Current implementation renders all winners
{winners.map((winner, index) => (
  <div key={index} className={STYLES.winnerItem}>
    <span>{formatCurrency(winner.amount)}</span>
    <span>{winner.username}</span>
  </div>
))}
```

**Control Panel Re-rendering**:
```typescript
// useControlPanels hook recreates buttons on every render
const betOptionsButtons = useMemo<ControlPanelButton[]>(
  () => [/* ... */],
  [handleBetAction, Betting, autoplayActive, showHotCold, showPastNumbers]
)
```

**Image Loading Optimization**:
- Chip images loaded synchronously
- Bonus symbols not lazy-loaded
- No image preloading strategy
- Missing WebP format support

## Target Architecture

### Performance Optimization Structure
```
src/performance/
├── memoization/
│   ├── ComponentMemo.tsx (memoized components)
│   ├── HookMemo.ts (memoized hooks)
│   └── SelectorMemo.ts (memoized selectors)
├── virtualization/
│   ├── VirtualizedWinners.tsx (virtual scrolling)
│   ├── VirtualizedHistory.tsx (virtual history)
│   └── VirtualScrollUtils.ts (utilities)
├── bundling/
│   ├── LazyComponents.tsx (lazy loading)
│   ├── CodeSplitting.ts (split points)
│   └── PreloadStrategies.ts (preloading)
├── images/
│   ├── ImageOptimizer.ts (image optimization)
│   ├── LazyImageLoader.tsx (lazy loading)
│   └── ImagePreloader.ts (preloading)
└── monitoring/
    ├── PerformanceMonitor.ts (monitoring)
    ├── MemoryTracker.ts (memory tracking)
    └── RenderProfiler.tsx (render profiling)
```

### Memoized Component System
```typescript
// ComponentMemo.tsx
export const MemoizedRouletteTable = React.memo(RouletteTable, (prevProps, nextProps) => {
  // Custom comparison for roulette table props
  return (
    prevProps.gameType === nextProps.gameType &&
    prevProps.hoveredCell?.id === nextProps.hoveredCell?.id &&
    prevProps.placedChips.length === nextProps.placedChips.length
  )
})

export const MemoizedControlPanel = React.memo(ControlPanel, (prevProps, nextProps) => {
  // Shallow comparison for control panel buttons
  return (
    prevProps.buttons.length === nextProps.buttons.length &&
    prevProps.buttons.every((button, index) => 
      button.id === nextProps.buttons[index]?.id &&
      button.disabled === nextProps.buttons[index]?.disabled &&
      button.active === nextProps.buttons[index]?.active
    )
  )
})

export const MemoizedGameSection = React.memo(GameSection, (prevProps, nextProps) => {
  return (
    prevProps.title === nextProps.title &&
    prevProps.className === nextProps.className &&
    prevProps.hasInsetShadow === nextProps.hasInsetShadow
  )
})
```

### Virtual Scrolling Implementation
```typescript
// VirtualizedWinners.tsx
interface VirtualizedWinnersProps {
  winners: Winner[]
  height: number
  itemHeight: number
  overscan?: number
}

export const VirtualizedWinners: React.FC<VirtualizedWinnersProps> = ({
  winners,
  height,
  itemHeight,
  overscan = 5
}) => {
  const [scrollTop, setScrollTop] = useState(0)
  const containerRef = useRef<HTMLDivElement>(null)
  
  const visibleRange = useMemo(() => {
    const start = Math.floor(scrollTop / itemHeight)
    const end = Math.min(
      start + Math.ceil(height / itemHeight) + overscan,
      winners.length
    )
    return { start: Math.max(0, start - overscan), end }
  }, [scrollTop, height, itemHeight, overscan, winners.length])
  
  const visibleItems = useMemo(() => 
    winners.slice(visibleRange.start, visibleRange.end),
    [winners, visibleRange]
  )
  
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop)
  }, [])
  
  return (
    <div
      ref={containerRef}
      className="overflow-auto"
      style={{ height }}
      onScroll={handleScroll}
    >
      <div style={{ height: winners.length * itemHeight, position: 'relative' }}>
        {visibleItems.map((winner, index) => (
          <div
            key={visibleRange.start + index}
            className={STYLES.winnerItem}
            style={{
              position: 'absolute',
              top: (visibleRange.start + index) * itemHeight,
              height: itemHeight,
              width: '100%'
            }}
          >
            <span className={STYLES.winnerAmount}>
              {formatCurrency(winner.amount)}
            </span>
            <span className={STYLES.winnerUsername}>
              {winner.username}
            </span>
          </div>
        ))}
      </div>
    </div>
  )
}
```

### Bundle Splitting Strategy
```typescript
// LazyComponents.tsx
export const LazyRouletteTable = lazy(() => 
  import('../features/roulette-boards/roulette-table').then(module => ({
    default: module.default
  }))
)

export const LazyControlPanel = lazy(() => 
  import('../features/bottom-navigation/control-panel').then(module => ({
    default: module.ControlPanel
  }))
)

export const LazyVideoPlayer = lazy(() => 
  import('../features/streaming/video-player').then(module => ({
    default: module.default
  }))
)

// CodeSplitting.ts
export const createLazyComponent = <T extends React.ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  fallback?: React.ComponentType
) => {
  const LazyComponent = lazy(importFn)
  
  return React.forwardRef<any, React.ComponentProps<T>>((props, ref) => (
    <Suspense fallback={fallback ? <fallback /> : <div>Loading...</div>}>
      <LazyComponent {...props} ref={ref} />
    </Suspense>
  ))
}
```

### Image Optimization System
```typescript
// ImageOptimizer.ts
interface OptimizedImageProps {
  src: string
  alt: string
  width?: number
  height?: number
  priority?: boolean
  placeholder?: 'blur' | 'empty'
  className?: string
}

export const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  width,
  height,
  priority = false,
  placeholder = 'empty',
  className
}) => {
  const [isLoaded, setIsLoaded] = useState(false)
  const [error, setError] = useState(false)
  const imgRef = useRef<HTMLImageElement>(null)
  
  // Generate optimized src URLs
  const optimizedSrc = useMemo(() => {
    const isWebPSupported = supportsWebP()
    const baseUrl = src.replace(/\.(jpg|jpeg|png)$/i, '')
    
    if (isWebPSupported) {
      return `${baseUrl}.webp`
    }
    return src
  }, [src])
  
  // Lazy loading with Intersection Observer
  useEffect(() => {
    if (!priority && imgRef.current) {
      const observer = new IntersectionObserver(
        ([entry]) => {
          if (entry.isIntersecting) {
            const img = entry.target as HTMLImageElement
            img.src = optimizedSrc
            observer.disconnect()
          }
        },
        { rootMargin: '50px' }
      )
      
      observer.observe(imgRef.current)
      return () => observer.disconnect()
    }
  }, [optimizedSrc, priority])
  
  return (
    <div className={cn('relative overflow-hidden', className)}>
      {placeholder === 'blur' && !isLoaded && (
        <div className="absolute inset-0 bg-gray-200 animate-pulse" />
      )}
      
      <img
        ref={imgRef}
        src={priority ? optimizedSrc : undefined}
        alt={alt}
        width={width}
        height={height}
        className={cn(
          'transition-opacity duration-300',
          isLoaded ? 'opacity-100' : 'opacity-0'
        )}
        onLoad={() => setIsLoaded(true)}
        onError={() => setError(true)}
        loading={priority ? 'eager' : 'lazy'}
      />
      
      {error && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
          <span className="text-gray-500 text-sm">Failed to load</span>
        </div>
      )}
    </div>
  )
}
```

## Step-by-Step Implementation Plan

### Phase 1: Component Memoization (Days 1-3)

#### Day 1: Identify Re-rendering Issues
- [ ] Add React DevTools Profiler to identify re-rendering
- [ ] Analyze component render frequency
- [ ] Identify expensive calculations and operations
- [ ] Create performance baseline measurements

#### Day 2: Implement Component Memoization
- [ ] Create memoized versions of heavy components
- [ ] Add custom comparison functions for complex props
- [ ] Implement useMemo for expensive calculations
- [ ] Add useCallback for event handlers

#### Day 3: Optimize Hook Dependencies
- [ ] Review and optimize hook dependency arrays
- [ ] Implement memoized selectors for Zustand stores
- [ ] Add useMemo for derived state calculations
- [ ] Create performance monitoring utilities

### Phase 2: Virtual Scrolling and List Optimization (Days 4-6)

#### Day 4: Implement Virtual Scrolling
- [ ] Create VirtualizedWinners component
- [ ] Implement virtual scrolling for past results
- [ ] Add virtual scrolling utilities
- [ ] Create performance benchmarks

#### Day 5: Optimize Large Lists
- [ ] Implement windowing for game history
- [ ] Add pagination for bet history
- [ ] Optimize list rendering performance
- [ ] Add list virtualization testing

#### Day 6: Memory Management
- [ ] Implement proper cleanup for event listeners
- [ ] Add memory leak detection
- [ ] Optimize audio resource management
- [ ] Create memory monitoring utilities

### Phase 3: Bundle Optimization (Days 7-9)

#### Day 7: Implement Code Splitting
- [ ] Add lazy loading for feature components
- [ ] Implement route-based code splitting
- [ ] Create dynamic imports for heavy dependencies
- [ ] Add bundle analysis tools

#### Day 8: Image Optimization
- [ ] Implement optimized image component
- [ ] Add WebP format support
- [ ] Implement lazy image loading
- [ ] Create image preloading strategies

#### Day 9: Bundle Analysis and Optimization
- [ ] Analyze bundle size and composition
- [ ] Implement tree-shaking optimizations
- [ ] Add compression and minification
- [ ] Create bundle monitoring

### Phase 4: Performance Monitoring (Days 10-11)

#### Day 10: Performance Monitoring
- [ ] Implement performance monitoring system
- [ ] Add Core Web Vitals tracking
- [ ] Create performance dashboards
- [ ] Add automated performance testing

#### Day 11: Final Optimization and Testing
- [ ] Conduct comprehensive performance testing
- [ ] Optimize based on monitoring data
- [ ] Create performance documentation
- [ ] Add performance regression testing

## Code Examples

### Before: Unoptimized Component
```typescript
// Heavy re-rendering component
const ControlPanel = ({ buttons }) => {
  const [activeButton, setActiveButton] = useState(null)
  
  // Recreated on every render
  const handleClick = (buttonId) => {
    setActiveButton(buttonId)
    // Heavy operation
    processButtonClick(buttonId)
  }
  
  return (
    <div>
      {buttons.map(button => (
        <button key={button.id} onClick={() => handleClick(button.id)}>
          {button.label}
        </button>
      ))}
    </div>
  )
}
```

### After: Optimized Component
```typescript
// Memoized and optimized component
const ControlPanel = React.memo(({ buttons }) => {
  const [activeButton, setActiveButton] = useState(null)
  
  // Memoized callback
  const handleClick = useCallback((buttonId) => {
    setActiveButton(buttonId)
    processButtonClick(buttonId)
  }, [])
  
  // Memoized button rendering
  const renderedButtons = useMemo(() => 
    buttons.map(button => (
      <MemoizedButton 
        key={button.id} 
        button={button}
        onClick={handleClick}
        isActive={activeButton === button.id}
      />
    )),
    [buttons, handleClick, activeButton]
  )
  
  return <div>{renderedButtons}</div>
}, (prevProps, nextProps) => {
  // Custom comparison
  return prevProps.buttons.length === nextProps.buttons.length &&
         prevProps.buttons.every((btn, i) => 
           btn.id === nextProps.buttons[i]?.id &&
           btn.disabled === nextProps.buttons[i]?.disabled
         )
})
```

### Performance Monitoring
```typescript
// PerformanceMonitor.ts
export class PerformanceMonitor {
  private metrics = new Map<string, number[]>()
  
  startMeasure(name: string) {
    performance.mark(`${name}-start`)
  }
  
  endMeasure(name: string) {
    performance.mark(`${name}-end`)
    performance.measure(name, `${name}-start`, `${name}-end`)
    
    const measure = performance.getEntriesByName(name)[0]
    if (measure) {
      this.recordMetric(name, measure.duration)
    }
  }
  
  private recordMetric(name: string, duration: number) {
    if (!this.metrics.has(name)) {
      this.metrics.set(name, [])
    }
    this.metrics.get(name)!.push(duration)
  }
  
  getAverageTime(name: string): number {
    const times = this.metrics.get(name) || []
    return times.reduce((sum, time) => sum + time, 0) / times.length
  }
}

// Usage in components
const RouletteTable = () => {
  useEffect(() => {
    performanceMonitor.startMeasure('roulette-table-render')
    return () => {
      performanceMonitor.endMeasure('roulette-table-render')
    }
  })
  
  // Component logic...
}
```

## Testing Strategy

### Performance Tests
```typescript
// performance.test.ts
describe('Performance Optimizations', () => {
  it('renders large winner list within performance budget', () => {
    const largeWinnerList = generateMockWinners(1000)
    
    const startTime = performance.now()
    render(<VirtualizedWinners winners={largeWinnerList} height={400} itemHeight={50} />)
    const endTime = performance.now()
    
    expect(endTime - startTime).toBeLessThan(100) // 100ms budget
  })
  
  it('memoized components prevent unnecessary re-renders', () => {
    const renderSpy = jest.fn()
    const MemoizedComponent = React.memo(() => {
      renderSpy()
      return <div>Test</div>
    })
    
    const { rerender } = render(<MemoizedComponent prop="same" />)
    rerender(<MemoizedComponent prop="same" />)
    
    expect(renderSpy).toHaveBeenCalledTimes(1)
  })
})

// Bundle size tests
describe('Bundle Optimization', () => {
  it('lazy loads components correctly', async () => {
    const { findByTestId } = render(<LazyRouletteTable />)
    
    // Should show loading initially
    expect(screen.getByText(/loading/i)).toBeInTheDocument()
    
    // Should load component
    await findByTestId('roulette-table')
    expect(screen.getByTestId('roulette-table')).toBeInTheDocument()
  })
})
```

### Memory Leak Tests
```typescript
// memory.test.ts
describe('Memory Management', () => {
  it('cleans up event listeners on unmount', () => {
    const addEventListenerSpy = jest.spyOn(document, 'addEventListener')
    const removeEventListenerSpy = jest.spyOn(document, 'removeEventListener')
    
    const { unmount } = render(<RouletteTable />)
    
    expect(addEventListenerSpy).toHaveBeenCalled()
    
    unmount()
    
    expect(removeEventListenerSpy).toHaveBeenCalledTimes(
      addEventListenerSpy.mock.calls.length
    )
  })
})
```

## Risk Mitigation

### Potential Issues
1. **Over-optimization**: Making code complex for minimal gains
2. **Bundle Splitting**: Breaking functionality with lazy loading
3. **Memory Leaks**: Introducing new memory issues
4. **Performance Regression**: Optimizations causing slowdowns

### Mitigation Strategies
1. **Measure First**: Always benchmark before and after optimizations
2. **Gradual Implementation**: Implement optimizations incrementally
3. **Comprehensive Testing**: Test all optimization changes thoroughly
4. **Monitoring**: Continuous performance monitoring in production

### Rollback Procedure
```typescript
// Feature flags for performance optimizations
const USE_VIRTUALIZATION = import.meta.env.VITE_USE_VIRTUALIZATION !== 'false'
const USE_MEMOIZATION = import.meta.env.VITE_USE_MEMOIZATION !== 'false'
const USE_LAZY_LOADING = import.meta.env.VITE_USE_LAZY_LOADING !== 'false'

export const WinnersPanel = () => {
  return USE_VIRTUALIZATION ? (
    <VirtualizedWinners {...props} />
  ) : (
    <StandardWinners {...props} />
  )
}
```

## Success Criteria

### Quantitative Metrics
- [ ] Initial load time improved by 15%
- [ ] Bundle size reduced by 20%
- [ ] Memory usage reduced by 25%
- [ ] Re-render frequency reduced by 40%
- [ ] Mobile performance score >90 (Lighthouse)
- [ ] Core Web Vitals in "Good" range

### Qualitative Metrics
- [ ] Smoother animations and interactions
- [ ] Better mobile responsiveness
- [ ] Improved perceived performance
- [ ] Reduced memory pressure
- [ ] Better scalability for large datasets

### Acceptance Criteria
- [ ] All performance optimizations implemented
- [ ] No functional regressions introduced
- [ ] Performance benchmarks met or exceeded
- [ ] Memory leaks eliminated
- [ ] Bundle analysis shows improvements
- [ ] Mobile performance improved
- [ ] Performance monitoring in place
- [ ] Code review approval
- [ ] QA testing passed
