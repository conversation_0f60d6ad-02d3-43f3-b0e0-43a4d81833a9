import { createEnhancedStore, createSelector } from "@/middleware"
import { createStoreLogger } from "@/hooks/logging/use-stable-logger"

export type TokenData = {
  PlayerID: string
  OperatorID: number
  CurrencyCode: string
  BrandID: string
}

interface AuthStore {
  // State
  tokenData: TokenData | null
  token: string

  // Actions
  setTokenData: (tokenData: TokenData | null) => void
  setToken: (token: string) => void
  clearAuth: () => void
}

// Create a stable logger for this store
const storeLogger = createStoreLogger("AuthStore")

// Create the enhanced store
export const useAuthStore = createEnhancedStore<AuthStore>(
  (set) => ({
    // Initial state
    tokenData: null,
    token: "",

    // Actions
    setTokenData: (tokenData) =>
      set((state) => {
        state.tokenData = tokenData

        // Log token data update
        if (tokenData) {
          storeLogger.info("Token data updated", {
            playerID: tokenData.PlayerID,
            operatorID: tokenData.OperatorID,
            currencyCode: tokenData.CurrencyCode,
            brandID: tokenData.BrandID,
          })
        } else {
          storeLogger.info("Token data cleared")
        }
      }),

    setToken: (token) =>
      set((state) => {
        state.token = token

        // Log token update
        if (token) {
          storeLogger.info("Token updated")
        } else {
          storeLogger.info("Token cleared")
        }
      }),

    clearAuth: () =>
      set((state) => {
        state.token = ""
        state.tokenData = null

        // Log logout
        storeLogger.info("User logged out")
      }),
  }),
  {
    name: "enhanced-auth-storage",
    persist: true,
    secure: true,
    immer: true,
  }
)

// Create selectors for common state values
export const useToken = createSelector(useAuthStore, (state) => state.token)

export const useTokenData = createSelector(
  useAuthStore,
  (state) => state.tokenData
)

export const usePlayerInfo = createSelector(useAuthStore, (state) => ({
  playerId: state.tokenData?.PlayerID,
  operatorId: state.tokenData?.OperatorID,
  currencyCode: state.tokenData?.CurrencyCode,
  brandId: state.tokenData?.BrandID,
  isAuthenticated: !!state.token && !!state.tokenData,
}))
