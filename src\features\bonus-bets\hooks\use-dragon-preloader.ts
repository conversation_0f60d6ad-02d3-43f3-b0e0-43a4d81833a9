import { useEffect } from "react"
import { imageCache } from "@/lib/image-cache"
import { logger } from "@/middleware"
import { DRAGON_ASSETS } from "../constants/bonus-panel-styles"

// Track preload status to prevent repeated requests
let dragonImagesPreloaded = false

/**
 * Preloads dragon images to prevent repeated requests
 */
const preloadDragonImages = () => {
  if (dragonImagesPreloaded) return
  dragonImagesPreloaded = true

  // Use image cache to prevent redundant loading
  imageCache.preloadMultiple([...DRAGON_ASSETS]).catch((error) => {
    logger.warn("Failed to preload dragon images", {
      context: "BonusBetsPanel",
      data: { error: error.message },
    })
    // Reset flag to allow retry
    dragonImagesPreloaded = false
  })
}

/**
 * Hook to preload dragon images on component mount
 */
export const useDragonPreloader = () => {
  useEffect(() => {
    preloadDragonImages()
  }, [])
}
