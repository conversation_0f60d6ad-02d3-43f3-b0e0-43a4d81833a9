#!/usr/bin/env node

/**
 * Browser Opening Script
 * 
 * This script opens a URL in a specific browser based on the BROWSER environment variable.
 * It's used by the development scripts to ensure the correct browser is opened.
 */

import { spawn } from 'child_process'
import { platform } from 'os'

const BROWSER = process.env.BROWSER || 'chrome'
const URL = process.argv[2] || 'http://localhost:3000'

/**
 * Get the browser command based on the platform and browser name
 */
function getBrowserCommand(browser) {
  const os = platform()
  
  const browserCommands = {
    win32: {
      chrome: 'start chrome',
      firefox: 'start firefox',
      edge: 'start msedge',
      zen: 'start zen-browser',
      safari: 'start safari' // Not available on Windows, but included for completeness
    },
    darwin: {
      chrome: 'open -a "Google Chrome"',
      firefox: 'open -a "Firefox"',
      edge: 'open -a "Microsoft Edge"',
      zen: 'open -a "Zen Browser"',
      safari: 'open -a "Safari"'
    },
    linux: {
      chrome: 'google-chrome',
      firefox: 'firefox',
      edge: 'microsoft-edge',
      zen: 'zen-browser',
      safari: 'safari' // Not typically available on Linux
    }
  }
  
  const commands = browserCommands[os]
  if (!commands) {
    console.warn(`⚠️  Unsupported platform: ${os}. Using default browser.`)
    return null
  }
  
  const command = commands[browser.toLowerCase()]
  if (!command) {
    console.warn(`⚠️  Unsupported browser: ${browser}. Available browsers: ${Object.keys(commands).join(', ')}`)
    return commands.chrome || Object.values(commands)[0] // Fallback to chrome or first available
  }
  
  return command
}

/**
 * Open the URL in the specified browser
 */
function openBrowser(browser, url) {
  const command = getBrowserCommand(browser)
  
  if (!command) {
    console.log(`🌐 Opening ${url} in default browser...`)
    // Fallback to system default
    const os = platform()
    const fallbackCommand = os === 'win32' ? `start ${url}` : 
                           os === 'darwin' ? `open ${url}` : 
                           `xdg-open ${url}`
    
    spawn(fallbackCommand, { shell: true, detached: true, stdio: 'ignore' })
    return
  }
  
  console.log(`🚀 Opening ${url} in ${browser}...`)
  
  const fullCommand = `${command} ${url}`
  spawn(fullCommand, { shell: true, detached: true, stdio: 'ignore' })
}

// Main execution
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log(`
🌐 Browser Opening Script

Usage:
  node scripts/open-browser.js [url]

Environment Variables:
  BROWSER    Browser to open (chrome, firefox, edge, zen, safari)
             Default: chrome

Examples:
  BROWSER=firefox node scripts/open-browser.js http://localhost:3000
  BROWSER=edge node scripts/open-browser.js
  
Supported Browsers:
  • chrome    - Google Chrome
  • firefox   - Mozilla Firefox  
  • edge      - Microsoft Edge
  • zen       - Zen Browser
  • safari    - Safari (macOS only)
`)
  process.exit(0)
}

openBrowser(BROWSER, URL)
