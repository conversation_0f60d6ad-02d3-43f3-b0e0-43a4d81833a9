import { useApiQuery } from "@/middleware"
import { useAuth } from "../auth"
import { PlayerBetHistory, BetHistoryNav } from "./use-api"

// Constants
const ENDPOINTS = {
  GET_PLAYER_HISTORY: import.meta.env.VITE_APP_GET_PLAYER_HISTORY,
  GET_PLAYER_HISTORY_NAV: import.meta.env.VITE_APP_GET_PLAYER_HISTORY_NAV,
}

/**
 * Custom hook to fetch player bet history using React Query
 */
export const usePlayerBetHistoryQuery = (
  playerGuid: string,
  enabled: boolean = true
) => {
  const { token, isAuthenticated } = useAuth()

  return useApiQuery<PlayerBetHistory[]>(
    ["playerBetHistory", playerGuid],
    `${ENDPOINTS.GET_PLAYER_HISTORY}?playerGuid=${playerGuid}`,
    null,
    token ? { Authorization: `Bearer ${token}` } : undefined,
    {
      enabled: !!token && isAuthenticated() && !!playerGuid && enabled,
      staleTime: 1000 * 60, // 1 minute
      refetchInterval: 1000 * 60 * 5, // 5 minutes
      refetchOnWindowFocus: true,
      showErrorToast: true,
      errorToastTitle: "Failed to Load Bet History",
    }
  )
}

/**
 * Custom hook to fetch player bet history navigation using React Query
 */
export const usePlayerBetHistoryNavQuery = (
  playerGuid: string,
  enabled: boolean = true
) => {
  const { token, isAuthenticated } = useAuth()

  return useApiQuery<BetHistoryNav[] | string>(
    ["playerBetHistoryNav", playerGuid],
    `${ENDPOINTS.GET_PLAYER_HISTORY_NAV}?playerGuid=${playerGuid}`,
    null,
    token ? { Authorization: `Bearer ${token}` } : undefined,
    {
      enabled: !!token && isAuthenticated() && !!playerGuid && enabled,
      staleTime: 1000 * 60, // 1 minute
      refetchInterval: 1000 * 60 * 5, // 5 minutes
      refetchOnWindowFocus: true,
      showErrorToast: false,
      select: (data) => {
        // Handle string response (error message)
        if (typeof data === "string") {
          if (data.includes("No history")) {
            return []
          }
          return []
        }

        // Handle array response
        if (Array.isArray(data)) {
          return data
        }

        return []
      },
    }
  )
}
