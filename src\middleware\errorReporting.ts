import { logger } from "@/lib/logger"
import { apiClient } from "./api/apiClient"

// Types for error reporting
interface ErrorReport {
  message: string
  stack?: string
  context?: string
  metadata?: Record<string, unknown>
  timestamp: string
  userAgent: string
  url: string
}

// Error reporting configuration
interface ErrorReportingConfig {
  enabled: boolean
  endpoint: string
  batchSize: number
  flushInterval: number
  sampleRate: number
}

// Default configuration
const DEFAULT_CONFIG: ErrorReportingConfig = {
  enabled: import.meta.env.PROD, // Only enabled in production by default
  endpoint: import.meta.env.VITE_ERROR_REPORTING_ENDPOINT || "/api/errors",
  batchSize: 10,
  flushInterval: 30000, // 30 seconds
  sampleRate: 1.0, // Report 100% of errors
}

/**
 * Create an error reporting service
 */
export const createErrorReporting = (
  config: Partial<ErrorReportingConfig> = {}
) => {
  // Merge default config with provided config
  const mergedConfig: ErrorReportingConfig = {
    ...DEFAULT_CONFIG,
    ...config,
  }

  // Error queue
  const errorQueue: ErrorReport[] = []

  // Interval ID for automatic flushing
  let flushIntervalId: number | null = null

  // Initialize the service
  const initialize = () => {
    if (!mergedConfig.enabled) {
      logger.info("Error reporting is disabled", { context: "ErrorReporting" })
      return
    }

    // Set up automatic flushing
    if (flushIntervalId) {
      clearInterval(flushIntervalId)
    }

    flushIntervalId = window.setInterval(() => {
      if (errorQueue.length > 0) {
        flush()
      }
    }, mergedConfig.flushInterval) as unknown as number

    logger.info("Error reporting initialized", {
      context: "ErrorReporting",
      data: {
        endpoint: mergedConfig.endpoint,
        batchSize: mergedConfig.batchSize,
        flushInterval: mergedConfig.flushInterval,
        sampleRate: mergedConfig.sampleRate,
      },
    })

    // Set up global error handler
    window.addEventListener("error", handleGlobalError)
    window.addEventListener("unhandledrejection", handleUnhandledRejection)
  }

  // Handle global errors
  const handleGlobalError = (event: ErrorEvent) => {
    reportError(event.error || new Error(event.message), "GlobalError")
  }

  // Handle unhandled promise rejections
  const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
    const error =
      event.reason instanceof Error
        ? event.reason
        : new Error(String(event.reason))

    reportError(error, "UnhandledRejection")
  }

  // Report an error
  const reportError = (
    error: Error | string,
    context?: string,
    metadata?: Record<string, unknown>
  ) => {
    if (!mergedConfig.enabled) return

    // Apply sampling
    if (Math.random() > mergedConfig.sampleRate) return

    // Create error report
    const errorReport: ErrorReport = {
      message: error instanceof Error ? error.message : error,
      stack: error instanceof Error ? error.stack : undefined,
      context,
      metadata,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
    }

    // Add to queue
    errorQueue.push(errorReport)

    // Flush if queue is full
    if (errorQueue.length >= mergedConfig.batchSize) {
      flush()
    }
  }

  // Flush the error queue
  const flush = async () => {
    if (errorQueue.length === 0) return

    // Clone and clear the queue
    const errors = [...errorQueue]
    errorQueue.length = 0

    try {
      // Send errors to the backend
      const [response, error] = await apiClient.post(mergedConfig.endpoint, {
        errors,
      })

      if (error) {
        logger.error("Failed to send error reports", error, {
          context: "ErrorReporting",
        })

        // Put errors back in the queue if sending failed
        errorQueue.push(...errors)
      } else {
        logger.debug(`Sent ${errors.length} error reports`, {
          context: "ErrorReporting",
        })
      }
    } catch (error) {
      logger.error("Exception sending error reports", error, {
        context: "ErrorReporting",
      })

      // Put errors back in the queue
      errorQueue.push(...errors)
    }
  }

  // Clean up
  const shutdown = () => {
    if (flushIntervalId) {
      clearInterval(flushIntervalId)
      flushIntervalId = null
    }

    // Remove event listeners
    window.removeEventListener("error", handleGlobalError)
    window.removeEventListener("unhandledrejection", handleUnhandledRejection)

    // Flush remaining errors
    flush()
  }

  return {
    initialize,
    reportError,
    flush,
    shutdown,
  }
}

// Create a default error reporting instance
export const errorReporting = createErrorReporting()
