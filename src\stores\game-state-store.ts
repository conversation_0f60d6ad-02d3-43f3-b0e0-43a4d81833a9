import { History, RouletteRound } from "@/hooks"
import { createEnhancedStore, createSelector, logger } from "@/middleware"

export type GameType = "special" | "normal"

interface GameStateStore {
  // State
  roundData: RouletteRound | null
  gameType: GameType
  currencyCode: string
  isSessionActive: boolean

  // Actions
  setRoundData: (roundData: RouletteRound | null) => void
  setGameType: (gameType: GameType) => void
  setCurrencyCode: (currencyCode: string) => void
  setSessionActive: (active: boolean) => void

  // Past results
  showHotCold: boolean
  setShowHotCold: (show: boolean) => void
  showPastNumbers: boolean
  setShowPastNumbers: (show: boolean) => void
}

// Create the enhanced store
export const useGameStateStore = createEnhancedStore<GameStateStore>(
  (set) => ({
    // Initial state
    roundData: null,
    gameType: "special",
    currencyCode: "FUN",
    isSessionActive: true,
    showPastNumbers: false,
    showHotCold: false,

    // Actions
    setShowPastNumbers: (show) =>
      set((state) => {
        state.showPastNumbers = show
      }),

    setShowHotCold: (show) =>
      set((state) => {
        state.showHotCold = show
      }),

    setRoundData: (roundData) =>
      set((state) => {
        state.roundData = roundData

        // Log round data update
        if (roundData) {
          logger.debug("Round data updated", {
            context: "GameState",
            data: {
              rouletteDrawId: roundData.rouletteDrawId,
              rouletteNumber: roundData.rouletteNumber,
              bonusNumber: roundData.bonusNumber,
              drawStatusId: roundData.drawStatusId,
            },
          })
        }
      }),

    setGameType: (gameType) =>
      set((state) => {
        state.gameType = gameType

        // Log game type change
        logger.debug("Game type changed", {
          context: "GameState",
          data: { gameType },
        })
      }),

    setCurrencyCode: (currencyCode) =>
      set((state) => {
        state.currencyCode = currencyCode

        // Log currency code change
        logger.debug("Currency code changed", {
          context: "GameState",
          data: { currencyCode },
        })
      }),

    setSessionActive: (active) =>
      set((state) => {
        state.isSessionActive = active

        // Log session state change
        logger.info(`Session ${active ? "activated" : "deactivated"}`, {
          context: "GameState",
        })
      }),
  }),
  {
    name: "enhanced-game-state-storage",
    persist: true,
    secure: true,
    immer: true,
  }
)

// Create selectors for common state values
export const useRoundData = createSelector(
  useGameStateStore,
  (state) => state.roundData
)

export const useGameType = createSelector(
  useGameStateStore,
  (state) => state.gameType
)

export const useCurrencyCode = createSelector(
  useGameStateStore,
  (state) => state.currencyCode
)

export const useIsSessionActive = createSelector(
  useGameStateStore,
  (state) => state.isSessionActive
)
