import { useApiQuery } from "@/middleware"
import { useBettingStore } from "@/stores/betting-store"
import { BetTypes } from "./use-api"

// Constants
const ENDPOINTS = {
  GET_BET_TYPES: import.meta.env.VITE_APP_GET_BET_TYPES,
}

/**
 * Custom hook to fetch bet types using React Query
 */
export const useBetTypesQuery = () => {
  const setBetTypes = useBettingStore(
    (state) => (betTypes: BetTypes[] | null) => {
      state.betTypes = betTypes
    }
  )

  const query = useApiQuery<BetTypes[]>(
    ["betTypes"],
    ENDPOINTS.GET_BET_TYPES,
    null,
    undefined,
    {
      staleTime: 1000 * 60 * 60, // 1 hour
      gcTime: 1000 * 60 * 60 * 24, // 24 hours
      retry: 3,
      showErrorToast: true,
      errorToastTitle: "Failed to Load Bet Types",
      onSuccess: (data: BetTypes[]) => {
        // Update the store with the fetched data
        if (data) {
          setBetTypes(data)
        }
      },
    }
  )

  return query
}

/**
 * Custom hook to fetch bet types by group
 */
export const useBetTypesByGroup = (groupId: number) => {
  const { data: allBetTypes, ...rest } = useBetTypesQuery()

  // Filter bet types by group ID
  const betTypesByGroup =
    allBetTypes?.filter((betType) => betType.betGroupId === groupId) || []

  return {
    ...rest,
    data: betTypesByGroup,
  }
}

/**
 * Custom hook to get a specific bet type by ID
 */
export const useBetTypeById = (betTypeId: number | undefined) => {
  const { data: allBetTypes, ...rest } = useBetTypesQuery()

  // Find bet type by ID
  const betType = betTypeId
    ? allBetTypes?.find((bt) => bt.betTypeId === betTypeId)
    : undefined

  return {
    ...rest,
    data: betType,
  }
}

/**
 * Custom hook to get bet types by multiple IDs
 */
export const useBetTypesByIds = (betTypeIds: number[]) => {
  const { data: allBetTypes, ...rest } = useBetTypesQuery()

  // Filter bet types by IDs
  const betTypes =
    allBetTypes?.filter((betType) => betTypeIds.includes(betType.betTypeId)) ||
    []

  return {
    ...rest,
    data: betTypes,
  }
}
